# دليل المستخدم - نظام إدارة ديون العملاء

## 🚀 البدء السريع

### تشغيل النظام
1. **Windows**: انقر نقراً مزدوجاً على `start.bat`
2. **Linux/Mac**: شغل `bash start.sh` في الطرفية
3. سيفتح النظام تلقائياً في المتصفح على `http://localhost:5000`

## 📋 الواجهة الرئيسية

### 1. لوحة الإحصائيات (أعلى الصفحة)
- **إجمالي المبالغ**: مجموع جميع الديون
- **عدد العملاء**: العملاء الفريدين (بناءً على الاسم والمدينة)
- **عدد الفواتير**: إجمالي الفواتير المسجلة

### 2. نموذج إضافة دين جديد (يسار الصفحة)
#### الحقول المطلوبة:
- **رقم الفاتورة**: رقم فريد للفاتورة
- **اسم العميل**: اسم العميل كاملاً
- **المدينة**: مدينة العميل
- **مبلغ الدين**: المبلغ بالريال السعودي
- **الملاحظات**: ملاحظات إضافية (اختياري)

#### ميزات خاصة:
- **عرض السجل السابق**: عند إدخال اسم العميل والمدينة، سيظهر سجله السابق تلقائياً
- **منع التكرار**: لا يمكن إدخال رقم فاتورة مكرر
- **التحقق من البيانات**: التأكد من صحة البيانات قبل الحفظ

### 3. مربع البحث
- **البحث الشامل**: ابحث في جميع الحقول
- **البحث المتقدم**: يمكن البحث بـ:
  - رقم الفاتورة
  - اسم العميل
  - المدينة
  - المبلغ
  - الملاحظات
  - التاريخ

### 4. جدول السجلات (يمين الصفحة)
- **عرض جميع السجلات**: مرتبة حسب التاريخ (الأحدث أولاً)
- **أزرار الإجراءات**: حذف السجلات
- **تصميم متجاوب**: يعمل على جميع الأجهزة

## 🔧 الميزات المتقدمة

### سجل العميل التلقائي
عند إدخال اسم العميل والمدينة:
- يظهر سجل العميل السابق في مربع منفصل
- يعرض إجمالي ديون العميل
- يعرض عدد الفواتير السابقة
- يعرض تفاصيل كل فاتورة سابقة

### نظام البحث الذكي
- **البحث الفوري**: النتائج تظهر أثناء الكتابة
- **البحث متعدد الحقول**: ابحث في أي حقل
- **تمييز النتائج**: النتائج المطابقة تظهر بوضوح

### الإحصائيات المباشرة
- **تحديث فوري**: الإحصائيات تتحدث مع كل إضافة/حذف
- **حساب العملاء الفريدين**: بناءً على تطابق الاسم والمدينة
- **إجماليات دقيقة**: حسابات دقيقة للمبالغ

## 📊 إدارة البيانات

### حفظ البيانات
- **حفظ تلقائي**: البيانات تحفظ فوراً في قاعدة البيانات
- **نسخ احتياطية**: نسخة احتياطية تلقائية قبل كل تعديل
- **تسجيل التاريخ**: تاريخ ووقت الإدخال يسجل تلقائياً

### أمان البيانات
- **ملف JSON**: البيانات محفوظة في `data/customers.json`
- **ترميز UTF-8**: دعم كامل للنصوص العربية
- **نسخ احتياطية**: ملفات `.bak` تنشأ تلقائياً

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. النظام لا يفتح
**المشكلة**: رسالة خطأ عند تشغيل `start.bat`
**الحل**:
- تأكد من تثبيت Python 3.7+
- تحقق من الاتصال بالإنترنت لتثبيت المتطلبات
- شغل `python --version` للتأكد من Python

#### 2. البيانات لا تظهر
**المشكلة**: الجدول فارغ رغم إدخال البيانات
**الحل**:
- تحقق من ملف `data/customers.json`
- تأكد من عدم وجود أخطاء في وحدة تحكم المتصفح (F12)
- أعد تشغيل الخادم

#### 3. خطأ في إضافة البيانات
**المشكلة**: رسالة خطأ عند الحفظ
**الحل**:
- تأكد من ملء جميع الحقول المطلوبة
- تحقق من أن رقم الفاتورة غير مكرر
- تأكد من أن المبلغ رقم صحيح

#### 4. البحث لا يعمل
**المشكلة**: لا تظهر نتائج البحث
**الحل**:
- تأكد من وجود بيانات في النظام
- جرب البحث بكلمات مختلفة
- امسح مربع البحث وأعد المحاولة

### رسائل الخطأ الشائعة

| رسالة الخطأ | السبب | الحل |
|-------------|--------|------|
| "رقم الفاتورة موجود مسبقاً" | رقم فاتورة مكرر | استخدم رقم فاتورة مختلف |
| "يرجى إدخال مبلغ صحيح" | مبلغ غير صالح | أدخل رقماً صحيحاً |
| "Python غير مثبت" | Python غير موجود | ثبت Python من python.org |
| "خطأ في الاتصال" | الخادم متوقف | أعد تشغيل start.bat |

## 📱 استخدام النظام على الأجهزة المختلفة

### الحاسوب المكتبي
- **الشاشة الكاملة**: استخدم النظام بأقصى كفاءة
- **اختصارات لوحة المفاتيح**: Tab للتنقل بين الحقول
- **النسخ واللصق**: يعمل في جميع الحقول

### الجهاز اللوحي
- **تصميم متجاوب**: الواجهة تتكيف مع حجم الشاشة
- **اللمس**: جميع الأزرار محسنة للمس
- **التمرير**: سهولة التمرير في الجداول الطويلة

### الهاتف المحمول
- **واجهة مبسطة**: الحقول تترتب عمودياً
- **أزرار كبيرة**: سهولة الضغط
- **لوحة مفاتيح ذكية**: تظهر لوحة الأرقام للحقول الرقمية

## 🔄 النسخ الاحتياطي والاستعادة

### إنشاء نسخة احتياطية يدوية
1. انسخ مجلد `data` بالكامل
2. احفظه في مكان آمن
3. أضف التاريخ لاسم المجلد

### استعادة البيانات
1. أوقف النظام
2. استبدل مجلد `data` بالنسخة الاحتياطية
3. أعد تشغيل النظام

### تصدير البيانات
- استخدم المتصفح للوصول إلى `/api/export`
- احفظ الملف الناتج كنسخة احتياطية
- يمكن استيراده لاحقاً باستخدام `/api/import`

## 📞 الدعم والمساعدة

### للحصول على المساعدة:
1. راجع هذا الدليل أولاً
2. تحقق من قسم استكشاف الأخطاء
3. شغل `python test_system.py` لاختبار النظام
4. تحقق من ملفات السجل في وحدة تحكم المتصفح

### معلومات النظام:
- **الإصدار**: 1.0.0
- **التقنيات**: Python Flask, HTML5, CSS3, JavaScript
- **المتصفحات المدعومة**: Chrome, Firefox, Safari, Edge
- **نظام التشغيل**: Windows, Linux, macOS

---

**🎉 نتمنى لك تجربة ممتعة مع نظام إدارة ديون العملاء!**
