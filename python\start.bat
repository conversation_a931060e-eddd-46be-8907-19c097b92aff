@echo off
echo ========================================
echo    نظام إدارة ديون العملاء
echo ========================================
echo.

echo 🔍 التحقق من Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo ✅ Python موجود

echo.
echo 📦 تثبيت المتطلبات...
pip install -r requirements.txt >nul 2>&1
if errorlevel 1 (
    echo ⚠️  تحذير: قد تكون هناك مشكلة في تثبيت المتطلبات
)

echo.
echo 🚀 بدء تشغيل الخادم...
echo 🌐 سيتم فتح النظام على: http://localhost:5000
echo 📊 للإيقاف اضغط Ctrl+C
echo.

python app.py
