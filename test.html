<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام</title>
    
    <!-- Libraries for PDF and Excel -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
    
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>اختبار نظام إدارة ديون العملاء</h1>
    
    <div class="test-section">
        <h2>1. اختبار تحميل المكتبات</h2>
        <button onclick="testLibraries()">اختبار المكتبات</button>
        <div id="libraryStatus"></div>
    </div>
    
    <div class="test-section">
        <h2>2. اختبار إنشاء PDF</h2>
        <button onclick="testPDF()">إنشاء PDF تجريبي</button>
        <div id="pdfStatus"></div>
    </div>
    
    <div class="test-section">
        <h2>3. اختبار إنشاء Excel</h2>
        <button onclick="testExcel()">إنشاء Excel تجريبي</button>
        <div id="excelStatus"></div>
    </div>
    
    <div class="test-section">
        <h2>4. اختبار البيانات التجريبية</h2>
        <button onclick="createTestData()">إنشاء بيانات تجريبية</button>
        <button onclick="testWithData()">اختبار مع البيانات</button>
        <div id="dataStatus"></div>
    </div>

    <script>
        // Test data
        let testData = [];
        
        // Mock debtManager for testing
        window.debtManager = {
            debts: [],
            showSuccess: function(msg) {
                console.log('Success:', msg);
                alert('نجح: ' + msg);
            },
            showError: function(msg) {
                console.log('Error:', msg);
                alert('خطأ: ' + msg);
            }
        };
        
        function showStatus(elementId, message, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${isSuccess ? 'success' : 'error'}">${message}</div>`;
        }
        
        function testLibraries() {
            let status = '';
            let allGood = true;
            
            // Test jsPDF
            if (window.jspdf) {
                status += '✅ jsPDF محمل بنجاح<br>';
            } else {
                status += '❌ jsPDF غير محمل<br>';
                allGood = false;
            }
            
            // Test XLSX
            if (window.XLSX) {
                status += '✅ XLSX محمل بنجاح<br>';
            } else {
                status += '❌ XLSX غير محمل<br>';
                allGood = false;
            }
            
            // Test autoTable
            if (window.jspdf && window.jspdf.jsPDF.prototype.autoTable) {
                status += '✅ autoTable محمل بنجاح<br>';
            } else {
                status += '❌ autoTable غير محمل<br>';
                allGood = false;
            }
            
            showStatus('libraryStatus', status, allGood);
        }
        
        function testPDF() {
            try {
                if (!window.jspdf) {
                    throw new Error('jsPDF غير متوفر');
                }
                
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF();
                
                // Add title
                doc.setFontSize(18);
                doc.text('Test PDF Report', 105, 20, { align: 'center' });
                
                // Add date
                doc.setFontSize(12);
                doc.text(`Date: ${new Date().toLocaleDateString()}`, 20, 40);
                
                // Add test table
                const testTableData = [
                    ['1', 'INV-001', 'Ahmed Mohamed', 'Riyadh', '1500 SAR', 'Test invoice'],
                    ['2', 'INV-002', 'Fatima Ali', 'Jeddah', '2000 SAR', 'Another test']
                ];
                
                doc.autoTable({
                    head: [['#', 'Invoice', 'Customer', 'City', 'Amount', 'Notes']],
                    body: testTableData,
                    startY: 50
                });
                
                // Save PDF
                doc.save('test_report.pdf');
                
                showStatus('pdfStatus', '✅ تم إنشاء PDF بنجاح!', true);
                
            } catch (error) {
                showStatus('pdfStatus', `❌ خطأ في إنشاء PDF: ${error.message}`, false);
            }
        }
        
        function testExcel() {
            try {
                if (!window.XLSX) {
                    throw new Error('XLSX غير متوفر');
                }
                
                // Create test data
                const testData = [
                    { 'رقم الفاتورة': 'INV-001', 'اسم العميل': 'أحمد محمد', 'المدينة': 'الرياض', 'المبلغ': 1500 },
                    { 'رقم الفاتورة': 'INV-002', 'اسم العميل': 'فاطمة علي', 'المدينة': 'جدة', 'المبلغ': 2000 }
                ];
                
                // Create workbook
                const wb = XLSX.utils.book_new();
                const ws = XLSX.utils.json_to_sheet(testData);
                
                // Add worksheet
                XLSX.utils.book_append_sheet(wb, ws, 'بيانات تجريبية');
                
                // Save file
                XLSX.writeFile(wb, 'test_data.xlsx');
                
                showStatus('excelStatus', '✅ تم إنشاء Excel بنجاح!', true);
                
            } catch (error) {
                showStatus('excelStatus', `❌ خطأ في إنشاء Excel: ${error.message}`, false);
            }
        }
        
        function createTestData() {
            testData = [
                {
                    id: 1,
                    invoiceNumber: 'INV-001',
                    customerName: 'أحمد محمد',
                    city: 'الرياض',
                    amount: 1500,
                    notes: 'فاتورة تجريبية',
                    date: new Date().toLocaleDateString('ar-SA'),
                    timestamp: new Date().toISOString()
                },
                {
                    id: 2,
                    invoiceNumber: 'INV-002',
                    customerName: 'فاطمة علي',
                    city: 'جدة',
                    amount: 2000,
                    notes: 'فاتورة أخرى',
                    date: new Date().toLocaleDateString('ar-SA'),
                    timestamp: new Date().toISOString()
                }
            ];
            
            // Update mock debtManager
            window.debtManager.debts = testData;
            
            showStatus('dataStatus', '✅ تم إنشاء بيانات تجريبية بنجاح!', true);
        }
        
        function testWithData() {
            if (testData.length === 0) {
                showStatus('dataStatus', '❌ لا توجد بيانات تجريبية. اضغط على "إنشاء بيانات تجريبية" أولاً', false);
                return;
            }
            
            try {
                // Test PDF with data
                testPDFWithData();
                
                // Test Excel with data
                testExcelWithData();
                
                showStatus('dataStatus', '✅ تم اختبار جميع الوظائف مع البيانات بنجاح!', true);
                
            } catch (error) {
                showStatus('dataStatus', `❌ خطأ في الاختبار: ${error.message}`, false);
            }
        }
        
        function testPDFWithData() {
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();
            
            doc.setFontSize(18);
            doc.text('Test Report with Data', 105, 20, { align: 'center' });
            
            const tableData = testData.map((item, index) => [
                (index + 1).toString(),
                item.invoiceNumber,
                item.customerName,
                item.city,
                item.amount.toLocaleString() + ' SAR',
                item.notes
            ]);
            
            doc.autoTable({
                head: [['#', 'Invoice', 'Customer', 'City', 'Amount', 'Notes']],
                body: tableData,
                startY: 40
            });
            
            doc.save('test_with_data.pdf');
        }
        
        function testExcelWithData() {
            const wb = XLSX.utils.book_new();
            
            const excelData = testData.map((item, index) => ({
                'الرقم': index + 1,
                'رقم الفاتورة': item.invoiceNumber,
                'اسم العميل': item.customerName,
                'المدينة': item.city,
                'المبلغ': item.amount,
                'الملاحظات': item.notes
            }));
            
            const ws = XLSX.utils.json_to_sheet(excelData);
            XLSX.utils.book_append_sheet(wb, ws, 'البيانات');
            
            XLSX.writeFile(wb, 'test_with_data.xlsx');
        }
        
        // Auto-test libraries on load
        window.addEventListener('load', () => {
            setTimeout(testLibraries, 1000);
        });
    </script>
</body>
</html>
