# نظام إدارة ديون العملاء
Customer Debt Management System

نظام شامل لإدارة ديون العملاء مع واجهة عربية متقدمة وميزات التصدير والاستيراد.

## 🚀 بدء سريع

### Windows (الطريقة الأسرع)
```bash
# تشغيل فوري - كل شيء تلقائي
quick-start.bat
```

### طرق أخرى
```bash
# Windows التقليدي
start.bat

# Linux/Mac
chmod +x start.sh && ./start.sh

# تشغيل يدوي
cd python && pip install -r requirements.txt && python app.py
```

## ✨ الميزات الرئيسية

### 📊 إدارة البيانات
- ✅ إضافة فواتير جديدة مع منع التكرار
- ✅ عرض الديون السابقة (مرجعية)
- ✅ بحث شامل فوري
- ✅ حذف وتعديل السجلات

### 📈 التصدير والاستيراد
- ✅ PDF عربي احترافي
- ✅ CSV مع دعم UTF-8
- ✅ Excel متقدم مع إحصائيات
- ✅ استيراد ذكي من CSV/Excel

### 📋 التحليلات
- ✅ إحصائيات فورية
- ✅ مقارنات تفصيلية
- ✅ تحليل العملاء
- ✅ تقارير شاملة

### 🎨 الواجهة
- ✅ عربية كاملة (RTL)
- ✅ متجاوبة 100%
- ✅ إشعارات ذكية
- ✅ تصميم عصري

## 📋 متطلبات النظام

- **Python 3.7+** (يتم فحصه تلقائياً)
- **متصفح حديث** (Chrome, Firefox, Edge, Safari)
- **50 MB مساحة** فارغة

## هيكل المشروع

```
الديون/
├── html/
│   └── index.html          # الواجهة الرئيسية
├── css/
│   └── style.css           # ملفات التنسيق
├── javascript/
│   └── app.js              # منطق الواجهة الأمامية
├── python/
│   ├── app.py              # خادم Flask الرئيسي
│   ├── database.py         # مدير قاعدة البيانات
│   └── requirements.txt    # متطلبات Python
├── data/
│   └── customers.json      # ملف البيانات
└── README.md               # هذا الملف
```

## التثبيت والتشغيل

### 1. تثبيت Python
تأكد من تثبيت Python 3.7 أو أحدث على نظامك.

### 2. تثبيت المتطلبات
```bash
cd python
pip install -r requirements.txt
```

### 3. تشغيل الخادم
```bash
cd python
python app.py
```

### 4. فتح التطبيق
افتح المتصفح وانتقل إلى: `http://localhost:5000`

## كيفية الاستخدام

### إضافة دين جديد
1. املأ جميع الحقول المطلوبة في النموذج
2. سيظهر سجل العميل السابق تلقائياً عند إدخال الاسم والمدينة
3. اضغط "حفظ البيانات"

### البحث في السجلات
- استخدم مربع البحث للبحث في جميع الحقول
- يمكن البحث برقم الفاتورة، اسم العميل، المدينة، المبلغ، أو الملاحظات

### عرض الإحصائيات
- إجمالي المبالغ
- عدد العملاء (بناءً على تطابق الاسم والمدينة)
- عدد الفواتير

## API Endpoints

### GET /api/debts
الحصول على جميع السجلات

### POST /api/debts
إضافة سجل جديد

### DELETE /api/debts/{id}
حذف سجل محدد

### GET /api/debts/customer/{name}/{city}
الحصول على سجلات عميل محدد

### GET /api/debts/search?q={term}
البحث في السجلات

### GET /api/statistics
الحصول على الإحصائيات

### GET /api/export
تصدير البيانات

### POST /api/import
استيراد البيانات

## الأمان والنسخ الاحتياطية

- يتم إنشاء نسخة احتياطية تلقائياً قبل كل عملية حفظ
- يتم حفظ البيانات في ملف JSON مع ترميز UTF-8
- حماية من تكرار أرقام الفواتير

## المتطلبات التقنية

### Python Packages
- Flask 2.3.3
- Flask-CORS 4.0.0
- Werkzeug 2.3.7

### Browser Support
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## الاستكشاف وإصلاح الأخطاء

### مشاكل شائعة

1. **خطأ في تشغيل الخادم**
   - تأكد من تثبيت جميع المتطلبات
   - تحقق من أن المنفذ 5000 غير مستخدم

2. **عدم ظهور البيانات**
   - تحقق من وجود ملف `data/customers.json`
   - تأكد من صحة تنسيق JSON

3. **مشاكل في الواجهة**
   - تأكد من تحميل ملفات CSS و JavaScript
   - تحقق من وحدة تحكم المتصفح للأخطاء

## التطوير المستقبلي

- [ ] إضافة قاعدة بيانات SQL
- [ ] نظام المستخدمين والصلاحيات
- [ ] تقارير مفصلة
- [ ] تصدير إلى Excel/PDF
- [ ] إشعارات الاستحقاق
- [ ] واجهة برمجة تطبيقات محسنة

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إجراء التغييرات
4. إرسال Pull Request

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتجاري.

## الدعم

للدعم والاستفسارات، يرجى إنشاء Issue في المستودع.

---

**تم تطوير هذا النظام بعناية لتلبية احتياجات إدارة ديون العملاء بكفاءة وسهولة.**
