const { contextBridge, ipcRenderer } = require('electron');

// تعريض APIs آمنة للتطبيق الرئيسي
contextBridge.exposeInMainWorld('electronAPI', {
    // معلومات النظام
    platform: process.platform,
    version: process.versions.electron,
    
    // التعامل مع الملفات
    onFileOpened: (callback) => {
        ipcRenderer.on('file-opened', (event, filePath) => {
            callback(filePath);
        });
    },
    
    // التعامل مع أحداث القائمة
    onMenuAction: (callback) => {
        ipcRenderer.on('menu-action', (event, action) => {
            callback(action);
        });
    },
    
    // إرسال إشعارات للنافذة الرئيسية
    sendNotification: (title, body) => {
        ipcRenderer.send('show-notification', { title, body });
    },
    
    // حفظ البيانات
    saveData: (data) => {
        ipcRenderer.send('save-data', data);
    },
    
    // تحميل البيانات
    loadData: () => {
        return ipcRenderer.invoke('load-data');
    },
    
    // تصدير البيانات
    exportData: (data, format) => {
        ipcRenderer.send('export-data', { data, format });
    },
    
    // طباعة
    print: () => {
        ipcRenderer.send('print');
    },
    
    // إظهار حوار حفظ الملف
    showSaveDialog: (options) => {
        return ipcRenderer.invoke('show-save-dialog', options);
    },
    
    // إظهار حوار فتح الملف
    showOpenDialog: (options) => {
        return ipcRenderer.invoke('show-open-dialog', options);
    },
    
    // إظهار رسالة
    showMessage: (options) => {
        return ipcRenderer.invoke('show-message', options);
    },
    
    // فتح رابط خارجي
    openExternal: (url) => {
        ipcRenderer.send('open-external', url);
    },
    
    // الحصول على مسار التطبيق
    getAppPath: () => {
        return ipcRenderer.invoke('get-app-path');
    },
    
    // التحقق من وجود الملف
    fileExists: (path) => {
        return ipcRenderer.invoke('file-exists', path);
    },
    
    // قراءة ملف
    readFile: (path) => {
        return ipcRenderer.invoke('read-file', path);
    },
    
    // كتابة ملف
    writeFile: (path, data) => {
        return ipcRenderer.invoke('write-file', path, data);
    }
});

// إضافة معلومات التطبيق
contextBridge.exposeInMainWorld('appInfo', {
    name: 'نظام إدارة ديون العملاء',
    version: '1.0.0',
    isElectron: true,
    platform: process.platform,
    arch: process.arch
});

// إضافة وظائف مساعدة
contextBridge.exposeInMainWorld('utils', {
    // تنسيق التاريخ
    formatDate: (date) => {
        return new Date(date).toLocaleDateString('ar-SA');
    },
    
    // تنسيق الأرقام
    formatNumber: (number) => {
        return new Intl.NumberFormat('ar-SA').format(number);
    },
    
    // تنسيق العملة
    formatCurrency: (amount) => {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR'
        }).format(amount);
    },
    
    // التحقق من صحة البريد الإلكتروني
    isValidEmail: (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },
    
    // إنشاء معرف فريد
    generateId: () => {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    },
    
    // تنظيف النص العربي
    cleanArabicText: (text) => {
        if (!text) return '';
        return text.toString().trim().replace(/\s+/g, ' ');
    }
});

// إضافة دعم للتخزين المحلي المحسن
contextBridge.exposeInMainWorld('storage', {
    // حفظ البيانات
    setItem: (key, value) => {
        try {
            const data = JSON.stringify(value);
            localStorage.setItem(key, data);
            return true;
        } catch (error) {
            console.error('خطأ في حفظ البيانات:', error);
            return false;
        }
    },
    
    // تحميل البيانات
    getItem: (key, defaultValue = null) => {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : defaultValue;
        } catch (error) {
            console.error('خطأ في تحميل البيانات:', error);
            return defaultValue;
        }
    },
    
    // حذف البيانات
    removeItem: (key) => {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (error) {
            console.error('خطأ في حذف البيانات:', error);
            return false;
        }
    },
    
    // مسح جميع البيانات
    clear: () => {
        try {
            localStorage.clear();
            return true;
        } catch (error) {
            console.error('خطأ في مسح البيانات:', error);
            return false;
        }
    },
    
    // الحصول على جميع المفاتيح
    getAllKeys: () => {
        try {
            return Object.keys(localStorage);
        } catch (error) {
            console.error('خطأ في الحصول على المفاتيح:', error);
            return [];
        }
    }
});

// إضافة دعم للإشعارات
contextBridge.exposeInMainWorld('notifications', {
    // إظهار إشعار نظام
    show: (title, body, icon = null) => {
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification(title, {
                body: body,
                icon: icon,
                dir: 'rtl',
                lang: 'ar'
            });
        }
    },
    
    // طلب إذن الإشعارات
    requestPermission: async () => {
        if ('Notification' in window) {
            const permission = await Notification.requestPermission();
            return permission === 'granted';
        }
        return false;
    },
    
    // التحقق من دعم الإشعارات
    isSupported: () => {
        return 'Notification' in window;
    },
    
    // التحقق من إذن الإشعارات
    hasPermission: () => {
        return 'Notification' in window && Notification.permission === 'granted';
    }
});

// إضافة معلومات حالة التطبيق
contextBridge.exposeInMainWorld('appState', {
    // التحقق من حالة الاتصال
    isOnline: () => navigator.onLine,
    
    // مراقبة تغيير حالة الاتصال
    onOnline: (callback) => {
        window.addEventListener('online', callback);
    },
    
    onOffline: (callback) => {
        window.addEventListener('offline', callback);
    },
    
    // معلومات الشاشة
    getScreenInfo: () => {
        return {
            width: window.screen.width,
            height: window.screen.height,
            availWidth: window.screen.availWidth,
            availHeight: window.screen.availHeight,
            colorDepth: window.screen.colorDepth,
            pixelDepth: window.screen.pixelDepth
        };
    },
    
    // معلومات النافذة
    getWindowInfo: () => {
        return {
            width: window.innerWidth,
            height: window.innerHeight,
            scrollX: window.scrollX,
            scrollY: window.scrollY
        };
    }
});

console.log('✅ تم تحميل preload.js بنجاح - APIs جاهزة للاستخدام');
