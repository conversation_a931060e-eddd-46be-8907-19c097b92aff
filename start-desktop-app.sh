#!/bin/bash

# نظام إدارة ديون العملاء - تشغيل سطح المكتب
# Customer Debt Management System - Desktop App Launcher

# تعيين الترميز
export LANG=en_US.UTF-8

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# وظيفة طباعة ملونة
print_colored() {
    echo -e "${2}${1}${NC}"
}

# وظيفة طباعة العنوان
print_header() {
    echo
    echo "========================================"
    echo "    نظام إدارة ديون العملاء"
    echo "    Customer Debt Management System"
    echo "    تشغيل كبرنامج سطح مكتب"
    echo "========================================"
    echo
}

# وظيفة التحقق من Node.js
check_nodejs() {
    if ! command -v node &> /dev/null; then
        print_colored "❌ خطأ: Node.js غير مثبت على النظام" $RED
        echo
        print_colored "📥 يرجى تثبيت Node.js:" $YELLOW
        echo "Ubuntu/Debian: sudo apt install nodejs npm"
        echo "CentOS/RHEL: sudo yum install nodejs npm"
        echo "macOS: brew install node"
        echo "أو تحميل من: https://nodejs.org/"
        echo
        exit 1
    fi
    
    print_colored "✅ Node.js مثبت - الإصدار: $(node --version)" $GREEN
    echo
}

# وظيفة التحقق من package.json
check_package_json() {
    if [ ! -f "package.json" ]; then
        print_colored "❌ خطأ: ملف package.json غير موجود" $RED
        echo "تأكد من تشغيل الأمر في مجلد المشروع الصحيح"
        echo
        exit 1
    fi
}

# وظيفة تثبيت المتطلبات
install_dependencies() {
    if [ ! -d "node_modules" ]; then
        print_colored "📦 تثبيت المتطلبات لأول مرة..." $YELLOW
        echo "هذا قد يستغرق بضع دقائق..."
        echo
        
        npm install
        if [ $? -eq 0 ]; then
            print_colored "✅ تم تثبيت المتطلبات بنجاح" $GREEN
        else
            print_colored "❌ فشل في تثبيت المتطلبات" $RED
            exit 1
        fi
        echo
    fi
}

# وظيفة عرض القائمة
show_menu() {
    print_colored "🚀 اختر طريقة التشغيل:" $BLUE
    echo
    echo "1. تشغيل التطبيق (عادي)"
    echo "2. تشغيل التطبيق (مع أدوات المطور)"
    echo "3. بناء التطبيق للتوزيع"
    echo "4. بناء لـ Linux فقط"
    echo "5. بناء لـ Mac فقط (macOS only)"
    echo "6. بناء لجميع المنصات"
    echo "7. تنظيف وإعادة تثبيت المتطلبات"
    echo "8. عرض معلومات المشروع"
    echo "0. خروج"
    echo
}

# وظيفة تشغيل عادي
run_normal() {
    echo
    print_colored "🚀 تشغيل التطبيق..." $BLUE
    echo
    npm start
}

# وظيفة تشغيل مع أدوات المطور
run_dev() {
    echo
    print_colored "🔧 تشغيل التطبيق مع أدوات المطور..." $BLUE
    echo
    npm run dev
}

# وظيفة البناء العام
build_all() {
    echo
    print_colored "🏗️ بناء التطبيق للتوزيع..." $YELLOW
    echo "هذا قد يستغرق عدة دقائق..."
    echo
    
    npm run build
    if [ $? -eq 0 ]; then
        print_colored "✅ تم بناء التطبيق بنجاح!" $GREEN
        print_colored "📁 الملفات متوفرة في مجلد: dist/" $BLUE
        echo
        if [ -d "dist" ]; then
            print_colored "📋 الملفات المُنتجة:" $BLUE
            ls -la dist/ | grep -E '\.(AppImage|deb|dmg|exe)$'
        fi
    else
        print_colored "❌ فشل في بناء التطبيق" $RED
    fi
}

# وظيفة بناء Linux
build_linux() {
    echo
    print_colored "🐧 بناء التطبيق لـ Linux..." $BLUE
    echo
    npm run build-linux
    if [ $? -eq 0 ]; then
        print_colored "✅ تم بناء التطبيق لـ Linux بنجاح!" $GREEN
        print_colored "📁 الملفات متوفرة في مجلد: dist/" $BLUE
    else
        print_colored "❌ فشل في بناء التطبيق" $RED
    fi
}

# وظيفة بناء Mac
build_mac() {
    if [[ "$OSTYPE" != "darwin"* ]]; then
        print_colored "⚠️ تحذير: بناء تطبيقات Mac يتطلب نظام macOS" $YELLOW
        echo "يمكنك المتابعة لكن قد تواجه مشاكل..."
        echo
    fi
    
    echo
    print_colored "🍎 بناء التطبيق لـ Mac..." $BLUE
    echo
    npm run build-mac
    if [ $? -eq 0 ]; then
        print_colored "✅ تم بناء التطبيق لـ Mac بنجاح!" $GREEN
        print_colored "📁 الملفات متوفرة في مجلد: dist/" $BLUE
    else
        print_colored "❌ فشل في بناء التطبيق" $RED
    fi
}

# وظيفة بناء جميع المنصات
build_all_platforms() {
    echo
    print_colored "🌍 بناء التطبيق لجميع المنصات..." $YELLOW
    echo "هذا قد يستغرق وقت طويل..."
    echo
    
    print_colored "🐧 بناء Linux..." $BLUE
    npm run build-linux
    
    print_colored "🍎 بناء Mac..." $BLUE
    npm run build-mac
    
    print_colored "🪟 بناء Windows..." $BLUE
    npm run build-win
    
    print_colored "✅ تم الانتهاء من البناء لجميع المنصات" $GREEN
}

# وظيفة التنظيف
clean_install() {
    echo
    print_colored "🧹 تنظيف وإعادة تثبيت المتطلبات..." $YELLOW
    echo
    
    if [ -d "node_modules" ]; then
        echo "حذف node_modules..."
        rm -rf node_modules
    fi
    
    if [ -f "package-lock.json" ]; then
        echo "حذف package-lock.json..."
        rm package-lock.json
    fi
    
    echo "إعادة تثبيت المتطلبات..."
    npm install
    
    if [ $? -eq 0 ]; then
        print_colored "✅ تم تنظيف وإعادة تثبيت المتطلبات بنجاح" $GREEN
    else
        print_colored "❌ فشل في إعادة التثبيت" $RED
    fi
}

# وظيفة عرض المعلومات
show_info() {
    echo
    print_colored "📋 معلومات المشروع:" $BLUE
    echo "=================="
    echo
    
    if [ -f "package.json" ]; then
        grep -E '"name"|"version"|"description"' package.json
    fi
    
    echo
    print_colored "📊 إحصائيات:" $BLUE
    [ -d "html" ] && echo "✅ ملفات HTML موجودة"
    [ -d "css" ] && echo "✅ ملفات CSS موجودة"
    [ -d "javascript" ] && echo "✅ ملفات JavaScript موجودة"
    [ -d "electron" ] && echo "✅ ملفات Electron موجودة"
    [ -d "build" ] && echo "✅ مجلد البناء موجود"
    [ -d "node_modules" ] && echo "✅ المتطلبات مثبتة"
    
    echo
    print_colored "🖥️ معلومات النظام:" $BLUE
    echo "النظام: $(uname -s)"
    echo "المعمارية: $(uname -m)"
    echo "Node.js: $(node --version 2>/dev/null || echo 'غير مثبت')"
    echo "npm: $(npm --version 2>/dev/null || echo 'غير مثبت')"
    echo
}

# الوظيفة الرئيسية
main() {
    print_header
    check_nodejs
    check_package_json
    install_dependencies
    
    while true; do
        show_menu
        read -p "اختر رقم (0-8): " choice
        
        case $choice in
            1) run_normal ;;
            2) run_dev ;;
            3) build_all ;;
            4) build_linux ;;
            5) build_mac ;;
            6) build_all_platforms ;;
            7) clean_install ;;
            8) show_info ;;
            0) 
                echo
                print_colored "👋 شكراً لاستخدام نظام إدارة ديون العملاء" $GREEN
                echo
                exit 0
                ;;
            *)
                echo
                print_colored "❌ اختيار غير صحيح. يرجى اختيار رقم من 0 إلى 8" $RED
                echo
                ;;
        esac
        
        echo
        print_colored "🎯 انتهت العملية" $BLUE
        echo
        read -p "اضغط Enter للمتابعة..."
        clear
        print_header
    done
}

# تشغيل البرنامج
main
