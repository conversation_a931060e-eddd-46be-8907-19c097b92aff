// Customer Debt Management System - JavaScript

class DebtManager {
    constructor() {
        this.debts = [];
        this.previousDebts = [];
        this.filteredDebts = []; // للبيانات المفلترة
        this.filteredPreviousDebts = []; // للديون السابقة المفلترة
        this.currentSearchTerm = ''; // مصطلح البحث الحالي
        // إعدادات الفرز
        this.sortConfig = {
            field: null,
            direction: 'asc', // 'asc' or 'desc'
            section: null // 'new', 'previous', 'differences'
        };
        this.init();
    }

    init() {
        this.loadData();
        this.loadPreviousDebts();

        // Initialize sample data if no data exists
        if (this.debts.length === 0 && this.previousDebts.length === 0) {
            this.initializeSampleData();
        }

        this.bindEvents();

        // Initialize filtered data
        this.filteredDebts = [...this.debts];
        this.filteredPreviousDebts = [...this.previousDebts];

        // Initialize sticky headers
        this.initializeStickyHeaders();

        this.updateStatistics();
        this.displayRecords();
        this.displayNewDebt([]);
        this.displayPreviousDebt(this.previousDebts);
        this.showWelcomeMessage();

        // Initialize analytics section
        setTimeout(() => {
            this.showSection('analytics');
        }, 100);
    }

    // Show welcome message
    showWelcomeMessage() {
        console.log('🎉 نظام إدارة ديون العملاء جاهز للاستخدام');
        console.log('📊 إجمالي السجلات الجديدة:', this.debts.length);
        console.log('📊 إجمالي الديون السابقة:', this.previousDebts.length);
    }

    // Load data from localStorage
    loadData() {
        const savedData = localStorage.getItem('customerDebts');
        if (savedData) {
            this.debts = JSON.parse(savedData);
        }
    }

    // Save data to localStorage
    saveData() {
        localStorage.setItem('customerDebts', JSON.stringify(this.debts));
    }

    // Load previous debts from localStorage
    loadPreviousDebts() {
        const savedData = localStorage.getItem('previousDebts');
        if (savedData) {
            this.previousDebts = JSON.parse(savedData);
        }
    }

    // Save previous debts to localStorage
    savePreviousDebts() {
        localStorage.setItem('previousDebts', JSON.stringify(this.previousDebts));
    }

    // Initialize sample data for demonstration
    initializeSampleData() {
        this.previousDebts = [
            { id: 1, invoiceNumber: 'INV-001', customerName: 'محمد العلي', city: 'الدمام', amount: 35000, notes: 'فاتورة سابقة', date: '15/01/2024' },
            { id: 2, invoiceNumber: 'INV-002', customerName: 'فاطمة أحمد', city: 'المدينة المنورة', amount: 57660, notes: 'فاتورة سابقة', date: '20/01/2024' },
            { id: 3, invoiceNumber: 'INV-003', customerName: 'أحمد محمد', city: 'الرياض', amount: 27000, notes: 'فاتورة سابقة', date: '25/01/2024' },
            { id: 4, invoiceNumber: 'INV-004', customerName: 'سارة أحمد', city: 'جدة', amount: 43050, notes: 'فاتورة سابقة', date: '01/02/2024' },
            { id: 5, invoiceNumber: 'INV-005', customerName: 'عبدالله سعد', city: 'جدة', amount: 18000, notes: 'فاتورة سابقة', date: '05/02/2024' }
        ];

        // Add some sample new invoices
        this.debts = [
            { id: 101, invoiceNumber: 'INV-101', customerName: 'محمد العلي', city: 'الدمام', amount: 45000, notes: 'فاتورة جديدة', date: '01/03/2024' },
            { id: 102, invoiceNumber: 'INV-102', customerName: 'علي أحمد', city: 'الرياض', amount: 25000, notes: 'فاتورة جديدة', date: '02/03/2024' },
            { id: 103, invoiceNumber: 'INV-103', customerName: 'نورا سعد', city: 'مكة', amount: 32000, notes: 'فاتورة جديدة', date: '03/03/2024' }
        ];

        this.savePreviousDebts();
        this.saveData();

        console.log('✅ تم تحميل البيانات التجريبية بنجاح');
    }

    // Bind event listeners
    bindEvents() {
        // Form submission
        document.getElementById('debtForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addDebt();
        });



        // Search functionality
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            // مراقبة الإدخال المباشر
            searchInput.addEventListener('input', (e) => {
                const value = e.target.value;
                console.log(`🔍 تغيير في البحث: "${value}"`);
                this.searchRecords(value);
            });

            // مراقبة مسح النص
            searchInput.addEventListener('keyup', (e) => {
                if (e.key === 'Backspace' || e.key === 'Delete') {
                    const value = e.target.value;
                    console.log(`🗑️ مسح في البحث: "${value}"`);
                    this.searchRecords(value);
                }
            });

            // مراقبة اللصق
            searchInput.addEventListener('paste', (e) => {
                setTimeout(() => {
                    const value = e.target.value;
                    console.log(`📋 لصق في البحث: "${value}"`);
                    this.searchRecords(value);
                }, 10);
            });
        }



        // Customer name and city change to show history
        document.getElementById('customerName').addEventListener('input', () => {
            this.showCustomerHistory();
        });

        document.getElementById('city').addEventListener('input', () => {
            this.showCustomerHistory();
        });
    }

    // Add new debt record
    addDebt() {
        const formData = this.getFormData();

        // Validate form data
        if (!this.validateForm(formData)) {
            return;
        }

        // Check for duplicate invoice number with same customer and city
        if (this.isDuplicateInvoiceWithSameCustomer(formData.invoiceNumber, formData.customerName, formData.city)) {
            this.showError('رقم الفاتورة موجود مسبقاً لنفس العميل والمدينة. يرجى استخدام رقم مختلف.');
            return;
        }

        // Create new debt record
        const newDebt = {
            id: Date.now(),
            invoiceNumber: formData.invoiceNumber,
            customerName: formData.customerName.trim(),
            city: formData.city.trim(),
            amount: parseFloat(formData.amount),
            notes: formData.notes.trim(),
            date: new Date().toLocaleDateString('en-GB'), // DD/MM/YYYY format
            timestamp: new Date().toISOString()
        };

        // Add to debts array
        this.debts.unshift(newDebt);

        // Save data
        this.saveData();

        // Update UI
        this.updateStatistics();
        this.displayRecords();
        this.displayNewDebt([newDebt]);

        // تحديث البيانات المفلترة
        this.filteredDebts = [...this.debts];
        this.filteredPreviousDebts = [...this.previousDebts];

        // إعادة تطبيق البحث إذا كان نشطاً
        if (this.currentSearchTerm) {
            console.log('🔄 إعادة تطبيق البحث بعد تحديث البيانات');
            this.searchRecords(this.currentSearchTerm);
        } else {
            // إذا لم يكن هناك بحث نشط، تأكد من عرض جميع البيانات
            console.log('📊 عرض جميع البيانات - لا يوجد بحث نشط');
            this.displayRecords(this.debts);
            this.displayPreviousDebt(this.previousDebts);
        }

        // إعادة تطبيق الفرز إذا كان نشطاً
        this.reapplySortIfActive();

        this.refreshAnalytics();

        // Show success message
        this.showSuccess(`تم إضافة الدين بنجاح للعميل: ${newDebt.customerName}`);

        // Add animation effect
        this.addSuccessAnimation();

        // Clear form after a short delay
        setTimeout(() => {
            document.getElementById('debtForm').reset();
        }, 1500);
    }

    // Get form data
    getFormData() {
        return {
            invoiceNumber: document.getElementById('invoiceNumber').value.trim(),
            customerName: document.getElementById('customerName').value.trim(),
            city: document.getElementById('city').value.trim(),
            amount: document.getElementById('amount').value,
            notes: document.getElementById('notes').value.trim()
        };
    }

    // Validate form data
    validateForm(data) {
        if (!data.invoiceNumber) {
            this.showError('يرجى إدخال رقم الفاتورة');
            return false;
        }
        if (!data.customerName) {
            this.showError('يرجى إدخال اسم العميل');
            return false;
        }
        if (!data.city) {
            this.showError('يرجى إدخال المدينة');
            return false;
        }
        if (!data.amount || parseFloat(data.amount) <= 0) {
            this.showError('يرجى إدخال مبلغ صحيح');
            return false;
        }
        return true;
    }

    // Check for duplicate invoice number with same customer and city
    isDuplicateInvoiceWithSameCustomer(invoiceNumber, customerName, city) {
        const normalizedCustomer = customerName.toLowerCase().trim();
        const normalizedCity = city.toLowerCase().trim();

        // Check ONLY in new debts for same invoice number with same customer and city
        // Previous debts are reference data and should not prevent adding new invoices
        const inNewDebts = this.debts.some(debt =>
            debt.invoiceNumber === invoiceNumber &&
            debt.customerName.toLowerCase().trim() === normalizedCustomer &&
            debt.city.toLowerCase().trim() === normalizedCity
        );

        return inNewDebts;
    }

    // Keep the old function for backward compatibility (if needed elsewhere)
    isDuplicateInvoice(invoiceNumber) {
        // Check in both new debts and previous debts
        const inNewDebts = this.debts.some(debt => debt.invoiceNumber === invoiceNumber);
        const inPreviousDebts = this.previousDebts.some(debt => debt.invoiceNumber === invoiceNumber);
        return inNewDebts || inPreviousDebts;
    }





    // Display previous debt (reference only - no new data added here)
    displayPreviousDebt(previousDebts = []) {
        console.log('🖥️ عرض الديون السابقة - عدد السجلات:', previousDebts.length);

        // This will show historical/reference data only
        // No new invoices are added to this section
        const previousDebtList = document.getElementById('previousDebtList');
        const noPreviousDebtDiv = document.getElementById('noPreviousDebt');

        if (previousDebts.length === 0) {
            console.log('⚠️ لا توجد ديون سابقة للعرض');
            previousDebtList.innerHTML = '';
            noPreviousDebtDiv.style.display = 'block';

            // Update stats to show zero
            document.getElementById('previousDebtCustomers').textContent = '0';
            document.getElementById('previousDebtInvoices').textContent = '0';
            document.getElementById('previousDebtTotal').textContent = '0 ر.س';
            return;
        }

        noPreviousDebtDiv.style.display = 'none';

        let debtHTML = '';
        previousDebts.forEach(debt => {
            const debtData = JSON.stringify(debt).replace(/"/g, '&quot;');
            debtHTML += `
                <div class="debt-item" onclick="debtManager.fillFormWithoutAmount(${debtData})" style="cursor: pointer;">
                    <div class="debt-item-header">
                        <span class="debt-customer">${debt.customerName}</span>
                        <span class="debt-amount">${debt.amount.toLocaleString('ar-SA')} ر.س</span>
                    </div>
                    <div class="debt-details">
                        <div>
                            <span class="debt-invoice">${debt.invoiceNumber}</span>
                            <span class="debt-city">${debt.city}</span>
                        </div>
                        <span class="debt-date">${debt.date}</span>
                    </div>
                    ${debt.notes ? `<div class="debt-notes" style="margin-top: 5px; font-size: 11px; color: #64748b;">${debt.notes}</div>` : ''}
                </div>
            `;
        });

        previousDebtList.innerHTML = debtHTML;

        // Re-initialize sticky headers after content update
        setTimeout(() => {
            this.initializeStickyHeaders();
        }, 100);

        // Update stats for previous debts
        const totalAmount = previousDebts.reduce((sum, debt) => sum + debt.amount, 0);
        const uniqueCustomers = new Set();
        previousDebts.forEach(debt => {
            const customerKey = `${debt.customerName.toLowerCase().trim()}_${debt.city.toLowerCase().trim()}`;
            uniqueCustomers.add(customerKey);
        });

        document.getElementById('previousDebtCustomers').textContent = uniqueCustomers.size;
        document.getElementById('previousDebtInvoices').textContent = previousDebts.length;
        document.getElementById('previousDebtTotal').textContent = totalAmount.toLocaleString('ar-SA') + ' ر.س';

        console.log('✅ تم عرض الديون السابقة بنجاح:', {
            عدد_العملاء: uniqueCustomers.size,
            عدد_الفواتير: previousDebts.length,
            إجمالي_المبلغ: totalAmount
        });
    }

    // Fill form with debt data (without amount for invoice click)
    fillFormWithoutAmount(debt) {
        document.getElementById('invoiceNumber').value = debt.invoiceNumber;
        document.getElementById('customerName').value = debt.customerName;
        document.getElementById('city').value = debt.city;
        document.getElementById('amount').value = ''; // Don't fill amount
        document.getElementById('notes').value = debt.notes || '';

        this.showSuccess('تم تحميل بيانات العميل. يرجى إدخال المبلغ الجديد');
    }

    // Display new debt (for animation purposes)
    displayNewDebt(newDebts) {
        if (newDebts && newDebts.length > 0) {
            // Add animation to new debt items
            setTimeout(() => {
                const debtItems = document.querySelectorAll('.debt-item');
                if (debtItems.length > 0) {
                    debtItems[0].style.animation = 'slideInRight 0.5s ease-out';
                }
            }, 100);
        }
    }

    // Show customer history when typing name and city
    showCustomerHistory() {
        const customerName = document.getElementById('customerName').value.trim();
        const city = document.getElementById('city').value.trim();

        // Only show customer history if there are no imported previous debts
        // This prevents overwriting imported data
        if (this.previousDebts && this.previousDebts.length > 0) {
            return; // Don't override imported previous debts
        }

        if (customerName.length >= 2 && city.length >= 2) {
            const customerHistory = this.debts.filter(debt =>
                debt.customerName.toLowerCase().includes(customerName.toLowerCase()) &&
                debt.city.toLowerCase().includes(city.toLowerCase())
            );

            if (customerHistory.length > 0) {
                this.displayPreviousDebt(customerHistory);
            }
        } else if (customerName.length < 2 || city.length < 2) {
            // Show imported previous debts when clearing the form
            this.displayPreviousDebt(this.previousDebts);
        }
    }



    // Update new invoices statistics (this is where new data goes)
    updateNewInvoicesStats(invoices) {
        const totalAmount = invoices.reduce((sum, debt) => sum + debt.amount, 0);
        const invoiceCount = invoices.length;

        // Count unique customers based on name and city combination
        const uniqueCustomers = new Set();
        invoices.forEach(debt => {
            const customerKey = `${debt.customerName.toLowerCase().trim()}_${debt.city.toLowerCase().trim()}`;
            uniqueCustomers.add(customerKey);
        });
        const customerCount = uniqueCustomers.size;

        document.getElementById('newInvoicesCustomers').textContent = customerCount;
        document.getElementById('newInvoicesCount').textContent = invoiceCount;
        document.getElementById('newInvoicesTotal').textContent = totalAmount.toLocaleString('ar-SA') + ' ر.س';
    }



    // Update statistics
    updateStatistics() {
        // Update new invoices stats (this is where all new data goes)
        this.updateNewInvoicesStats(this.debts);

        // تحديث البيانات المفلترة
        if (!this.currentSearchTerm) {
            this.filteredDebts = [...this.debts];
            this.filteredPreviousDebts = [...this.previousDebts];
            // Display previous debts only if no search is active
            this.displayPreviousDebt(this.previousDebts);
        } else {
            // If search is active, maintain filtered data and display
            this.searchRecords(this.currentSearchTerm);
        }

        // إعادة تطبيق الفرز إذا كان نشطاً
        this.reapplySortIfActive();
    }

    // Update statistics for filtered data
    updateFilteredStatistics() {
        // Update new invoices stats with filtered data
        this.updateNewInvoicesStats(this.filteredDebts);

        // Update previous debts stats with filtered data
        this.updatePreviousDebtsStats(this.filteredPreviousDebts);
    }

    // Update previous debts statistics
    updatePreviousDebtsStats(debts) {
        const totalAmount = debts.reduce((sum, debt) => sum + debt.amount, 0);
        const uniqueCustomers = new Set();
        debts.forEach(debt => {
            const customerKey = `${debt.customerName.toLowerCase().trim()}_${debt.city.toLowerCase().trim()}`;
            uniqueCustomers.add(customerKey);
        });

        document.getElementById('previousDebtCustomers').textContent = uniqueCustomers.size;
        document.getElementById('previousDebtInvoices').textContent = debts.length;
        document.getElementById('previousDebtTotal').textContent = totalAmount.toLocaleString('ar-SA') + ' ر.س';
    }

    // Display all records (new invoices)
    displayRecords(records = null) {
        const recordsToShow = records || this.debts;
        const newInvoicesList = document.getElementById('newInvoicesList');
        const noNewInvoicesDiv = document.getElementById('noNewInvoices');

        if (recordsToShow.length === 0) {
            newInvoicesList.innerHTML = '';
            noNewInvoicesDiv.style.display = 'block';
            return;
        }

        noNewInvoicesDiv.style.display = 'none';

        let debtHTML = '';
        recordsToShow.forEach(debt => {
            const debtData = JSON.stringify(debt).replace(/"/g, '&quot;');
            debtHTML += `
                <div class="debt-item" onclick="debtManager.fillFormWithoutAmount(${debtData})" style="cursor: pointer;">
                    <div class="debt-item-header">
                        <span class="debt-customer">${debt.customerName}</span>
                        <span class="debt-amount">${debt.amount.toLocaleString('ar-SA')} ر.س</span>
                    </div>
                    <div class="debt-details">
                        <div>
                            <span class="debt-invoice">${debt.invoiceNumber}</span>
                            <span class="debt-city">${debt.city}</span>
                        </div>
                        <span class="debt-date">${debt.date}</span>
                    </div>
                    ${debt.notes ? `<div class="debt-notes" style="margin-top: 5px; font-size: 11px; color: #64748b;">${debt.notes}</div>` : ''}
                    <div class="debt-actions" onclick="event.stopPropagation()">
                        <button class="debt-action-btn debt-edit-btn" onclick="debtManager.editDebt(${debt.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="debt-action-btn debt-delete-btn" onclick="debtManager.deleteDebt(${debt.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        });

        newInvoicesList.innerHTML = debtHTML;

        // Re-initialize sticky headers after content update
        setTimeout(() => {
            this.initializeStickyHeaders();
        }, 100);
    }

    // Search records
    searchRecords(searchTerm) {
        this.currentSearchTerm = searchTerm.trim();
        console.log(`🔍 البحث عن: "${this.currentSearchTerm}"`);

        // إظهار أو إخفاء زر إلغاء البحث
        const clearBtn = document.getElementById('clearSearchBtn');
        if (clearBtn) {
            clearBtn.style.display = this.currentSearchTerm ? 'flex' : 'none';
        }

        if (!this.currentSearchTerm) {
            // إذا لم يكن هناك بحث، اعرض جميع البيانات
            console.log('🔄 مسح البحث - إرجاع جميع البيانات');
            this.filteredDebts = [...this.debts];
            this.filteredPreviousDebts = [...this.previousDebts];

            // إعادة تعيين الفرز
            this.resetSort();

            // عرض جميع البيانات الأصلية
            this.displayRecords(this.debts);
            this.displayPreviousDebt(this.previousDebts);
            this.showSearchResults(null);

            // تحديث الإحصائيات بالبيانات الكاملة
            this.updateStatistics();
            this.refreshAnalytics();

            console.log('✅ تم إرجاع جميع البيانات إلى طبيعتها');
            return;
        }

        // فلترة الفواتير الجديدة
        this.filteredDebts = this.debts.filter(debt => {
            const searchLower = this.currentSearchTerm.toLowerCase();
            return (
                debt.invoiceNumber.toLowerCase().includes(searchLower) ||
                debt.customerName.toLowerCase().includes(searchLower) ||
                debt.city.toLowerCase().includes(searchLower) ||
                debt.amount.toString().includes(searchLower) ||
                (debt.notes && debt.notes.toLowerCase().includes(searchLower)) ||
                debt.date.includes(this.currentSearchTerm)
            );
        });

        // فلترة الديون السابقة
        this.filteredPreviousDebts = this.previousDebts.filter(debt => {
            const searchLower = this.currentSearchTerm.toLowerCase();
            return (
                debt.invoiceNumber.toLowerCase().includes(searchLower) ||
                debt.customerName.toLowerCase().includes(searchLower) ||
                debt.city.toLowerCase().includes(searchLower) ||
                debt.amount.toString().includes(searchLower) ||
                (debt.notes && debt.notes.toLowerCase().includes(searchLower)) ||
                debt.date.includes(this.currentSearchTerm)
            );
        });

        // عرض النتائج المفلترة
        this.displayRecords(this.filteredDebts);
        this.displayPreviousDebt(this.filteredPreviousDebts);
        this.showSearchResults(this.filteredDebts, this.currentSearchTerm);

        // تحديث الإحصائيات والتحليلات للبيانات المفلترة
        this.updateFilteredStatistics();
        this.refreshAnalytics();

        // إعادة تطبيق الفرز إذا كان نشطاً
        this.reapplySortIfActive();

        console.log(`✅ تم البحث: ${this.filteredDebts.length} فاتورة جديدة، ${this.filteredPreviousDebts.length} دين سابق`);
    }

    // Show search results info
    showSearchResults(results, searchTerm) {
        const searchInfo = document.getElementById('searchInfo');
        if (!searchInfo) {
            const info = document.createElement('div');
            info.id = 'searchInfo';
            info.className = 'search-info';
            document.querySelector('.search-container').appendChild(info);
        }

        if (results === null || !searchTerm) {
            document.getElementById('searchInfo').style.display = 'none';
            console.log('🔍 إخفاء معلومات البحث');
        } else {
            document.getElementById('searchInfo').style.display = 'block';
            document.getElementById('searchInfo').innerHTML = `
                <i class="fas fa-search"></i>
                تم العثور على ${results.length} نتيجة للبحث عن "${searchTerm}"
            `;
            console.log(`🔍 عرض معلومات البحث: ${results.length} نتيجة`);
        }
    }

    // Delete debt record
    deleteDebt(id) {
        if (confirm('هل أنت متأكد من حذف هذا السجل؟')) {
            this.debts = this.debts.filter(debt => debt.id !== id);
            this.saveData();
            this.updateStatistics();
            this.displayRecords();
            this.showSuccess('تم حذف السجل بنجاح');
        }
    }

    // Add success animation
    addSuccessAnimation() {
        const containers = document.querySelectorAll('.data-container');
        containers.forEach(container => {
            container.style.animation = 'pulse 0.6s ease-in-out';
            setTimeout(() => {
                container.style.animation = '';
            }, 600);
        });
    }

    // Show success message
    showSuccess(message) {
        // Only show toast notification
        this.showToast(message, 'success');
    }

    // Show error message
    showError(message) {
        // Only show toast notification
        this.showToast(message, 'error');
    }

    // Show toast notification
    showToast(message, type) {
        // Position multiple toasts
        const existingToasts = document.querySelectorAll('.toast-notification');
        const topOffset = 20 + (existingToasts.length * 80);

        const toast = document.createElement('div');
        toast.className = `toast-notification toast-${type}`;
        toast.style.top = `${topOffset}px`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i>
                <span>${message}</span>
                <button class="toast-close" onclick="debtManager.removeToast(this.parentElement.parentElement)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        document.body.appendChild(toast);

        // Show toast with animation
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);

        // Auto-hide toast after 5 seconds
        setTimeout(() => {
            this.removeToast(toast);
        }, 5000);
    }

    // Remove toast with animation
    removeToast(toast) {
        if (document.body.contains(toast)) {
            toast.classList.remove('show');
            setTimeout(() => {
                if (document.body.contains(toast)) {
                    document.body.removeChild(toast);
                    // Reposition remaining toasts
                    this.repositionToasts();
                }
            }, 300);
        }
    }

    // Reposition remaining toasts
    repositionToasts() {
        const toasts = document.querySelectorAll('.toast-notification');
        toasts.forEach((toast, index) => {
            toast.style.top = `${20 + (index * 80)}px`;
        });
    }

    // Edit debt record (loads all data including amount)
    editDebt(id) {
        const debt = this.debts.find(d => d.id === id);
        if (!debt) {
            this.showError('لم يتم العثور على السجل');
            return;
        }

        // Fill the form with ALL existing data including amount
        document.getElementById('invoiceNumber').value = debt.invoiceNumber;
        document.getElementById('customerName').value = debt.customerName;
        document.getElementById('city').value = debt.city;
        document.getElementById('amount').value = debt.amount; // Include amount for editing
        document.getElementById('notes').value = debt.notes || '';

        // Remove the old record
        this.debts = this.debts.filter(d => d.id !== id);
        this.saveData();
        this.updateStatistics();
        this.displayRecords();

        // Update filtered data if search is active
        if (this.currentSearchTerm) {
            this.filteredDebts = [...this.debts];
            this.filteredPreviousDebts = [...this.previousDebts];
        }

        this.showSuccess('تم تحميل جميع البيانات للتعديل. يرجى إجراء التعديلات المطلوبة والضغط على إضافة');
    }

    // Delete debt record
    deleteDebt(id) {
        if (confirm('هل أنت متأكد من حذف هذا السجل؟')) {
            this.debts = this.debts.filter(debt => debt.id !== id);
            this.saveData();
            this.updateStatistics();
            this.displayRecords();

            // Update filtered data and re-apply search if active
            this.filteredDebts = [...this.debts];
            this.filteredPreviousDebts = [...this.previousDebts];

            if (this.currentSearchTerm) {
                console.log('🔄 إعادة تطبيق البحث بعد الحذف');
                this.searchRecords(this.currentSearchTerm);
            } else {
                console.log('📊 عرض جميع البيانات بعد الحذف');
                this.displayRecords(this.debts);
                this.displayPreviousDebt(this.previousDebts);
            }

            this.refreshAnalytics();
            this.showSuccess('تم حذف السجل بنجاح');
        }
    }

    // Show analytics section with smooth animation
    showSection(sectionType) {
        console.log('🔄 Switching to section:', sectionType);

        // Get all content sections
        const analyticsContent = document.getElementById('analyticsContent');
        const comparisonContent = document.getElementById('comparisonContent');
        const statsContent = document.getElementById('statsContent');

        const allSections = [analyticsContent, comparisonContent, statsContent];

        // Remove active class from all buttons
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // Step 1: Add exit animation to visible sections
        allSections.forEach(section => {
            if (section && section.style.display !== 'none') {
                section.classList.add('section-exit');
                section.classList.remove('section-enter', 'section-slide-in');
            }
        });

        // Step 2: After exit animation, hide sections and show target
        setTimeout(() => {
            // Hide all sections
            allSections.forEach(section => {
                if (section) {
                    section.style.display = 'none';
                    section.style.visibility = 'hidden';
                    section.style.height = '0';
                    section.style.minHeight = '0';
                    section.style.maxHeight = '0';
                    section.style.padding = '0';
                    section.style.margin = '0';
                    section.style.overflow = 'hidden';
                    section.style.opacity = '0';
                    section.style.transform = 'translateY(-20px) scale(0.95)';
                    section.classList.remove('section-exit', 'section-enter', 'section-slide-in');
                    section.innerHTML = ''; // Clear content to prevent space
                }
            });

            // Determine target section
            let targetSection = null;
            let targetButton = null;
            let loadFunction = null;

            switch(sectionType) {
                case 'analytics':
                    targetSection = analyticsContent;
                    targetButton = document.getElementById('analyticsNavBtn');
                    loadFunction = () => this.loadAnalytics();
                    break;
                case 'comparisons':
                    targetSection = comparisonContent;
                    targetButton = document.getElementById('comparisonsNavBtn');
                    loadFunction = () => this.loadComparisons();
                    break;
                case 'statistics':
                    targetSection = statsContent;
                    targetButton = document.getElementById('statisticsNavBtn');
                    loadFunction = () => this.loadAdvancedStats();
                    break;
                case 'differences':
                    targetSection = document.getElementById('differencesContent');
                    targetButton = document.getElementById('differencesNavBtn');
                    loadFunction = () => this.loadDifferences();
                    break;
            }

            // Step 3: Show and animate target section
            if (targetSection && targetButton && loadFunction) {
                // Prepare section for animation
                targetSection.style.display = 'block';
                targetSection.style.visibility = 'visible';
                targetSection.style.height = 'auto';
                targetSection.style.minHeight = '300px';
                targetSection.style.maxHeight = 'none';
                targetSection.style.padding = '20px';
                targetSection.style.margin = '0';
                targetSection.style.overflow = 'visible';
                targetSection.style.opacity = '0';
                targetSection.style.transform = 'translateY(30px) scale(0.95)';

                // Load content first
                loadFunction();

                // Activate button
                targetButton.classList.add('active');

                // Add entrance animation
                setTimeout(() => {
                    targetSection.classList.add('section-enter');
                    targetSection.style.opacity = '1';
                    targetSection.style.transform = 'translateY(0) scale(1)';
                }, 50);

                console.log(`✅ ${sectionType} section shown with animation`);
            }
        }, 250); // Wait for exit animation to complete
    }

    // Load analytics content
    loadAnalytics() {
        const analyticsContent = document.getElementById('analyticsContent');

        // استخدام البيانات المفلترة أو الكاملة حسب حالة البحث
        const currentDebts = this.currentSearchTerm ? this.filteredDebts : this.debts;
        const currentPreviousDebts = this.currentSearchTerm ? this.filteredPreviousDebts : this.previousDebts;

        // Check if there's any data to analyze
        if (currentDebts.length === 0 && currentPreviousDebts.length === 0) {
            const filterIndicator = this.currentSearchTerm ?
                `<div class="filter-indicator">
                    <i class="fas fa-filter"></i>
                    مفلتر حسب: "${this.currentSearchTerm}"
                    <button onclick="debtManager.clearSearch()" class="clear-filter-btn">
                        <i class="fas fa-times"></i> إلغاء الفلتر
                    </button>
                </div>` : '';

            analyticsContent.innerHTML = `
                ${filterIndicator}
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h5>لا توجد بيانات للتحليل</h5>
                    <p>قم بإضافة فواتير جديدة أو استيراد ديون سابقة لرؤية التحليلات</p>
                    <div class="empty-state-actions">
                        <button onclick="document.getElementById('invoiceNumber').focus()" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة فاتورة جديدة
                        </button>
                    </div>
                </div>
            `;
            return;
        }

        // Get unique customers from filtered data
        const newCustomers = this.getUniqueCustomers(currentDebts);
        const previousCustomers = this.getUniqueCustomers(currentPreviousDebts);

        // Find customers who are new (not in previous debts)
        const firstTimeCustomers = newCustomers.filter(newCustomer =>
            !previousCustomers.some(prevCustomer =>
                prevCustomer.name === newCustomer.name && prevCustomer.city === newCustomer.city
            )
        );

        // Find inactive customers (in previous but not in new)
        const inactiveCustomers = previousCustomers.filter(prevCustomer =>
            !newCustomers.some(newCustomer =>
                newCustomer.name === prevCustomer.name && newCustomer.city === prevCustomer.city
            )
        );

        // إضافة مؤشر الفلترة
        const filterIndicator = this.currentSearchTerm ?
            `<div class="filter-indicator">
                <i class="fas fa-filter"></i>
                مفلتر حسب: "${this.currentSearchTerm}"
                <button onclick="debtManager.clearSearch()" class="clear-filter-btn">
                    <i class="fas fa-times"></i> إلغاء الفلتر
                </button>
            </div>` : '';

        // حساب إحصائيات العملاء الجدد
        const newCustomersStats = {
            count: firstTimeCustomers.length,
            totalInvoices: firstTimeCustomers.reduce((sum, customer) => sum + customer.invoices, 0),
            totalAmount: firstTimeCustomers.reduce((sum, customer) => sum + customer.totalAmount, 0)
        };

        // حساب إحصائيات العملاء غير النشطين
        const inactiveCustomersStats = {
            count: inactiveCustomers.length,
            totalInvoices: inactiveCustomers.reduce((sum, customer) => sum + customer.invoices, 0),
            totalAmount: inactiveCustomers.reduce((sum, customer) => sum + customer.totalAmount, 0)
        };

        analyticsContent.innerHTML = `
            ${filterIndicator}
            <div class="analytics-grid">
                <div class="analytics-section">
                    <div class="analytics-header">
                        <h5><i class="fas fa-user-plus text-success"></i> العملاء الجدد</h5>
                        <div class="analytics-actions">
                            <button onclick="debtManager.exportAnalyticsToExcel('newCustomers')" class="export-btn excel-btn" title="تصدير Excel">
                                <i class="fas fa-file-excel"></i>
                            </button>
                            <button onclick="debtManager.printAnalyticsPDF('newCustomers')" class="export-btn pdf-btn" title="طباعة PDF">
                                <i class="fas fa-file-pdf"></i>
                            </button>
                        </div>
                        <div class="analytics-stats">
                            <span class="stat-item">
                                <i class="fas fa-users"></i>
                                ${newCustomersStats.count} عميل
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-file-invoice"></i>
                                ${newCustomersStats.totalInvoices} فاتورة
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-money-bill-wave"></i>
                                ${newCustomersStats.totalAmount.toLocaleString('ar-SA')} ر.س
                            </span>
                        </div>
                    </div>
                    <div class="analytics-list">
                        ${firstTimeCustomers.length > 0 ?
                            firstTimeCustomers.map(customer => `
                                <div class="analytics-item">
                                    <span class="customer-name">${customer.name}</span>
                                    <span class="customer-city">${customer.city}</span>
                                    <span class="customer-invoices">${customer.invoices} فاتورة</span>
                                    <span class="customer-amount">${customer.totalAmount.toLocaleString('ar-SA')} ر.س</span>
                                </div>
                            `).join('') :
                            '<div class="empty-analytics">لا توجد عملاء جدد</div>'
                        }
                    </div>
                </div>

                <div class="analytics-section">
                    <div class="analytics-header">
                        <h5><i class="fas fa-user-minus text-warning"></i> العملاء غير النشطين</h5>
                        <div class="analytics-actions">
                            <button onclick="debtManager.exportAnalyticsToExcel('inactiveCustomers')" class="export-btn excel-btn" title="تصدير Excel">
                                <i class="fas fa-file-excel"></i>
                            </button>
                            <button onclick="debtManager.printAnalyticsPDF('inactiveCustomers')" class="export-btn pdf-btn" title="طباعة PDF">
                                <i class="fas fa-file-pdf"></i>
                            </button>
                        </div>
                        <div class="analytics-stats">
                            <span class="stat-item">
                                <i class="fas fa-users"></i>
                                ${inactiveCustomersStats.count} عميل
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-file-invoice"></i>
                                ${inactiveCustomersStats.totalInvoices} فاتورة
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-money-bill-wave"></i>
                                ${inactiveCustomersStats.totalAmount.toLocaleString('ar-SA')} ر.س
                            </span>
                        </div>
                    </div>
                    <div class="analytics-list">
                        ${inactiveCustomers.length > 0 ?
                            inactiveCustomers.map(customer => `
                                <div class="analytics-item">
                                    <span class="customer-name">${customer.name}</span>
                                    <span class="customer-city">${customer.city}</span>
                                    <span class="customer-invoices">${customer.invoices} فاتورة</span>
                                    <span class="customer-amount">${customer.totalAmount.toLocaleString('ar-SA')} ر.س</span>
                                </div>
                            `).join('') :
                            '<div class="empty-analytics">جميع العملاء نشطين</div>'
                        }
                    </div>
                </div>
            </div>
        `;
    }

    // Load comparisons content
    loadComparisons() {
        const comparisonContent = document.getElementById('comparisonContent');

        // استخدام البيانات المفلترة أو الكاملة حسب حالة البحث
        const currentDebts = this.currentSearchTerm ? this.filteredDebts : this.debts;
        const currentPreviousDebts = this.currentSearchTerm ? this.filteredPreviousDebts : this.previousDebts;

        // Check if there's any data to compare
        if (currentDebts.length === 0 && currentPreviousDebts.length === 0) {
            const filterIndicator = this.currentSearchTerm ?
                `<div class="filter-indicator">
                    <i class="fas fa-filter"></i>
                    مفلتر حسب: "${this.currentSearchTerm}"
                    <button onclick="debtManager.clearSearch()" class="clear-filter-btn">
                        <i class="fas fa-times"></i> إلغاء الفلتر
                    </button>
                </div>` : '';

            comparisonContent.innerHTML = `
                ${filterIndicator}
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-balance-scale"></i>
                    </div>
                    <h5>لا توجد بيانات للمقارنة</h5>
                    <p>قم بإضافة فواتير جديدة أو استيراد ديون سابقة لرؤية المقارنات</p>
                    <div class="empty-state-actions">
                        <button onclick="document.getElementById('invoiceNumber').focus()" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة فاتورة جديدة
                        </button>
                    </div>
                </div>
            `;
            return;
        }

        const newTotal = currentDebts.reduce((sum, debt) => sum + debt.amount, 0);
        const previousTotal = currentPreviousDebts.reduce((sum, debt) => sum + debt.amount, 0);
        const difference = newTotal - previousTotal;
        const percentageChange = previousTotal > 0 ? ((difference / previousTotal) * 100) : 0;

        // إضافة مؤشر الفلترة
        const filterIndicator = this.currentSearchTerm ?
            `<div class="filter-indicator">
                <i class="fas fa-filter"></i>
                مفلتر حسب: "${this.currentSearchTerm}"
                <button onclick="debtManager.clearSearch()" class="clear-filter-btn">
                    <i class="fas fa-times"></i> إلغاء الفلتر
                </button>
            </div>` : '';

        comparisonContent.innerHTML = `
            ${filterIndicator}
            <div class="comparison-header">
                <h4><i class="fas fa-balance-scale"></i> مقارنة البيانات</h4>
                <div class="comparison-actions">
                    <button onclick="debtManager.exportAnalyticsToExcel('comparisons')" class="export-btn excel-btn" title="تصدير Excel">
                        <i class="fas fa-file-excel"></i>
                    </button>
                    <button onclick="debtManager.printAnalyticsPDF('comparisons')" class="export-btn pdf-btn" title="طباعة PDF">
                        <i class="fas fa-file-pdf"></i>
                    </button>
                </div>
            </div>
            <div class="comparison-grid">
                <div class="comparison-card">
                    <h5><i class="fas fa-file-invoice text-primary"></i> الفواتير الجديدة</h5>
                    <div class="comparison-stats">
                        <div class="stat-item">
                            <span class="stat-label">عدد الفواتير</span>
                            <span class="stat-value">${currentDebts.length}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">إجمالي المبلغ</span>
                            <span class="stat-value">${newTotal.toLocaleString('ar-SA')} ر.س</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">متوسط الفاتورة</span>
                            <span class="stat-value">${currentDebts.length > 0 ? Math.round(newTotal / currentDebts.length).toLocaleString('ar-SA') : 0} ر.س</span>
                        </div>
                    </div>
                </div>

                <div class="comparison-card">
                    <h5><i class="fas fa-history text-secondary"></i> الديون السابقة</h5>
                    <div class="comparison-stats">
                        <div class="stat-item">
                            <span class="stat-label">عدد الفواتير</span>
                            <span class="stat-value">${currentPreviousDebts.length}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">إجمالي المبلغ</span>
                            <span class="stat-value">${previousTotal.toLocaleString('ar-SA')} ر.س</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">متوسط الفاتورة</span>
                            <span class="stat-value">${currentPreviousDebts.length > 0 ? Math.round(previousTotal / currentPreviousDebts.length).toLocaleString('ar-SA') : 0} ر.س</span>
                        </div>
                    </div>
                </div>

                <div class="comparison-summary">
                    <h5><i class="fas fa-balance-scale"></i> النتيجة</h5>
                    <div class="simple-comparison">
                        <div class="comparison-row">
                            <span class="comparison-label">الفرق في المبلغ:</span>
                            <span class="comparison-value ${difference >= 0 ? 'positive' : 'negative'}">
                                ${difference >= 0 ? '+' : ''}${difference.toLocaleString('ar-SA')} ر.س
                            </span>
                        </div>
                        <div class="comparison-row">
                            <span class="comparison-label">نسبة التغيير:</span>
                            <span class="comparison-value ${percentageChange >= 0 ? 'positive' : 'negative'}">
                                ${percentageChange >= 0 ? '+' : ''}${percentageChange.toFixed(1)}%
                            </span>
                        </div>
                        <div class="comparison-row">
                            <span class="comparison-label">الفرق في عدد الفواتير:</span>
                            <span class="comparison-value ${(currentDebts.length - currentPreviousDebts.length) >= 0 ? 'positive' : 'negative'}">
                                ${(currentDebts.length - currentPreviousDebts.length) >= 0 ? '+' : ''}${currentDebts.length - currentPreviousDebts.length}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Load advanced statistics - الاستناد للديون الجديدة فقط
    loadAdvancedStats() {
        const statsContent = document.getElementById('statsContent');

        // استخدام الديون الجديدة فقط للإحصائيات المتقدمة
        const currentDebts = this.currentSearchTerm ? this.filteredDebts : this.debts;

        // الاستناد للديون الجديدة فقط
        const allDebts = [...currentDebts];

        // Check if there's any data for statistics
        if (allDebts.length === 0) {
            const filterIndicator = this.currentSearchTerm ?
                `<div class="filter-indicator">
                    <i class="fas fa-filter"></i>
                    مفلتر حسب: "${this.currentSearchTerm}"
                    <button onclick="debtManager.clearSearch()" class="clear-filter-btn">
                        <i class="fas fa-times"></i> إلغاء الفلتر
                    </button>
                </div>` : '';

            statsContent.innerHTML = `
                ${filterIndicator}
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <h5>لا توجد بيانات للإحصائيات</h5>
                    <p>قم بإضافة فواتير جديدة أو استيراد ديون سابقة لرؤية الإحصائيات المتقدمة</p>
                    <div class="empty-state-actions">
                        <button onclick="document.getElementById('invoiceNumber').focus()" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة فاتورة جديدة
                        </button>
                    </div>
                </div>
            `;
            return;
        }

        // Get top customers by amount - مع أرقام الفواتير
        const customerTotals = {};
        allDebts.forEach(debt => {
            const key = `${debt.customerName}_${debt.city}`;
            if (!customerTotals[key]) {
                customerTotals[key] = {
                    name: debt.customerName,
                    city: debt.city,
                    amount: 0,
                    invoices: 0,
                    invoiceNumbers: []
                };
            }
            customerTotals[key].amount += debt.amount;
            customerTotals[key].invoices += 1;
            customerTotals[key].invoiceNumbers.push(debt.invoiceNumber);
        });

        const topCustomers = Object.values(customerTotals)
            .sort((a, b) => b.amount - a.amount)
            .slice(0, 8); // زيادة العدد لاستغلال المساحة

        // Find largest and smallest debts - زيادة العدد
        const sortedDebts = allDebts.sort((a, b) => b.amount - a.amount);
        const largestDebts = sortedDebts.slice(0, 5); // زيادة العدد
        const smallestDebts = sortedDebts.slice(-5).reverse(); // زيادة العدد

        // إضافة مؤشر الفلترة فقط عند وجود بحث
        const filterIndicator = this.currentSearchTerm ?
            `<div class="filter-indicator">
                <i class="fas fa-filter"></i>
                مفلتر حسب: "${this.currentSearchTerm}"
                <button onclick="debtManager.clearSearch()" class="clear-filter-btn">
                    <i class="fas fa-times"></i> إلغاء الفلتر
                </button>
            </div>` : '';

        statsContent.innerHTML = `
            ${filterIndicator}
            <div class="stats-header">
                <h4><i class="fas fa-chart-bar"></i> الإحصائيات المتقدمة</h4>
                <div class="stats-actions">
                    <button onclick="debtManager.exportAnalyticsToExcel('statistics')" class="export-btn excel-btn" title="تصدير Excel">
                        <i class="fas fa-file-excel"></i>
                    </button>
                    <button onclick="debtManager.printAnalyticsPDF('statistics')" class="export-btn pdf-btn" title="طباعة PDF">
                        <i class="fas fa-file-pdf"></i>
                    </button>
                </div>
            </div>
            <div class="stats-grid">
                <div class="stats-section">
                    <h5><i class="fas fa-crown text-warning"></i> أكبر العملاء</h5>
                    <div class="top-customers-list">
                        ${topCustomers.map((customer, index) => `
                            <div class="top-customer-item">
                                <span class="customer-rank">${index + 1}</span>
                                <span class="customer-name" title="${customer.name}">${customer.name}</span>
                                <span class="customer-city">${customer.city}</span>
                                <span class="customer-invoices-list" title="الفواتير: ${customer.invoiceNumbers.join(', ')}">${customer.invoiceNumbers.join(', ')}</span>
                                <span class="customer-amount">${customer.amount.toLocaleString('ar-SA')} ر.س</span>
                                <span class="customer-invoices">${customer.invoices}ف</span>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <div class="stats-section-row">
                    <div class="stats-subsection">
                        <h6><i class="fas fa-arrow-up text-success"></i> أكبر الديون</h6>
                        <div class="debt-list">
                            ${largestDebts.map(debt => `
                                <div class="debt-stat-item">
                                    <div class="debt-info">
                                        <span class="debt-customer">${debt.customerName}</span>
                                        <span class="debt-invoice">فاتورة: ${debt.invoiceNumber}</span>
                                    </div>
                                    <span class="debt-amount">${debt.amount.toLocaleString('ar-SA')} ر.س</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div class="stats-subsection">
                        <h6><i class="fas fa-arrow-down text-info"></i> أصغر الديون</h6>
                        <div class="debt-list">
                            ${smallestDebts.map(debt => `
                                <div class="debt-stat-item">
                                    <div class="debt-info">
                                        <span class="debt-customer">${debt.customerName}</span>
                                        <span class="debt-invoice">فاتورة: ${debt.invoiceNumber}</span>
                                    </div>
                                    <span class="debt-amount">${debt.amount.toLocaleString('ar-SA')} ر.س</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Load differences content
    loadDifferences() {
        const differencesContent = document.getElementById('differencesContent');

        // استخدام البيانات المفلترة أو الكاملة حسب حالة البحث
        const currentDebts = this.currentSearchTerm ? this.filteredDebts : this.debts;
        const currentPreviousDebts = this.currentSearchTerm ? this.filteredPreviousDebts : this.previousDebts;

        // Check if there's any data to analyze differences
        if (currentDebts.length === 0 && currentPreviousDebts.length === 0) {
            const filterIndicator = this.currentSearchTerm ?
                `<div class="filter-indicator">
                    <i class="fas fa-filter"></i>
                    مفلتر حسب: "${this.currentSearchTerm}"
                    <button onclick="debtManager.clearSearch()" class="clear-filter-btn">
                        <i class="fas fa-times"></i> إلغاء الفلتر
                    </button>
                </div>` : '';

            differencesContent.innerHTML = `
                ${filterIndicator}
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <h5>لا توجد بيانات لحساب الفروقات</h5>
                    <p>قم بإضافة فواتير جديدة أو استيراد ديون سابقة لرؤية الفروقات</p>
                    <div class="empty-state-actions">
                        <button onclick="document.getElementById('invoiceNumber').focus()" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة فاتورة جديدة
                        </button>
                    </div>
                </div>
            `;
            return;
        }

        // Get unique customers from both datasets
        const newCustomers = this.getUniqueCustomers(currentDebts);
        const previousCustomers = this.getUniqueCustomers(currentPreviousDebts);

        // Find customers with differences
        const customerDifferences = [];

        // Check customers in both datasets
        newCustomers.forEach(newCustomer => {
            const previousCustomer = previousCustomers.find(prev =>
                prev.name === newCustomer.name && prev.city === newCustomer.city
            );

            if (previousCustomer) {
                const difference = newCustomer.totalAmount - previousCustomer.totalAmount;

                // Get invoice numbers for this customer
                const previousInvoices = currentPreviousDebts
                    .filter(debt => debt.customerName === newCustomer.name && debt.city === newCustomer.city)
                    .map(debt => debt.invoiceNumber);

                const newInvoices = currentDebts
                    .filter(debt => debt.customerName === newCustomer.name && debt.city === newCustomer.city)
                    .map(debt => debt.invoiceNumber);

                customerDifferences.push({
                    name: newCustomer.name,
                    city: newCustomer.city,
                    previousInvoiceNumbers: previousInvoices,
                    newInvoiceNumbers: newInvoices,
                    previousAmount: previousCustomer.totalAmount,
                    newAmount: newCustomer.totalAmount,
                    difference: difference
                });
            }
        });

        // تطبيق الفرز إذا كان نشطاً
        if (this.sortConfig.section === 'differences' && this.sortConfig.field) {
            this.sortArray(customerDifferences, this.sortConfig.field);
        } else {
            // الفرز الافتراضي: أكبر الفروقات أولاً
            customerDifferences.sort((a, b) => Math.abs(b.difference) - Math.abs(a.difference));
        }

        // Calculate totals
        const newTotal = currentDebts.reduce((sum, debt) => sum + debt.amount, 0);
        const previousTotal = currentPreviousDebts.reduce((sum, debt) => sum + debt.amount, 0);
        const totalDifference = newTotal - previousTotal;

        // إضافة مؤشر الفلترة
        const filterIndicator = this.currentSearchTerm ?
            `<div class="filter-indicator">
                <i class="fas fa-filter"></i>
                مفلتر حسب: "${this.currentSearchTerm}"
                <button onclick="debtManager.clearSearch()" class="clear-filter-btn">
                    <i class="fas fa-times"></i> إلغاء الفلتر
                </button>
            </div>` : '';

        differencesContent.innerHTML = `
            ${filterIndicator}
            <div class="differences-header">
                <h4><i class="fas fa-exchange-alt"></i> الفروقات</h4>
                <div class="differences-actions">
                    <button onclick="debtManager.exportAnalyticsToExcel('differences')" class="export-btn excel-btn" title="تصدير Excel">
                        <i class="fas fa-file-excel"></i>
                    </button>
                    <button onclick="debtManager.printAnalyticsPDF('differences')" class="export-btn pdf-btn" title="طباعة PDF">
                        <i class="fas fa-file-pdf"></i>
                    </button>
                </div>
            </div>
            <div class="differences-container">
                <!-- إجمالي الفروقات -->
                <div class="total-differences-summary">
                    <div class="summary-item">
                        <span class="summary-label">الديون السابقة:</span>
                        <span class="summary-value previous">${previousTotal.toLocaleString('ar-SA')} ر.س</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">الفواتير الجديدة:</span>
                        <span class="summary-value new">${newTotal.toLocaleString('ar-SA')} ر.س</span>
                    </div>
                    <div class="summary-item main">
                        <span class="summary-label">صافي الفرق:</span>
                        <span class="summary-value ${totalDifference >= 0 ? 'positive' : 'negative'}">
                            ${totalDifference >= 0 ? '+' : ''}${totalDifference.toLocaleString('ar-SA')} ر.س
                        </span>
                    </div>
                </div>

                <!-- جدول فروقات العملاء -->
                ${customerDifferences.length > 0 ? `
                    <div class="differences-table-container">
                        <table class="differences-table">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة السابق</th>
                                    <th>رقم الفاتورة الجديد</th>
                                    <th class="sortable-header" data-sort-field="customerName" data-sort-section="differences" onclick="debtManager.sortData('customerName', 'differences')">
                                        اسم العميل <i class="sort-icon fas fa-sort"></i>
                                    </th>
                                    <th class="sortable-header" data-sort-field="city" data-sort-section="differences" onclick="debtManager.sortData('city', 'differences')">
                                        المدينة <i class="sort-icon fas fa-sort"></i>
                                    </th>
                                    <th class="sortable-header" data-sort-field="previousAmount" data-sort-section="differences" onclick="debtManager.sortData('previousAmount', 'differences')">
                                        المبلغ السابق <i class="sort-icon fas fa-sort"></i>
                                    </th>
                                    <th class="sortable-header" data-sort-field="newAmount" data-sort-section="differences" onclick="debtManager.sortData('newAmount', 'differences')">
                                        المبلغ الجديد <i class="sort-icon fas fa-sort"></i>
                                    </th>
                                    <th class="sortable-header" data-sort-field="difference" data-sort-section="differences" onclick="debtManager.sortData('difference', 'differences')">
                                        الفرق <i class="sort-icon fas fa-sort"></i>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                ${customerDifferences.map(customer => `
                                    <tr>
                                        <td class="invoice-numbers">${customer.previousInvoiceNumbers.join(', ')}</td>
                                        <td class="invoice-numbers">${customer.newInvoiceNumbers.join(', ')}</td>
                                        <td class="customer-name">${customer.name}</td>
                                        <td class="customer-city">${customer.city}</td>
                                        <td class="amount previous">${customer.previousAmount.toLocaleString('ar-SA')} ر.س</td>
                                        <td class="amount new">${customer.newAmount.toLocaleString('ar-SA')} ر.س</td>
                                        <td class="difference ${customer.difference >= 0 ? 'positive' : 'negative'}">
                                            ${customer.difference >= 0 ? '+' : ''}${customer.difference.toLocaleString('ar-SA')} ر.س
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                ` : `
                    <div class="empty-differences">
                        <i class="fas fa-info-circle"></i>
                        <p>لا توجد عملاء مشتركين بين الفواتير الجديدة والديون السابقة</p>
                    </div>
                `}
            </div>
        `;

        // تحديث أيقونات الفرز بعد تحميل المحتوى
        setTimeout(() => {
            if (this.sortConfig.section === 'differences' && this.sortConfig.field) {
                this.updateSortIcons(this.sortConfig.field, this.sortConfig.section);
            }
        }, 100);
    }

    // Helper function to get unique customers
    getUniqueCustomers(debts) {
        const customers = {};
        debts.forEach(debt => {
            const key = `${debt.customerName}_${debt.city}`;
            if (!customers[key]) {
                customers[key] = {
                    name: debt.customerName,
                    city: debt.city,
                    totalAmount: 0,
                    invoices: 0
                };
            }
            customers[key].totalAmount += debt.amount;
            customers[key].invoices += 1;
        });
        return Object.values(customers);
    }

    // Refresh analytics if currently visible
    refreshAnalytics() {
        const analyticsContent = document.getElementById('analyticsContent');
        const comparisonContent = document.getElementById('comparisonContent');
        const statsContent = document.getElementById('statsContent');
        const differencesContent = document.getElementById('differencesContent');

        // Check which section is currently visible and refresh it
        if (analyticsContent && analyticsContent.style.display === 'block' && analyticsContent.style.visibility !== 'hidden') {
            this.loadAnalytics();
        } else if (comparisonContent && comparisonContent.style.display === 'block' && comparisonContent.style.visibility !== 'hidden') {
            this.loadComparisons();
        } else if (statsContent && statsContent.style.display === 'block' && statsContent.style.visibility !== 'hidden') {
            this.loadAdvancedStats();
        } else if (differencesContent && differencesContent.style.display === 'block' && differencesContent.style.visibility !== 'hidden') {
            this.loadDifferences();
        } else {
            // Check which button is active to determine current section
            const activeBtn = document.querySelector('.nav-btn.active');
            if (activeBtn) {
                const btnId = activeBtn.id;
                if (btnId === 'analyticsNavBtn') {
                    this.loadAnalytics();
                } else if (btnId === 'comparisonsNavBtn') {
                    this.loadComparisons();
                } else if (btnId === 'statisticsNavBtn') {
                    this.loadAdvancedStats();
                } else if (btnId === 'differencesNavBtn') {
                    this.loadDifferences();
                }
            } else {
                // Default to analytics if no active button found
                this.showSection('analytics');
            }
        }
    }

    // Clear search and show all data
    clearSearch() {
        console.log('🔄 إلغاء البحث وإرجاع جميع البيانات');

        try {
            // Clear search input
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.value = '';
            }

            // Reset search term and filtered data
            this.currentSearchTerm = '';
            this.filteredDebts = [...this.debts];
            this.filteredPreviousDebts = [...this.previousDebts];

            // إعادة تعيين الفرز
            this.resetSort();

            // Update displays with all original data
            this.displayRecords(this.debts);
            this.displayPreviousDebt(this.previousDebts);
            this.showSearchResults(null);

            // Update statistics with full data
            this.updateStatistics();
            this.refreshAnalytics();

            // Hide search info
            const searchInfo = document.getElementById('searchInfo');
            if (searchInfo) {
                searchInfo.style.display = 'none';
            }

            // Hide clear search button
            const clearBtn = document.getElementById('clearSearchBtn');
            if (clearBtn) {
                clearBtn.style.display = 'none';
            }

            // Show success message
            this.showSuccess('تم إلغاء البحث وإرجاع جميع البيانات إلى طبيعتها');

            console.log('✅ تم إلغاء البحث بنجاح');
        } catch (error) {
            console.error('❌ خطأ في إلغاء البحث:', error);
            this.showError('حدث خطأ أثناء إلغاء البحث');
        }
    }

    // Sort data function
    sortData(field, section) {
        console.log(`🔄 فرز البيانات: ${field} في قسم ${section}`);

        try {
            // التحقق من صحة المعاملات
            if (!field || !section) {
                console.error('❌ معاملات فرز غير صالحة:', { field, section });
                return;
            }

            // تحديد الاتجاه
            if (this.sortConfig.field === field && this.sortConfig.section === section) {
                // إذا كان نفس الحقل، اعكس الاتجاه
                this.sortConfig.direction = this.sortConfig.direction === 'asc' ? 'desc' : 'asc';
            } else {
                // حقل جديد، ابدأ بالتصاعدي
                this.sortConfig.direction = 'asc';
            }

            this.sortConfig.field = field;
            this.sortConfig.section = section;

            // تطبيق الفرز حسب القسم
            if (section === 'new') {
                if (!this.filteredDebts || this.filteredDebts.length === 0) {
                    console.warn('⚠️ لا توجد فواتير جديدة للفرز');
                    return;
                }
                console.log(`📊 فرز ${this.filteredDebts.length} فاتورة جديدة`);
                this.sortArray(this.filteredDebts, field);
                this.displayRecords(this.filteredDebts);
            } else if (section === 'previous') {
                if (!this.filteredPreviousDebts || this.filteredPreviousDebts.length === 0) {
                    console.warn('⚠️ لا توجد ديون سابقة للفرز');
                    return;
                }
                console.log(`📊 فرز ${this.filteredPreviousDebts.length} دين سابق`);
                this.sortArray(this.filteredPreviousDebts, field);
                this.displayPreviousDebt(this.filteredPreviousDebts);
            } else if (section === 'differences') {
                console.log('📊 إعادة تحميل الفروقات مع الفرز');
                this.loadDifferences(); // إعادة تحميل مع الفرز
            } else {
                console.error('❌ قسم فرز غير معروف:', section);
                return;
            }

            // تحديث أيقونات الفرز
            this.updateSortIcons(field, section);

            console.log(`✅ تم الفرز بنجاح: ${field} ${this.sortConfig.direction} في ${section}`);

        } catch (error) {
            console.error('❌ خطأ في عملية الفرز:', error);
            this.showError('حدث خطأ أثناء فرز البيانات');
        }
    }

    // Export analytics to Excel
    exportAnalyticsToExcel(section) {
        console.log(`📊 تصدير ${section} إلى Excel`);

        let data = [];
        let filename = '';
        let headers = [];

        switch (section) {
            case 'newCustomers':
                const currentDebts = this.currentSearchTerm ? this.filteredDebts : this.debts;
                const currentPreviousDebts = this.currentSearchTerm ? this.filteredPreviousDebts : this.previousDebts;

                const newCustomers = this.getUniqueCustomers(currentDebts);
                const previousCustomers = this.getUniqueCustomers(currentPreviousDebts);

                const firstTimeCustomers = newCustomers.filter(newCustomer =>
                    !previousCustomers.some(prevCustomer =>
                        prevCustomer.name === newCustomer.name && prevCustomer.city === newCustomer.city
                    )
                );

                headers = ['اسم العميل', 'المدينة', 'عدد الفواتير', 'إجمالي المبلغ'];
                data = firstTimeCustomers.map(customer => [
                    customer.name,
                    customer.city,
                    customer.invoices,
                    customer.totalAmount
                ]);
                filename = 'العملاء_الجدد';
                break;

            case 'inactiveCustomers':
                const currentDebts2 = this.currentSearchTerm ? this.filteredDebts : this.debts;
                const currentPreviousDebts2 = this.currentSearchTerm ? this.filteredPreviousDebts : this.previousDebts;

                const newCustomers2 = this.getUniqueCustomers(currentDebts2);
                const previousCustomers2 = this.getUniqueCustomers(currentPreviousDebts2);

                const inactiveCustomers = previousCustomers2.filter(prevCustomer =>
                    !newCustomers2.some(newCustomer =>
                        newCustomer.name === prevCustomer.name && newCustomer.city === prevCustomer.city
                    )
                );

                headers = ['اسم العميل', 'المدينة', 'عدد الفواتير', 'إجمالي المبلغ'];
                data = inactiveCustomers.map(customer => [
                    customer.name,
                    customer.city,
                    customer.invoices,
                    customer.totalAmount
                ]);
                filename = 'العملاء_غير_النشطين';
                break;

            case 'comparisons':
                const currentDebts3 = this.currentSearchTerm ? this.filteredDebts : this.debts;
                const currentPreviousDebts3 = this.currentSearchTerm ? this.filteredPreviousDebts : this.previousDebts;

                const newTotal = currentDebts3.reduce((sum, debt) => sum + debt.amount, 0);
                const previousTotal = currentPreviousDebts3.reduce((sum, debt) => sum + debt.amount, 0);
                const difference = newTotal - previousTotal;
                const percentageChange = previousTotal > 0 ? ((difference / previousTotal) * 100) : 0;

                headers = ['البيان', 'الفواتير الجديدة', 'الديون السابقة', 'الفرق', 'نسبة التغيير'];
                data = [
                    ['عدد الفواتير', currentDebts3.length, currentPreviousDebts3.length, currentDebts3.length - currentPreviousDebts3.length, ''],
                    ['إجمالي المبلغ', newTotal, previousTotal, difference, percentageChange.toFixed(1) + '%'],
                    ['متوسط الفاتورة',
                     currentDebts3.length > 0 ? Math.round(newTotal / currentDebts3.length) : 0,
                     currentPreviousDebts3.length > 0 ? Math.round(previousTotal / currentPreviousDebts3.length) : 0,
                     '', '']
                ];
                filename = 'مقارنة_البيانات';
                break;

            case 'statistics':
                const allDebts = [...(this.currentSearchTerm ? this.filteredDebts : this.debts),
                                 ...(this.currentSearchTerm ? this.filteredPreviousDebts : this.previousDebts)];

                const customerTotals = {};
                allDebts.forEach(debt => {
                    const key = `${debt.customerName}_${debt.city}`;
                    if (!customerTotals[key]) {
                        customerTotals[key] = {
                            name: debt.customerName,
                            city: debt.city,
                            amount: 0,
                            invoices: 0
                        };
                    }
                    customerTotals[key].amount += debt.amount;
                    customerTotals[key].invoices += 1;
                });

                const topCustomers = Object.values(customerTotals)
                    .sort((a, b) => b.amount - a.amount);

                headers = ['الترتيب', 'اسم العميل', 'المدينة', 'أرقام الفواتير', 'عدد الفواتير', 'إجمالي المبلغ'];
                data = topCustomers.map((customer, index) => [
                    index + 1,
                    customer.name,
                    customer.city,
                    customer.invoiceNumbers.join(', '),
                    customer.invoices,
                    customer.amount
                ]);
                filename = 'الإحصائيات_المتقدمة';
                break;

            case 'differences':
                const currentDebts4 = this.currentSearchTerm ? this.filteredDebts : this.debts;
                const currentPreviousDebts4 = this.currentSearchTerm ? this.filteredPreviousDebts : this.previousDebts;

                const newCustomers4 = this.getUniqueCustomers(currentDebts4);
                const previousCustomers4 = this.getUniqueCustomers(currentPreviousDebts4);

                const customerDifferences = [];
                newCustomers4.forEach(newCustomer => {
                    const previousCustomer = previousCustomers4.find(prev =>
                        prev.name === newCustomer.name && prev.city === newCustomer.city
                    );

                    if (previousCustomer) {
                        const difference = newCustomer.totalAmount - previousCustomer.totalAmount;
                        customerDifferences.push({
                            name: newCustomer.name,
                            city: newCustomer.city,
                            previousAmount: previousCustomer.totalAmount,
                            newAmount: newCustomer.totalAmount,
                            difference: difference
                        });
                    }
                });

                headers = ['اسم العميل', 'المدينة', 'المبلغ السابق', 'المبلغ الجديد', 'الفرق'];
                data = customerDifferences.map(customer => [
                    customer.name,
                    customer.city,
                    customer.previousAmount,
                    customer.newAmount,
                    customer.difference
                ]);
                filename = 'الفروقات';
                break;
        }

        if (data.length === 0) {
            this.showError('لا توجد بيانات للتصدير');
            return;
        }

        // Create Excel file
        this.createExcelFile(data, headers, filename);
        this.showSuccess(`تم تصدير ${filename} إلى Excel بنجاح`);
    }

    // Print analytics to PDF
    printAnalyticsPDF(section) {
        console.log(`🖨️ طباعة ${section} إلى PDF`);

        let title = '';
        let data = [];
        let headers = [];

        switch (section) {
            case 'newCustomers':
                title = 'تقرير العملاء الجدد';
                const currentDebts = this.currentSearchTerm ? this.filteredDebts : this.debts;
                const currentPreviousDebts = this.currentSearchTerm ? this.filteredPreviousDebts : this.previousDebts;

                const newCustomers = this.getUniqueCustomers(currentDebts);
                const previousCustomers = this.getUniqueCustomers(currentPreviousDebts);

                const firstTimeCustomers = newCustomers.filter(newCustomer =>
                    !previousCustomers.some(prevCustomer =>
                        prevCustomer.name === newCustomer.name && prevCustomer.city === newCustomer.city
                    )
                );

                headers = ['اسم العميل', 'المدينة', 'عدد الفواتير', 'إجمالي المبلغ'];
                data = firstTimeCustomers.map(customer => [
                    customer.name,
                    customer.city,
                    customer.invoices,
                    customer.totalAmount.toLocaleString('ar-SA') + ' ر.س'
                ]);
                break;

            case 'inactiveCustomers':
                title = 'تقرير العملاء غير النشطين';
                const currentDebts2 = this.currentSearchTerm ? this.filteredDebts : this.debts;
                const currentPreviousDebts2 = this.currentSearchTerm ? this.filteredPreviousDebts : this.previousDebts;

                const newCustomers2 = this.getUniqueCustomers(currentDebts2);
                const previousCustomers2 = this.getUniqueCustomers(currentPreviousDebts2);

                const inactiveCustomers = previousCustomers2.filter(prevCustomer =>
                    !newCustomers2.some(newCustomer =>
                        newCustomer.name === prevCustomer.name && newCustomer.city === prevCustomer.city
                    )
                );

                headers = ['اسم العميل', 'المدينة', 'عدد الفواتير', 'إجمالي المبلغ'];
                data = inactiveCustomers.map(customer => [
                    customer.name,
                    customer.city,
                    customer.invoices,
                    customer.totalAmount.toLocaleString('ar-SA') + ' ر.س'
                ]);
                break;

            case 'comparisons':
                title = 'تقرير مقارنة البيانات';
                const currentDebts3 = this.currentSearchTerm ? this.filteredDebts : this.debts;
                const currentPreviousDebts3 = this.currentSearchTerm ? this.filteredPreviousDebts : this.previousDebts;

                const newTotal = currentDebts3.reduce((sum, debt) => sum + debt.amount, 0);
                const previousTotal = currentPreviousDebts3.reduce((sum, debt) => sum + debt.amount, 0);
                const difference = newTotal - previousTotal;
                const percentageChange = previousTotal > 0 ? ((difference / previousTotal) * 100) : 0;

                headers = ['البيان', 'الفواتير الجديدة', 'الديون السابقة', 'الفرق', 'نسبة التغيير'];
                data = [
                    ['عدد الفواتير', currentDebts3.length, currentPreviousDebts3.length, currentDebts3.length - currentPreviousDebts3.length, ''],
                    ['إجمالي المبلغ', newTotal.toLocaleString('ar-SA') + ' ر.س', previousTotal.toLocaleString('ar-SA') + ' ر.س',
                     (difference >= 0 ? '+' : '') + difference.toLocaleString('ar-SA') + ' ر.س',
                     (percentageChange >= 0 ? '+' : '') + percentageChange.toFixed(1) + '%'],
                    ['متوسط الفاتورة',
                     (currentDebts3.length > 0 ? Math.round(newTotal / currentDebts3.length).toLocaleString('ar-SA') : '0') + ' ر.س',
                     (currentPreviousDebts3.length > 0 ? Math.round(previousTotal / currentPreviousDebts3.length).toLocaleString('ar-SA') : '0') + ' ر.س',
                     '', '']
                ];
                break;

            case 'statistics':
                title = 'تقرير الإحصائيات المتقدمة (الديون الجديدة فقط)';
                const allDebts = [...(this.currentSearchTerm ? this.filteredDebts : this.debts)];

                const customerTotals = {};
                allDebts.forEach(debt => {
                    const key = `${debt.customerName}_${debt.city}`;
                    if (!customerTotals[key]) {
                        customerTotals[key] = {
                            name: debt.customerName,
                            city: debt.city,
                            amount: 0,
                            invoices: 0,
                            invoiceNumbers: []
                        };
                    }
                    customerTotals[key].amount += debt.amount;
                    customerTotals[key].invoices += 1;
                    customerTotals[key].invoiceNumbers.push(debt.invoiceNumber);
                });

                const topCustomers = Object.values(customerTotals)
                    .sort((a, b) => b.amount - a.amount);

                headers = ['الترتيب', 'اسم العميل', 'المدينة', 'عدد الفواتير', 'إجمالي المبلغ'];
                data = topCustomers.map((customer, index) => [
                    index + 1,
                    customer.name,
                    customer.city,
                    customer.invoices,
                    customer.amount.toLocaleString('ar-SA') + ' ر.س'
                ]);
                break;

            case 'differences':
                title = 'تقرير الفروقات';
                const currentDebts4 = this.currentSearchTerm ? this.filteredDebts : this.debts;
                const currentPreviousDebts4 = this.currentSearchTerm ? this.filteredPreviousDebts : this.previousDebts;

                const newCustomers4 = this.getUniqueCustomers(currentDebts4);
                const previousCustomers4 = this.getUniqueCustomers(currentPreviousDebts4);

                const customerDifferences = [];
                newCustomers4.forEach(newCustomer => {
                    const previousCustomer = previousCustomers4.find(prev =>
                        prev.name === newCustomer.name && prev.city === newCustomer.city
                    );

                    if (previousCustomer) {
                        const difference = newCustomer.totalAmount - previousCustomer.totalAmount;
                        customerDifferences.push({
                            name: newCustomer.name,
                            city: newCustomer.city,
                            previousAmount: previousCustomer.totalAmount,
                            newAmount: newCustomer.totalAmount,
                            difference: difference
                        });
                    }
                });

                headers = ['اسم العميل', 'المدينة', 'المبلغ السابق', 'المبلغ الجديد', 'الفرق'];
                data = customerDifferences.map(customer => [
                    customer.name,
                    customer.city,
                    customer.previousAmount.toLocaleString('ar-SA') + ' ر.س',
                    customer.newAmount.toLocaleString('ar-SA') + ' ر.س',
                    (customer.difference >= 0 ? '+' : '') + customer.difference.toLocaleString('ar-SA') + ' ر.س'
                ]);
                break;
        }

        if (data.length === 0) {
            this.showError('لا توجد بيانات للطباعة');
            return;
        }

        // Create and print PDF
        this.createAnalyticsPDF(data, headers, title);
    }

    // Create analytics PDF with original Arabic text - استخدام الحل الجديد
    createAnalyticsPDF(data, headers, title) {
        try {
            console.log('🖨️ إنشاء PDF بالنص العربي الأصلي...');

            // استخدام الحل الجديد بدلاً من html2canvas
            this.openAnalyticsPrintPage(data, headers, title);

        } catch (error) {
            console.error('Error creating PDF file:', error);
            this.showError('حدث خطأ أثناء إنشاء ملف PDF');
        }
    }

    // وظيفة فتح صفحة طباعة للتحليلات - الحل الجديد
    openAnalyticsPrintPage(data, headers, title) {
        console.log('🖨️ فتح صفحة طباعة التحليلات...');

        try {
            const printHTML = this.createAnalyticsHTML(data, headers, title);

            if (!printHTML) {
                throw new Error('فشل في إنشاء HTML للطباعة');
            }

            // فتح نافذة جديدة
            const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');

            if (!printWindow) {
                throw new Error('فشل في فتح نافذة الطباعة - تأكد من السماح للنوافذ المنبثقة');
            }

            // كتابة HTML في النافذة الجديدة
            printWindow.document.write(printHTML);
            printWindow.document.close();

            // التركيز على النافذة الجديدة
            printWindow.focus();

            console.log('✅ تم فتح صفحة طباعة التحليلات بنجاح');

        } catch (error) {
            console.error('❌ خطأ في فتح صفحة طباعة التحليلات:', error);
            this.showError('فشل في فتح صفحة الطباعة: ' + error.message);
        }
    }

    // إنشاء HTML للتحليلات
    createAnalyticsHTML(data, headers, title) {
        try {
            // التاريخ الحالي
            const currentDate = new Date().toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            });

            // إنشاء HTML للطباعة
            let printHTML = '<!DOCTYPE html><html lang="ar" dir="rtl"><head><meta charset="UTF-8"><title>' + title + '</title>';
            printHTML += '<style>@media print {@page {size: A4; margin: 15mm;} body {margin: 0; padding: 0; font-family: Arial, sans-serif; font-size: 12pt; direction: rtl; text-align: right;} .no-print {display: none !important;}}';
            printHTML += 'body {font-family: Arial, sans-serif; direction: rtl; text-align: right; margin: 0; padding: 20px; background: #fff; color: #000; font-size: 14px;}';
            printHTML += '.header {text-align: center; margin-bottom: 30px; border-bottom: 3px solid #2c3e50; padding-bottom: 20px;}';
            printHTML += '.title {font-size: 28px; font-weight: bold; color: #2c3e50; margin-bottom: 10px;}';
            printHTML += '.date {font-size: 16px; color: #666; margin-bottom: 10px;}';
            printHTML += '.analytics-table {width: 100%; border-collapse: collapse; margin: 20px 0; font-size: 14px;}';
            printHTML += '.analytics-table th {background: #2c3e50; color: #fff; padding: 15px 10px; text-align: center; font-weight: bold; border: 2px solid #34495e; font-size: 16px;}';
            printHTML += '.analytics-table td {padding: 12px 10px; text-align: center; border: 1px solid #ddd; vertical-align: middle;}';
            printHTML += '.analytics-table tr:nth-child(even) {background: #f8f9fa;} .analytics-table tr:nth-child(odd) {background: #fff;}';
            printHTML += '.print-button {background: #27ae60; color: white; border: none; padding: 15px 30px; font-size: 18px; border-radius: 8px; cursor: pointer; margin: 20px; font-weight: bold;}';
            printHTML += '.buttons-container {text-align: center; margin: 30px 0; padding: 20px; background: #f8f9fa; border-radius: 10px;}';
            printHTML += '@media print {.buttons-container {display: none !important;}}</style></head><body>';

            printHTML += '<div class="buttons-container no-print">';
            printHTML += '<button class="print-button" onclick="window.print()">🖨️ طباعة</button>';
            printHTML += '<button class="print-button" onclick="window.print();" style="background: #e74c3c;">💾 حفظ PDF</button>';
            printHTML += '<button class="print-button" onclick="window.close()" style="background: #95a5a6;">❌ إغلاق</button></div>';

            printHTML += '<div class="header"><div class="title">' + title + '</div><div class="date">' + currentDate + '</div></div>';

            printHTML += '<table class="analytics-table"><thead><tr>';

            // إضافة العناوين
            headers.forEach(header => {
                printHTML += '<th>' + header + '</th>';
            });
            printHTML += '</tr></thead><tbody>';

            // إضافة البيانات
            data.forEach((row, index) => {
                printHTML += '<tr>';
                if (Array.isArray(row)) {
                    row.forEach(cell => {
                        printHTML += '<td>' + (cell || '') + '</td>';
                    });
                } else {
                    // إذا كانت البيانات كائن
                    headers.forEach(header => {
                        printHTML += '<td>' + (row[header] || '') + '</td>';
                    });
                }
                printHTML += '</tr>';
            });

            printHTML += '</tbody></table></body></html>';

            return printHTML;

        } catch (error) {
            console.error('❌ خطأ في إنشاء HTML للتحليلات:', error);
            return null;
        }
    }

    // Create HTML table for PDF conversion - عرض البيانات كما هي
    createHTMLTableForPDF(data, headers, title) {
        const currentDate = new Date().toLocaleDateString('en-GB'); // تاريخ ميلادي

        let html = `
            <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: rtl; text-align: right; padding: 20px; background: white;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #2c3e50; font-size: 24px; margin-bottom: 10px; direction: rtl;">${title}</h1>
                    <p style="color: #7f8c8d; font-size: 14px; direction: rtl;">تاريخ التقرير: ${currentDate}</p>
                </div>

                <table style="width: 100%; border-collapse: collapse; font-size: 12px; margin: 20px 0;">
                    <thead>
                        <tr style="background: linear-gradient(135deg, #3498db, #2980b9); color: white;">
        `;

        // Add headers with proper direction
        headers.forEach(header => {
            const isArabic = /[\u0600-\u06FF]/.test(header);
            const direction = isArabic ? 'rtl' : 'ltr';
            html += `<th style="padding: 12px 8px; border: 1px solid #bdc3c7; text-align: center; font-weight: bold; direction: ${direction};">${header}</th>`;
        });

        html += `
                        </tr>
                    </thead>
                    <tbody>
        `;

        // Add data rows - عرض البيانات كما هي بدون تحويل
        data.forEach((row, index) => {
            const bgColor = index % 2 === 0 ? '#f8f9fa' : '#ffffff';
            html += `<tr style="background: ${bgColor};">`;

            row.forEach(cell => {
                // تحديد اتجاه النص بناءً على المحتوى
                const isArabic = /[\u0600-\u06FF]/.test(cell);
                const direction = isArabic ? 'rtl' : 'ltr';
                const cellContent = cell || '-'; // عرض البيانات كما هي

                html += `<td style="padding: 10px 8px; border: 1px solid #bdc3c7; text-align: center; direction: ${direction}; white-space: nowrap;">${cellContent}</td>`;
            });

            html += `</tr>`;
        });

        html += `
                    </tbody>
                </table>

                <div style="text-align: center; margin-top: 30px; color: #7f8c8d; font-size: 12px; direction: rtl;">
                    <p>تم إنشاء هذا التقرير بواسطة نظام إدارة ديون العملاء</p>
                </div>
            </div>
        `;

        return html;
    }

    // Convert HTML to PDF using html2canvas - محسن للنصوص العربية
    convertHTMLToPDF(htmlContent, title) {
        // Create temporary div
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = htmlContent;
        tempDiv.style.position = 'absolute';
        tempDiv.style.left = '-9999px';
        tempDiv.style.top = '-9999px';
        tempDiv.style.width = '1000px'; // عرض أكبر لجودة أفضل
        tempDiv.style.background = 'white';
        tempDiv.style.fontFamily = 'Segoe UI, Tahoma, Arial, sans-serif';
        tempDiv.style.direction = 'rtl';
        document.body.appendChild(tempDiv);

        // Convert to canvas then PDF with enhanced settings
        html2canvas(tempDiv, {
            scale: 2.5, // جودة عالية جداً للنصوص العربية
            useCORS: true,
            allowTaint: true,
            backgroundColor: '#ffffff',
            width: 1000,
            height: tempDiv.scrollHeight,
            scrollX: 0,
            scrollY: 0,
            windowWidth: 1200,
            windowHeight: tempDiv.scrollHeight,
            removeContainer: true,
            imageTimeout: 12000,
            logging: false,
            foreignObjectRendering: true,
            onclone: function(clonedDoc) {
                // تحسين عرض النصوص العربية في النسخة المستنسخة
                const style = clonedDoc.createElement('style');
                style.textContent = `
                    * {
                        font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif !important;
                        -webkit-font-smoothing: antialiased;
                        -moz-osx-font-smoothing: grayscale;
                        text-rendering: optimizeLegibility;
                        font-feature-settings: "liga", "kern";
                    }
                    table {
                        border-collapse: collapse !important;
                        font-variant-numeric: tabular-nums;
                    }
                    th, td {
                        border: 1px solid #ddd !important;
                        text-align: center !important;
                        white-space: nowrap;
                        overflow: visible;
                    }
                    .arabic-text {
                        direction: rtl !important;
                        unicode-bidi: bidi-override !important;
                    }
                    [dir="rtl"] {
                        direction: rtl !important;
                        text-align: center !important;
                    }
                    [dir="ltr"] {
                        direction: ltr !important;
                        text-align: center !important;
                    }
                `;
                clonedDoc.head.appendChild(style);

                // تطبيق التحسينات على جميع العناصر
                const allElements = clonedDoc.querySelectorAll('*');
                allElements.forEach(el => {
                    if (el.textContent && /[\u0600-\u06FF]/.test(el.textContent)) {
                        el.style.direction = 'rtl';
                        el.style.unicodeBidi = 'bidi-override';
                        el.classList.add('arabic-text');
                    }
                });
            }
        }).then(canvas => {
            // Remove temporary div
            document.body.removeChild(tempDiv);

            // Create PDF with enhanced quality
            const { jsPDF } = window.jspdf;
            const pdf = new jsPDF('p', 'mm', 'a4');

            // تحسين جودة الصورة
            const imgData = canvas.toDataURL('image/jpeg', 0.98);

            const imgWidth = 210; // A4 width in mm
            const pageHeight = 295; // A4 height in mm
            const margin = 5; // هوامش صغيرة
            const contentWidth = imgWidth - (2 * margin);
            const contentHeight = pageHeight - (2 * margin);
            const imgHeight = (canvas.height * contentWidth) / canvas.width;
            let heightLeft = imgHeight;
            let position = 0;
            let pageCount = 1;

            // Add first page
            pdf.addImage(imgData, 'JPEG', margin, margin, contentWidth, Math.min(imgHeight, contentHeight));
            heightLeft -= contentHeight;

            // Add additional pages if needed
            while (heightLeft > 0) {
                position = heightLeft - imgHeight;
                pdf.addPage();
                pdf.addImage(imgData, 'JPEG', margin, position + margin, contentWidth, imgHeight);
                heightLeft -= contentHeight;
                pageCount++;
            }

            // Save PDF with metadata
            const filename = `${title.replace(/[^\w\s-]/g, '')}_${new Date().toISOString().split('T')[0]}.pdf`;

            pdf.setProperties({
                title: title,
                creator: 'نظام إدارة الديون',
                producer: 'نظام إدارة الديون المحسن'
            });

            pdf.save(filename);

            console.log(`✅ تم إنشاء PDF بالعربية: ${filename} (${pageCount} صفحة)`);
            this.showSuccess(`تم إنشاء ${title} بنجاح (${pageCount} صفحة)`);

        }).catch(error => {
            // Remove temporary div if error occurs
            if (document.body.contains(tempDiv)) {
                document.body.removeChild(tempDiv);
            }
            console.error('Error converting HTML to PDF:', error);
            this.showError('حدث خطأ أثناء تحويل HTML إلى PDF');
        });
    }



    // Create Excel file
    createExcelFile(data, headers, filename) {
        try {
            // Create workbook
            const wb = XLSX.utils.book_new();

            // Prepare data with headers
            const wsData = [headers, ...data];

            // Create worksheet
            const ws = XLSX.utils.aoa_to_sheet(wsData);

            // Set column widths
            const colWidths = headers.map(() => ({ wch: 20 }));
            ws['!cols'] = colWidths;

            // Add worksheet to workbook
            XLSX.utils.book_append_sheet(wb, ws, 'البيانات');

            // Save file
            const excelFilename = `${filename}_${new Date().toISOString().split('T')[0]}.xlsx`;
            XLSX.writeFile(wb, excelFilename);

            console.log(`✅ Excel file created: ${excelFilename}`);
        } catch (error) {
            console.error('Error creating Excel file:', error);
            this.showError('حدث خطأ أثناء إنشاء ملف Excel');
        }
    }

    // Sort array helper function
    sortArray(array, field) {
        console.log(`🔄 فرز المصفوفة: ${field} - ${this.sortConfig.direction}`);
        console.log(`📊 عدد العناصر قبل الفرز: ${array.length}`);

        array.sort((a, b) => {
            let valueA, valueB;

            try {
                switch (field) {
                    case 'customerName':
                        // للفروقات: استخدم name، للبيانات العادية: استخدم customerName
                        valueA = this.normalizeString(a.name || a.customerName || '');
                        valueB = this.normalizeString(b.name || b.customerName || '');
                        break;
                    case 'amount':
                        valueA = this.normalizeNumber(a.amount);
                        valueB = this.normalizeNumber(b.amount);
                        break;
                    case 'city':
                        valueA = this.normalizeString(a.city || '');
                        valueB = this.normalizeString(b.city || '');
                        break;
                    case 'invoiceNumber':
                        valueA = this.normalizeInvoiceNumber(a.invoiceNumber || '');
                        valueB = this.normalizeInvoiceNumber(b.invoiceNumber || '');
                        break;
                    case 'date':
                        valueA = this.normalizeDate(a.date);
                        valueB = this.normalizeDate(b.date);
                        break;
                    case 'difference':
                        valueA = this.normalizeNumber(a.difference);
                        valueB = this.normalizeNumber(b.difference);
                        break;
                    case 'previousAmount':
                        valueA = this.normalizeNumber(a.previousAmount);
                        valueB = this.normalizeNumber(b.previousAmount);
                        break;
                    case 'newAmount':
                        valueA = this.normalizeNumber(a.newAmount);
                        valueB = this.normalizeNumber(b.newAmount);
                        break;
                    default:
                        console.warn(`⚠️ حقل فرز غير معروف: ${field}`);
                        return 0;
                }

                // تطبيق الفرز
                let result;
                if (field === 'invoiceNumber') {
                    result = this.compareInvoiceNumbers(valueA, valueB);
                } else if (typeof valueA === 'string' && typeof valueB === 'string') {
                    result = this.compareStrings(valueA, valueB);
                } else if (typeof valueA === 'number' && typeof valueB === 'number') {
                    result = this.compareNumbers(valueA, valueB);
                } else if (valueA instanceof Date && valueB instanceof Date) {
                    result = this.compareDates(valueA, valueB);
                } else {
                    // تحويل إلى نص للمقارنة
                    result = this.compareStrings(String(valueA), String(valueB));
                }

                return this.sortConfig.direction === 'asc' ? result : -result;

            } catch (error) {
                console.error(`❌ خطأ في فرز العنصر:`, error);
                return 0;
            }
        });

        console.log(`✅ تم الفرز بنجاح: ${array.length} عنصر`);
    }

    // Helper functions for normalization and comparison
    normalizeString(str) {
        if (!str) return '';
        return str.toString().trim().toLowerCase()
            .replace(/أ|إ|آ/g, 'ا')  // توحيد الألف
            .replace(/ة/g, 'ه')       // توحيد التاء المربوطة
            .replace(/ي|ى/g, 'ي');    // توحيد الياء
    }

    normalizeNumber(num) {
        if (num === null || num === undefined || num === '') return 0;
        const parsed = parseFloat(num);
        return isNaN(parsed) ? 0 : parsed;
    }

    normalizeInvoiceNumber(invoice) {
        if (!invoice) return '';
        // استخراج الأرقام من رقم الفاتورة للفرز الرقمي
        const numbers = invoice.toString().match(/\d+/g);
        if (numbers && numbers.length > 0) {
            // إنشاء مفتاح فرز يجمع بين النص والرقم
            const prefix = invoice.toString().replace(/\d+/g, '').toLowerCase();
            const number = parseInt(numbers[numbers.length - 1]); // آخر رقم في السلسلة
            return { prefix, number, original: invoice.toString().toLowerCase() };
        }
        return { prefix: invoice.toString().toLowerCase(), number: 0, original: invoice.toString().toLowerCase() };
    }

    normalizeDate(dateStr) {
        if (!dateStr) return new Date(0);
        try {
            // تحويل التاريخ من dd/mm/yyyy إلى Date object
            const parts = dateStr.split('/');
            if (parts.length === 3) {
                const day = parseInt(parts[0]);
                const month = parseInt(parts[1]) - 1; // الشهر يبدأ من 0
                const year = parseInt(parts[2]);
                return new Date(year, month, day);
            }
            return new Date(dateStr);
        } catch (error) {
            console.warn(`⚠️ تاريخ غير صالح: ${dateStr}`);
            return new Date(0);
        }
    }

    compareStrings(a, b) {
        if (a < b) return -1;
        if (a > b) return 1;
        return 0;
    }

    compareNumbers(a, b) {
        return a - b;
    }

    compareDates(a, b) {
        return a.getTime() - b.getTime();
    }

    compareInvoiceNumbers(a, b) {
        // مقارنة أرقام الفواتير بذكاء
        if (!a || !b) {
            if (!a && !b) return 0;
            return !a ? -1 : 1;
        }

        // مقارنة البادئة أولاً
        if (a.prefix !== b.prefix) {
            return this.compareStrings(a.prefix, b.prefix);
        }

        // إذا كانت البادئة متشابهة، قارن الأرقام
        if (a.number !== b.number) {
            return a.number - b.number;
        }

        // إذا كانت البادئة والأرقام متشابهة، قارن النص الأصلي
        return this.compareStrings(a.original, b.original);
    }

    // Update sort icons
    updateSortIcons(activeField, activeSection) {
        try {
            console.log(`🔄 تحديث أيقونات الفرز: ${activeField} في ${activeSection}`);

            // إزالة جميع أيقونات الفرز النشطة في القسم المحدد
            const sectionIcons = document.querySelectorAll(`[data-sort-section="${activeSection}"] .sort-icon`);
            sectionIcons.forEach(icon => {
                icon.className = 'sort-icon fas fa-sort';
                icon.style.color = ''; // إزالة اللون المخصص
            });

            // إضافة الأيقونة النشطة
            const activeIcon = document.querySelector(`[data-sort-field="${activeField}"][data-sort-section="${activeSection}"] .sort-icon`);
            if (activeIcon) {
                if (this.sortConfig.direction === 'asc') {
                    activeIcon.className = 'sort-icon fas fa-sort-up';
                    activeIcon.style.color = '#059669'; // أخضر للتصاعدي
                } else {
                    activeIcon.className = 'sort-icon fas fa-sort-down';
                    activeIcon.style.color = '#dc2626'; // أحمر للتنازلي
                }
                console.log(`✅ تم تحديث الأيقونة: ${this.sortConfig.direction}`);
            } else {
                console.warn(`⚠️ لم يتم العثور على أيقونة الفرز: ${activeField} في ${activeSection}`);
            }

            // إضافة تأثير بصري للعمود النشط
            const activeHeader = document.querySelector(`[data-sort-field="${activeField}"][data-sort-section="${activeSection}"]`);
            if (activeHeader) {
                // إزالة التأثير من جميع العناوين في القسم
                document.querySelectorAll(`[data-sort-section="${activeSection}"]`).forEach(header => {
                    header.classList.remove('active-sort');
                });
                // إضافة التأثير للعنوان النشط
                activeHeader.classList.add('active-sort');
            }

        } catch (error) {
            console.error('❌ خطأ في تحديث أيقونات الفرز:', error);
        }
    }

    // Re-apply active sort after data changes
    reapplySortIfActive() {
        if (this.sortConfig.field && this.sortConfig.section) {
            console.log(`🔄 إعادة تطبيق الفرز: ${this.sortConfig.field} في ${this.sortConfig.section}`);

            try {
                if (this.sortConfig.section === 'new' && this.filteredDebts.length > 0) {
                    this.sortArray(this.filteredDebts, this.sortConfig.field);
                    this.displayRecords(this.filteredDebts);
                } else if (this.sortConfig.section === 'previous' && this.filteredPreviousDebts.length > 0) {
                    this.sortArray(this.filteredPreviousDebts, this.sortConfig.field);
                    this.displayPreviousDebt(this.filteredPreviousDebts);
                }

                // تحديث أيقونات الفرز
                this.updateSortIcons(this.sortConfig.field, this.sortConfig.section);

                console.log(`✅ تم إعادة تطبيق الفرز بنجاح`);
            } catch (error) {
                console.error('❌ خطأ في إعادة تطبيق الفرز:', error);
            }
        }
    }

    // Reset sort configuration
    resetSort() {
        console.log('🔄 إعادة تعيين إعدادات الفرز');
        this.sortConfig = {
            field: null,
            direction: 'asc',
            section: null
        };

        // إزالة جميع أيقونات الفرز النشطة
        document.querySelectorAll('.sort-icon').forEach(icon => {
            icon.className = 'sort-icon fas fa-sort';
            icon.style.color = '';
        });

        // إزالة تأثيرات العمود النشط
        document.querySelectorAll('.sortable-header').forEach(header => {
            header.classList.remove('active-sort');
        });

        console.log('✅ تم إعادة تعيين الفرز');
    }

    // Initialize sticky headers functionality
    initializeStickyHeaders() {
        console.log('🔧 تهيئة ترويسات الفرز المثبتة');

        // Add scroll event listeners to debt content containers
        const debtContainers = document.querySelectorAll('.debt-content');

        debtContainers.forEach((container, index) => {
            const header = container.querySelector('.debt-list-header');
            if (header) {
                container.addEventListener('scroll', () => {
                    this.handleStickyHeader(container, header);
                });

                // Initial check
                this.handleStickyHeader(container, header);
            }
        });

        console.log(`✅ تم تهيئة ${debtContainers.length} ترويسة مثبتة`);
    }

    // Handle sticky header visual effects
    handleStickyHeader(container, header) {
        const scrollTop = container.scrollTop;

        if (scrollTop > 10) {
            // Header is sticky and scrolled
            header.classList.add('sticky-active');
        } else {
            // Header is at top
            header.classList.remove('sticky-active');
        }
    }
}

// Toggle dropdown function
function toggleDropdown(dropdownId) {
    // Close all other dropdowns first
    document.querySelectorAll('.dropdown-content').forEach(dropdown => {
        if (dropdown.id !== dropdownId) {
            dropdown.classList.remove('show');
        }
    });

    // Toggle the clicked dropdown
    const dropdown = document.getElementById(dropdownId);
    if (dropdown) {
        dropdown.classList.toggle('show');
    }
}

// Close all dropdowns function (used by export-import.js)
function closeAllDropdowns() {
    document.querySelectorAll('.dropdown-content').forEach(dropdown => {
        dropdown.classList.remove('show');
    });
}

// Make functions globally accessible
window.toggleDropdown = toggleDropdown;
window.closeAllDropdowns = closeAllDropdowns;

// Close dropdowns when clicking outside
document.addEventListener('click', function(event) {
    if (!event.target.closest('.dropdown')) {
        document.querySelectorAll('.dropdown-content').forEach(dropdown => {
            dropdown.classList.remove('show');
        });
    }
});

// Initialize the application
let debtManager;
document.addEventListener('DOMContentLoaded', () => {
    debtManager = new DebtManager();
    // Make it globally accessible
    window.debtManager = debtManager;

    // Add reset function to console for testing
    window.resetData = () => {
        localStorage.clear();
        location.reload();
    };

    console.log('🔧 للمساعدة في التطوير: استخدم resetData() لمسح البيانات وإعادة التحميل');

    // Add test functions for analytics sections
    window.testAnalytics = () => {
        console.log('🧪 Testing smooth analytics transitions...');
        setTimeout(() => debtManager.showSection('analytics'), 100);
        setTimeout(() => debtManager.showSection('comparisons'), 1000);
        setTimeout(() => debtManager.showSection('statistics'), 2000);
        setTimeout(() => debtManager.showSection('analytics'), 3000);
        console.log('✅ Smooth transition test sequence started');
    };

    // Add function to test rapid switching
    window.testRapidSwitch = () => {
        console.log('⚡ Testing rapid section switching...');
        let count = 0;
        const sections = ['analytics', 'comparisons', 'statistics'];
        const interval = setInterval(() => {
            debtManager.showSection(sections[count % 3]);
            count++;
            if (count >= 6) {
                clearInterval(interval);
                console.log('✅ Rapid switch test completed');
            }
        }, 600);
    };

    // Add function to test smooth transitions
    window.testSmoothTransitions = () => {
        console.log('🎨 Testing ultra-smooth transitions...');
        const sections = ['analytics', 'comparisons', 'statistics'];
        let index = 0;

        const smoothSwitch = () => {
            if (index < sections.length * 2) {
                debtManager.showSection(sections[index % sections.length]);
                index++;
                setTimeout(smoothSwitch, 1200);
            } else {
                console.log('✅ Smooth transitions test completed');
            }
        };

        smoothSwitch();
    };
});

// Export data function
function exportData() {
    const dataStr = JSON.stringify(debtManager.debts, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `customer_debts_${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
}

// Import data function
function importData(event) {
    const file = event.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const importedData = JSON.parse(e.target.result);
                if (Array.isArray(importedData)) {
                    debtManager.debts = importedData;
                    debtManager.saveData();
                    debtManager.updateStatistics();
                    debtManager.displayRecords();
                    debtManager.showSuccess('تم استيراد البيانات بنجاح');
                } else {
                    debtManager.showError('ملف البيانات غير صحيح');
                }
            } catch (error) {
                debtManager.showError('خطأ في قراءة الملف');
            }
        };
        reader.readAsText(file);
    }
}
