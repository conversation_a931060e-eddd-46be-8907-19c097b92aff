<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نهائي للتصدير</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .pdf-btn { background: #dc3545; }
        .excel-btn { background: #28a745; }
        .dropdown {
            position: relative;
            display: inline-block;
        }
        .dropdown-btn {
            background: #6c757d;
            padding: 8px 12px;
        }
        .dropdown-content {
            display: none;
            position: absolute;
            background: white;
            min-width: 200px;
            box-shadow: 0px 8px 16px rgba(0,0,0,0.2);
            z-index: 1;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .dropdown-content.show {
            display: block;
        }
        .dropdown-content a {
            color: black;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
        }
        .dropdown-content a:hover {
            background: #f1f1f1;
        }
    </style>
</head>
<body>
    <h1>🧪 اختبار نهائي لوظائف التصدير</h1>
    
    <div class="container">
        <h2>📊 حالة النظام</h2>
        <div id="systemStatus"></div>
    </div>
    
    <div class="container">
        <h2>🎯 اختبار الوظائف</h2>
        
        <div class="test-section info">
            <h3>اختبار مباشر للوظائف</h3>
            <button class="pdf-btn" onclick="testPDFDirect()">اختبار PDF مباشر</button>
            <button class="excel-btn" onclick="testExcelDirect()">اختبار Excel مباشر</button>
        </div>
        
        <div class="test-section info">
            <h3>اختبار القائمة المنسدلة</h3>
            <div class="dropdown">
                <button class="dropdown-btn" onclick="toggleDropdown('testDropdown')">
                    قائمة الاختبار ⋮
                </button>
                <div class="dropdown-content" id="testDropdown">
                    <a href="#" onclick="testPDFFromDropdown()">
                        📄 اختبار PDF من القائمة
                    </a>
                    <a href="#" onclick="testExcelFromDropdown()">
                        📊 اختبار Excel من القائمة
                    </a>
                </div>
            </div>
        </div>
        
        <div id="testResults"></div>
    </div>

    <!-- نفس المكتبات المستخدمة في النظام الأصلي -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdn.sheetjs.com/xlsx-0.20.1/package/dist/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <script>
        // محاكاة debtManager مع بيانات تجريبية
        window.debtManager = {
            debts: [
                {
                    id: 1,
                    invoiceNumber: 'INV-001',
                    customerName: 'أحمد محمد',
                    city: 'الرياض',
                    amount: 1500,
                    notes: 'ملاحظة تجريبية',
                    date: '15/12/2024'
                },
                {
                    id: 2,
                    invoiceNumber: 'INV-002',
                    customerName: 'فاطمة علي',
                    city: 'جدة',
                    amount: 2000,
                    notes: 'ملاحظة أخرى',
                    date: '16/12/2024'
                }
            ],
            previousDebts: [
                {
                    id: 3,
                    invoiceNumber: 'OLD-001',
                    customerName: 'محمد أحمد',
                    city: 'الدمام',
                    amount: 800,
                    notes: 'دين سابق',
                    date: '10/12/2024'
                }
            ],
            showSuccess: function(message) {
                showResult('✅ نجح: ' + message, 'success');
            },
            showError: function(message) {
                showResult('❌ خطأ: ' + message, 'error');
            }
        };

        // وظائف مساعدة
        function showResult(message, type) {
            const resultsDiv = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = `test-section ${type}`;
            div.innerHTML = `<p>${message}</p>`;
            resultsDiv.appendChild(div);
            
            // إزالة الرسالة بعد 5 ثوان
            setTimeout(() => {
                if (resultsDiv.contains(div)) {
                    resultsDiv.removeChild(div);
                }
            }, 5000);
        }

        // وظائف القائمة المنسدلة
        function toggleDropdown(dropdownId) {
            const dropdown = document.getElementById(dropdownId);
            if (dropdown) {
                dropdown.classList.toggle('show');
            }
        }

        function closeAllDropdowns() {
            document.querySelectorAll('.dropdown-content').forEach(dropdown => {
                dropdown.classList.remove('show');
            });
        }

        // إغلاق القوائم عند النقر خارجها
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.dropdown')) {
                closeAllDropdowns();
            }
        });

        // جعل الوظائف متاحة عالمياً
        window.toggleDropdown = toggleDropdown;
        window.closeAllDropdowns = closeAllDropdowns;

        // فحص حالة النظام
        function checkSystemStatus() {
            const statusDiv = document.getElementById('systemStatus');
            let html = '';

            // فحص المكتبات
            if (typeof window.jspdf !== 'undefined') {
                html += '<div class="test-section success">✅ مكتبة jsPDF محملة</div>';
            } else {
                html += '<div class="test-section error">❌ مكتبة jsPDF غير محملة</div>';
            }

            if (typeof window.XLSX !== 'undefined') {
                html += '<div class="test-section success">✅ مكتبة XLSX محملة</div>';
            } else {
                html += '<div class="test-section error">❌ مكتبة XLSX غير محملة</div>';
            }

            // فحص البيانات
            if (window.debtManager && window.debtManager.debts.length > 0) {
                html += `<div class="test-section success">✅ البيانات الجديدة متوفرة (${window.debtManager.debts.length} سجل)</div>`;
            } else {
                html += '<div class="test-section error">❌ لا توجد بيانات جديدة</div>';
            }

            if (window.debtManager && window.debtManager.previousDebts.length > 0) {
                html += `<div class="test-section success">✅ الديون السابقة متوفرة (${window.debtManager.previousDebts.length} سجل)</div>`;
            } else {
                html += '<div class="test-section error">❌ لا توجد ديون سابقة</div>';
            }

            statusDiv.innerHTML = html;
        }

        // اختبار PDF مباشر
        function testPDFDirect() {
            showResult('🔄 بدء اختبار PDF...', 'info');
            
            try {
                if (!window.jspdf) {
                    throw new Error('مكتبة jsPDF غير متوفرة');
                }

                const { jsPDF } = window.jspdf;
                const doc = new jsPDF();
                
                doc.setFontSize(16);
                doc.text('Test PDF - New Invoices', 20, 20);
                
                const data = window.debtManager.debts;
                let yPos = 40;
                
                data.forEach((item, index) => {
                    doc.setFontSize(10);
                    doc.text(`${index + 1}. ${item.invoiceNumber} - ${item.customerName} - ${item.amount} SAR`, 20, yPos);
                    yPos += 10;
                });
                
                doc.save('test-direct-pdf.pdf');
                showResult('✅ تم إنشاء PDF مباشر بنجاح!', 'success');
                
            } catch (error) {
                showResult('❌ خطأ في PDF: ' + error.message, 'error');
            }
        }

        // اختبار Excel مباشر
        function testExcelDirect() {
            showResult('🔄 بدء اختبار Excel...', 'info');
            
            try {
                if (!window.XLSX) {
                    throw new Error('مكتبة XLSX غير متوفرة');
                }

                const data = window.debtManager.debts.map(item => ({
                    'رقم الفاتورة': item.invoiceNumber,
                    'اسم العميل': item.customerName,
                    'المدينة': item.city,
                    'المبلغ': item.amount,
                    'الملاحظات': item.notes
                }));

                const wb = XLSX.utils.book_new();
                const ws = XLSX.utils.json_to_sheet(data);
                XLSX.utils.book_append_sheet(wb, ws, 'اختبار');
                
                XLSX.writeFile(wb, 'test-direct-excel.xlsx');
                showResult('✅ تم إنشاء Excel مباشر بنجاح!', 'success');
                
            } catch (error) {
                showResult('❌ خطأ في Excel: ' + error.message, 'error');
            }
        }

        // اختبار من القائمة المنسدلة
        function testPDFFromDropdown() {
            closeAllDropdowns();
            showResult('🔄 اختبار PDF من القائمة المنسدلة...', 'info');
            
            // محاكاة استدعاء الوظيفة الأصلية
            setTimeout(() => {
                testPDFDirect();
            }, 500);
        }

        function testExcelFromDropdown() {
            closeAllDropdowns();
            showResult('🔄 اختبار Excel من القائمة المنسدلة...', 'info');
            
            // محاكاة استدعاء الوظيفة الأصلية
            setTimeout(() => {
                testExcelDirect();
            }, 500);
        }

        // تشغيل فحص النظام عند التحميل
        window.addEventListener('load', function() {
            checkSystemStatus();
            showResult('🎯 النظام جاهز للاختبار!', 'info');
        });
    </script>
</body>
</html>
