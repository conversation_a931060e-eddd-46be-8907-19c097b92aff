; Custom NSIS installer script for Customer Debt Manager
; نص مخصص لمثبت نظام إدارة ديون العملاء

; إعدادات اللغة العربية
!define LANG_ARABIC 1025

; رسائل مخصصة بالعربية
LangString WELCOME_TEXT ${LANG_ARABIC} "مرحباً بك في معالج تثبيت نظام إدارة ديون العملاء"
LangString FINISH_TEXT ${LANG_ARABIC} "تم تثبيت نظام إدارة ديون العملاء بنجاح"
LangString UNINSTALL_TEXT ${LANG_ARABIC} "هل تريد إزالة نظام إدارة ديون العملاء من جهازك؟"

; إعدادات المثبت
!define PRODUCT_NAME "نظام إدارة ديون العملاء"
!define PRODUCT_VERSION "1.0.0"
!define PRODUCT_PUBLISHER "مطور النظام"
!define PRODUCT_WEB_SITE "https://example.com"
!define PRODUCT_DIR_REGKEY "Software\Microsoft\Windows\CurrentVersion\App Paths\customer-debt-manager.exe"
!define PRODUCT_UNINST_KEY "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}"

; إعدادات إضافية
SetCompressor lzma
RequestExecutionLevel admin
ShowInstDetails show
ShowUnInstDetails show

; صفحة الترحيب المخصصة
!define MUI_WELCOMEPAGE_TITLE "مرحباً بك في معالج التثبيت"
!define MUI_WELCOMEPAGE_TEXT "سيقوم هذا المعالج بتثبيت نظام إدارة ديون العملاء على جهازك.$\r$\n$\r$\nالمميزات:$\r$\n• يعمل بدون إنترنت$\r$\n• حفظ البيانات محلياً$\r$\n• واجهة عربية كاملة$\r$\n• إحصائيات متقدمة$\r$\n$\r$\nانقر التالي للمتابعة."

; صفحة الانتهاء المخصصة
!define MUI_FINISHPAGE_TITLE "تم التثبيت بنجاح"
!define MUI_FINISHPAGE_TEXT "تم تثبيت نظام إدارة ديون العملاء بنجاح على جهازك.$\r$\n$\r$\nيمكنك الآن تشغيل البرنامج من سطح المكتب أو قائمة ابدأ."
!define MUI_FINISHPAGE_RUN "$INSTDIR\customer-debt-manager.exe"
!define MUI_FINISHPAGE_RUN_TEXT "تشغيل نظام إدارة ديون العملاء الآن"

; إنشاء اختصارات إضافية
Section "إنشاء الاختصارات" SEC_SHORTCUTS
    ; اختصار سطح المكتب
    CreateShortCut "$DESKTOP\نظام إدارة ديون العملاء.lnk" "$INSTDIR\customer-debt-manager.exe" "" "$INSTDIR\customer-debt-manager.exe" 0
    
    ; اختصار قائمة ابدأ
    CreateDirectory "$SMPROGRAMS\نظام إدارة ديون العملاء"
    CreateShortCut "$SMPROGRAMS\نظام إدارة ديون العملاء\نظام إدارة ديون العملاء.lnk" "$INSTDIR\customer-debt-manager.exe" "" "$INSTDIR\customer-debt-manager.exe" 0
    CreateShortCut "$SMPROGRAMS\نظام إدارة ديون العملاء\إزالة البرنامج.lnk" "$INSTDIR\Uninstall.exe" "" "$INSTDIR\Uninstall.exe" 0
    
    ; اختصار شريط المهام (Windows 7+)
    ${If} ${AtLeastWin7}
        ExecShell "taskbarpin" "$INSTDIR\customer-debt-manager.exe"
    ${EndIf}
SectionEnd

; إنشاء مجلد البيانات
Section "إعداد مجلد البيانات" SEC_DATA
    ; إنشاء مجلد البيانات في Documents
    CreateDirectory "$DOCUMENTS\نظام إدارة ديون العملاء"
    CreateDirectory "$DOCUMENTS\نظام إدارة ديون العملاء\البيانات"
    CreateDirectory "$DOCUMENTS\نظام إدارة ديون العملاء\النسخ الاحتياطية"
    CreateDirectory "$DOCUMENTS\نظام إدارة ديون العملاء\التقارير"
    
    ; إنشاء ملف README
    FileOpen $0 "$DOCUMENTS\نظام إدارة ديون العملاء\اقرأني.txt" w
    FileWrite $0 "مرحباً بك في نظام إدارة ديون العملاء$\r$\n"
    FileWrite $0 "================================$\r$\n$\r$\n"
    FileWrite $0 "هذا المجلد يحتوي على:$\r$\n"
    FileWrite $0 "• البيانات: ملفات البيانات المحفوظة$\r$\n"
    FileWrite $0 "• النسخ الاحتياطية: نسخ احتياطية من البيانات$\r$\n"
    FileWrite $0 "• التقارير: التقارير المصدرة$\r$\n$\r$\n"
    FileWrite $0 "لا تحذف هذا المجلد لتجنب فقدان البيانات.$\r$\n"
    FileClose $0
SectionEnd

; تسجيل البرنامج في النظام
Section "تسجيل البرنامج" SEC_REGISTER
    ; تسجيل في Add/Remove Programs
    WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "DisplayName" "${PRODUCT_NAME}"
    WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "DisplayVersion" "${PRODUCT_VERSION}"
    WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "Publisher" "${PRODUCT_PUBLISHER}"
    WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "URLInfoAbout" "${PRODUCT_WEB_SITE}"
    WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "DisplayIcon" "$INSTDIR\customer-debt-manager.exe"
    WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "UninstallString" "$INSTDIR\Uninstall.exe"
    WriteRegDWORD HKLM "${PRODUCT_UNINST_KEY}" "NoModify" 1
    WriteRegDWORD HKLM "${PRODUCT_UNINST_KEY}" "NoRepair" 1
    
    ; تسجيل مسار البرنامج
    WriteRegStr HKLM "${PRODUCT_DIR_REGKEY}" "" "$INSTDIR\customer-debt-manager.exe"
    
    ; ربط أنواع الملفات
    WriteRegStr HKCR ".debt" "" "DebtManagerFile"
    WriteRegStr HKCR "DebtManagerFile" "" "ملف نظام إدارة الديون"
    WriteRegStr HKCR "DebtManagerFile\DefaultIcon" "" "$INSTDIR\customer-debt-manager.exe,0"
    WriteRegStr HKCR "DebtManagerFile\shell\open\command" "" '"$INSTDIR\customer-debt-manager.exe" "%1"'
SectionEnd

; إعدادات الأمان
Section "إعدادات الأمان" SEC_SECURITY
    ; إضافة استثناء في Windows Defender (اختياري)
    ; ExecWait 'powershell.exe -Command "Add-MpPreference -ExclusionPath \"$INSTDIR\""'
    
    ; تعيين صلاحيات المجلد
    AccessControl::GrantOnFile "$INSTDIR" "(BU)" "FullAccess"
    AccessControl::GrantOnFile "$DOCUMENTS\نظام إدارة ديون العملاء" "(BU)" "FullAccess"
SectionEnd

; وظيفة التنظيف عند الإزالة
Function un.onInit
    MessageBox MB_ICONQUESTION|MB_YESNO|MB_DEFBUTTON2 "هل تريد إزالة نظام إدارة ديون العملاء وجميع مكوناته؟" IDYES +2
    Abort
FunctionEnd

; قسم الإزالة
Section "Uninstall"
    ; حذف الملفات
    Delete "$INSTDIR\customer-debt-manager.exe"
    Delete "$INSTDIR\Uninstall.exe"
    RMDir /r "$INSTDIR"
    
    ; حذف الاختصارات
    Delete "$DESKTOP\نظام إدارة ديون العملاء.lnk"
    RMDir /r "$SMPROGRAMS\نظام إدارة ديون العملاء"
    
    ; إزالة التسجيل
    DeleteRegKey HKLM "${PRODUCT_UNINST_KEY}"
    DeleteRegKey HKLM "${PRODUCT_DIR_REGKEY}"
    DeleteRegKey HKCR ".debt"
    DeleteRegKey HKCR "DebtManagerFile"
    
    ; سؤال عن حذف البيانات
    MessageBox MB_ICONQUESTION|MB_YESNO "هل تريد حذف البيانات المحفوظة أيضاً؟$\r$\n(في حالة الرفض ستبقى البيانات محفوظة)" IDNO +2
    RMDir /r "$DOCUMENTS\نظام إدارة ديون العملاء"
    
    SetAutoClose true
SectionEnd

; رسائل التأكيد
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
    !insertmacro MUI_DESCRIPTION_TEXT ${SEC_SHORTCUTS} "إنشاء اختصارات على سطح المكتب وقائمة ابدأ"
    !insertmacro MUI_DESCRIPTION_TEXT ${SEC_DATA} "إعداد مجلدات البيانات والنسخ الاحتياطية"
    !insertmacro MUI_DESCRIPTION_TEXT ${SEC_REGISTER} "تسجيل البرنامج في النظام وربط أنواع الملفات"
    !insertmacro MUI_DESCRIPTION_TEXT ${SEC_SECURITY} "إعداد صلاحيات الأمان المطلوبة"
!insertmacro MUI_FUNCTION_DESCRIPTION_END
