# 🖥️ نظام إدارة ديون العملاء - تطبيق سطح المكتب

## 🎯 نظرة عامة

تم تحويل نظام إدارة ديون العملاء إلى تطبيق سطح مكتب مستقل باستخدام Electron، يعمل بدون الحاجة لمتصفح ويمكن تثبيته على Windows, Mac, Linux.

---

## ✨ المميزات الجديدة

### 🖥️ تطبيق سطح مكتب أصلي:
- ✅ يعمل بدون متصفح
- ✅ نافذة مستقلة مع قوائم نظام
- ✅ اختصارات لوحة مفاتيح
- ✅ تكامل مع نظام التشغيل

### 📱 تثبيت احترافي:
- ✅ مثبت Windows (.exe)
- ✅ مثبت Mac (.dmg)
- ✅ تطبيق Linux (.AppImage, .deb)
- ✅ أيقونات مخصصة
- ✅ اختصارات سطح المكتب

### 🔌 عمل بدون إنترنت:
- ✅ جميع الوظائف تعمل محلياً
- ✅ البيانات محفوظة على الجهاز
- ✅ لا حاجة للإنترنت بعد التثبيت

---

## 🚀 التشغيل السريع

### 🪟 Windows:
```cmd
# تشغيل الملف التفاعلي
start-desktop-app.bat
```

### 🐧 Linux / 🍎 Mac:
```bash
# جعل الملف قابل للتنفيذ
chmod +x start-desktop-app.sh

# تشغيل الملف التفاعلي
./start-desktop-app.sh
```

### 📋 أو يدوياً:
```bash
# تثبيت المتطلبات
npm install

# تشغيل التطبيق
npm start

# بناء للتوزيع
npm run build
```

---

## 📁 هيكل المشروع

```
📁 نظام إدارة الديون/
├── 📁 electron/                 # ملفات Electron
│   ├── 📄 main.js              # الملف الرئيسي
│   ├── 📄 preload.js           # ملف الأمان
│   └── 📄 installer.nsh        # إعدادات مثبت Windows
├── 📁 build/                   # أيقونات التطبيق
│   ├── 📄 icon.ico            # أيقونة Windows
│   ├── 📄 icon.icns           # أيقونة Mac
│   └── 📄 icon.png            # أيقونة Linux
├── 📁 dist/                   # ملفات التوزيع (بعد البناء)
├── 📁 html/                   # ملفات HTML
├── 📁 css/                    # ملفات CSS
├── 📁 javascript/             # ملفات JavaScript
├── 📄 package.json            # إعدادات المشروع
├── 📄 start-desktop-app.bat   # تشغيل Windows
├── 📄 start-desktop-app.sh    # تشغيل Linux/Mac
└── 📄 BUILD_INSTRUCTIONS.md   # تعليمات البناء
```

---

## 🔧 المتطلبات

### 💻 للتطوير:
- **Node.js** (الإصدار 16 أو أحدث)
- **npm** أو **yarn**
- **Git** (اختياري)

### 🎨 لإنشاء الأيقونات:
- **GIMP** أو **Photoshop** (لتحرير الصور)
- **electron-icon-maker** (لإنشاء الأيقونات تلقائياً)

### 🏗️ للبناء:
- **Windows:** Windows 7+ (لبناء تطبيقات Windows)
- **Mac:** macOS 10.12+ (لبناء تطبيقات Mac)
- **Linux:** أي توزيعة حديثة (لبناء تطبيقات Linux)

---

## 📦 أوامر البناء

### 🚀 التطوير:
```bash
npm start          # تشغيل التطبيق
npm run dev        # تشغيل مع أدوات المطور
```

### 🏗️ البناء:
```bash
npm run build      # بناء للمنصة الحالية
npm run build-win  # بناء لـ Windows
npm run build-mac  # بناء لـ Mac
npm run build-linux # بناء لـ Linux
```

### 🧹 الصيانة:
```bash
npm run pack      # بناء بدون ضغط
npm run dist      # بناء للتوزيع
```

---

## 📱 أنواع الملفات المُنتجة

### 🪟 Windows:
- **Setup.exe** - مثبت كامل مع إزالة
- **Portable.exe** - نسخة محمولة
- **win-unpacked/** - ملفات غير مضغوطة

### 🍎 Mac:
- **.dmg** - مثبت Mac
- **.zip** - أرشيف مضغوط
- **mac/** - ملفات التطبيق

### 🐧 Linux:
- **.AppImage** - تطبيق محمول
- **.deb** - حزمة Debian/Ubuntu
- **linux-unpacked/** - ملفات غير مضغوطة

---

## 🎨 تخصيص التطبيق

### 🖼️ تغيير الأيقونات:
1. ضع الأيقونات الجديدة في مجلد `build/`
2. تأكد من الأسماء: `icon.ico`, `icon.icns`, `icon.png`
3. أعد البناء: `npm run build`

### 📝 تغيير معلومات التطبيق:
```json
// في package.json
{
  "name": "اسم-التطبيق-الجديد",
  "productName": "الاسم المعروض",
  "description": "وصف التطبيق",
  "version": "1.0.0"
}
```

### 🎯 تخصيص النافذة:
```javascript
// في electron/main.js
this.mainWindow = new BrowserWindow({
    width: 1400,        // العرض
    height: 900,        // الارتفاع
    title: 'العنوان',   // عنوان النافذة
    icon: 'المسار'      // مسار الأيقونة
});
```

---

## 🛠️ حل المشاكل

### ❌ مشكلة: Node.js غير مثبت
```bash
# Windows: تحميل من https://nodejs.org/
# Ubuntu/Debian:
sudo apt install nodejs npm

# CentOS/RHEL:
sudo yum install nodejs npm

# Mac:
brew install node
```

### ❌ مشكلة: فشل في البناء
```bash
# حذف وإعادة تثبيت المتطلبات
rm -rf node_modules package-lock.json
npm install

# تحديث electron-builder
npm update electron-builder
```

### ❌ مشكلة: الأيقونة لا تظهر
```bash
# التأكد من وجود الأيقونات
ls build/icon.*

# إنشاء الأيقونات تلقائياً
npm install -g electron-icon-maker
electron-icon-maker --input=source.png --output=build/
```

---

## 📋 قائمة التحقق

### ✅ قبل البناء:
- [ ] تثبيت Node.js و npm
- [ ] تثبيت المتطلبات: `npm install`
- [ ] إنشاء الأيقونات في `build/`
- [ ] اختبار التطبيق: `npm start`
- [ ] تحديث معلومات `package.json`

### ✅ بعد البناء:
- [ ] التحقق من ملفات `dist/`
- [ ] اختبار المثبت على نظام نظيف
- [ ] التأكد من عمل التطبيق بدون إنترنت
- [ ] اختبار جميع الوظائف

---

## 🎉 النتيجة النهائية

### 🖥️ تطبيق سطح مكتب احترافي:
- ✅ يعمل بدون متصفح أو إنترنت
- ✅ مثبت احترافي لجميع المنصات
- ✅ أيقونات مخصصة وقوائم نظام
- ✅ تجربة مستخدم أصلية

### 📊 جميع الوظائف الأصلية:
- ✅ إدارة الديون والفواتير
- ✅ إحصائيات وتحليلات متقدمة
- ✅ البحث والفرز
- ✅ تصدير البيانات
- ✅ طباعة التقارير

### 🔒 أمان وموثوقية:
- ✅ البيانات محفوظة محلياً
- ✅ لا حاجة للإنترنت
- ✅ تحديثات تلقائية (اختيارية)
- ✅ نسخ احتياطية محلية

---

## 📞 الدعم

### 🔍 للمساعدة:
1. راجع ملف `BUILD_INSTRUCTIONS.md`
2. تحقق من وحدة التحكم (F12 في التطبيق)
3. راجع ملفات السجل في مجلد التطبيق

### 🐛 الإبلاغ عن مشاكل:
- وصف المشكلة بالتفصيل
- نظام التشغيل والإصدار
- خطوات إعادة إنتاج المشكلة
- رسائل الخطأ (إن وجدت)

**🎯 الهدف: تطبيق سطح مكتب احترافي يعمل بدون إنترنت!**
