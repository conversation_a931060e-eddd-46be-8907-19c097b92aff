// الحل الجذري النهائي لمشكلة فصل الأعمدة في Excel
// هذا الحل يستخدم طريقة مختلفة تماماً لضمان فصل الأعمدة

function createExcelWithAbsoluteColumnSeparation(data, fileName) {
    console.log('🔥 بدء الحل الجذري النهائي لفصل الأعمدة...');

    try {
        if (typeof XLSX === 'undefined') {
            throw new Error('مكتبة XLSX غير متوفرة');
        }

        if (!data || data.length === 0) {
            throw new Error('لا توجد بيانات للتصدير');
        }

        console.log(`📊 معالجة ${data.length} سجل...`);

        // إنشاء workbook جديد
        const workbook = XLSX.utils.book_new();

        // إنشاء مصفوفة ثنائية الأبعاد للبيانات
        const worksheetData = [];

        // إضافة صف الـ headers
        const headers = ['رقم الفاتورة', 'اسم العميل', 'المدينة', 'المبلغ', 'الملاحظات'];
        worksheetData.push(headers);

        console.log('📋 Headers:', headers);

        // إضافة صفوف البيانات
        data.forEach((item, index) => {
            const row = [
                String(item.invoiceNumber || '').trim(),
                String(item.customerName || '').trim(),
                String(item.city || '').trim(),
                parseFloat(item.amount) || 0,
                String(item.notes || '').trim()
            ];

            worksheetData.push(row);

            if (index < 3) {
                console.log(`صف ${index + 2}:`, row);
            }
        });

        console.log(`📊 إجمالي الصفوف: ${worksheetData.length}`);
        console.log(`📊 إجمالي الأعمدة: ${worksheetData[0].length}`);

        // إنشاء worksheet من المصفوفة ثنائية الأبعاد
        const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

        // التحقق من إنشاء الورقة
        console.log('📐 نطاق الورقة:', worksheet['!ref']);

        // طباعة عينة من الخلايا للتحقق
        console.log('🔍 عينة من الخلايا:');
        console.log('A1:', worksheet['A1']);
        console.log('B1:', worksheet['B1']);
        console.log('C1:', worksheet['C1']);
        console.log('D1:', worksheet['D1']);
        console.log('E1:', worksheet['E1']);

        if (worksheet['A2']) {
            console.log('A2:', worksheet['A2']);
            console.log('B2:', worksheet['B2']);
            console.log('C2:', worksheet['C2']);
            console.log('D2:', worksheet['D2']);
            console.log('E2:', worksheet['E2']);
        }

        // تحديد عرض الأعمدة
        worksheet['!cols'] = [
            { wch: 15 }, // A: رقم الفاتورة
            { wch: 25 }, // B: اسم العميل
            { wch: 15 }, // C: المدينة
            { wch: 12 }, // D: المبلغ
            { wch: 30 }  // E: الملاحظات
        ];

        // إضافة تنسيق للـ headers
        const headerCells = ['A1', 'B1', 'C1', 'D1', 'E1'];
        headerCells.forEach(cellAddress => {
            if (worksheet[cellAddress]) {
                worksheet[cellAddress].s = {
                    font: { bold: true, color: { rgb: "FFFFFF" } },
                    fill: { fgColor: { rgb: "4472C4" } },
                    alignment: { horizontal: "center", vertical: "center" },
                    border: {
                        top: { style: "thin", color: { rgb: "000000" } },
                        bottom: { style: "thin", color: { rgb: "000000" } },
                        left: { style: "thin", color: { rgb: "000000" } },
                        right: { style: "thin", color: { rgb: "000000" } }
                    }
                };
            }
        });

        // تنسيق عمود المبلغ كأرقام
        for (let i = 2; i <= data.length + 1; i++) {
            const cellAddress = `D${i}`;
            if (worksheet[cellAddress] && typeof worksheet[cellAddress].v === 'number') {
                worksheet[cellAddress].z = '#,##0.00';
                worksheet[cellAddress].t = 'n';
            }
        }

        // إضافة الورقة إلى المصنف
        XLSX.utils.book_append_sheet(workbook, worksheet, 'بيانات الفواتير');

        console.log('✅ تم إنشاء الورقة وإضافتها للمصنف');

        // إنشاء اسم الملف
        const currentDate = new Date().toISOString().split('T')[0];
        const fullFileName = `${fileName}_فصل_الأعمدة_${currentDate}.xlsx`;

        // حفظ الملف
        XLSX.writeFile(workbook, fullFileName, {
            bookType: 'xlsx',
            type: 'binary',
            cellStyles: true,
            compression: true
        });

        console.log(`✅ تم حفظ الملف: ${fullFileName}`);

        return {
            success: true,
            fileName: fullFileName,
            recordsCount: data.length,
            message: `تم تصدير ${data.length} سجل بنجاح مع فصل مطلق للأعمدة`
        };

    } catch (error) {
        console.error('❌ خطأ في إنشاء Excel:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// وظيفة اختبار مع بيانات تجريبية
function testAbsoluteColumnSeparation() {
    const testData = [
        {
            invoiceNumber: '001',
            customerName: 'أحمد محمد',
            city: 'الرياض',
            amount: 1500.50,
            notes: 'ملاحظة تجريبية'
        },
        {
            invoiceNumber: '002',
            customerName: 'فاطمة علي',
            city: 'جدة',
            amount: 2000.75,
            notes: 'ملاحظة أخرى'
        },
        {
            invoiceNumber: '003',
            customerName: 'محمد سالم',
            city: 'الدمام',
            amount: 1750.25,
            notes: 'ملاحظة ثالثة'
        }
    ];

    console.log('🧪 بدء اختبار فصل الأعمدة المطلق...');
    const result = createExcelWithAbsoluteColumnSeparation(testData, 'اختبار_فصل_مطلق');

    if (result.success) {
        console.log('✅ نجح الاختبار!', result);
        alert(`✅ نجح الاختبار!\n\nتم إنشاء: ${result.fileName}\nعدد السجلات: ${result.recordsCount}\n\n🔍 تحقق من الملف - يجب أن تكون البيانات في أعمدة منفصلة تماماً:\n• العمود A: رقم الفاتورة\n• العمود B: اسم العميل\n• العمود C: المدينة\n• العمود D: المبلغ\n• العمود E: الملاحظات`);
    } else {
        console.error('❌ فشل الاختبار:', result.error);
        alert(`❌ فشل الاختبار: ${result.error}`);
    }
}

// وظيفة لاستبدال وظيفة Excel الحالية
function replaceExcelFunction(type) {
    console.log('🔄 استبدال وظيفة Excel بالحل الجذري...');

    try {
        if (typeof window.debtManager === 'undefined') {
            alert('نظام إدارة الديون غير جاهز. يرجى إعادة تحميل الصفحة.');
            return;
        }

        // الحصول على البيانات
        let data = [];
        let fileName = '';

        if (type === 'previous') {
            data = debtManager.previousDebts || [];
            fileName = 'الديون_السابقة';
        } else {
            data = debtManager.debts || [];
            fileName = 'الفواتير_الجديدة';
        }

        if (data.length === 0) {
            alert('لا توجد بيانات للتصدير');
            return;
        }

        console.log(`📊 تصدير ${data.length} سجل باستخدام الحل الجذري...`);

        // استخدام الحل الجذري
        const result = createExcelWithAbsoluteColumnSeparation(data, fileName);

        if (result.success) {
            if (typeof debtManager.showSuccess === 'function') {
                debtManager.showSuccess(`تم تصدير ${result.recordsCount} سجل بنجاح إلى ملف Excel مع فصل الأعمدة`);
            }
        } else {
            if (typeof debtManager.showError === 'function') {
                debtManager.showError(`حدث خطأ: ${result.error}`);
            }
        }

    } catch (error) {
        console.error('❌ خطأ في استبدال وظيفة Excel:', error);
        alert(`حدث خطأ: ${error.message}`);
    }
}

// تصدير الوظائف للاستخدام العام
if (typeof window !== 'undefined') {
    window.createExcelWithAbsoluteColumnSeparation = createExcelWithAbsoluteColumnSeparation;
    window.testAbsoluteColumnSeparation = testAbsoluteColumnSeparation;
    window.replaceExcelFunction = replaceExcelFunction;
    console.log('✅ وظائف الحل الجذري متوفرة عالمياً');
}

console.log('🔥 تم تحميل الحل الجذري النهائي لفصل الأعمدة');
