<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة التصدير</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .pdf-btn { background: #dc3545; }
        .excel-btn { background: #28a745; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🔍 تشخيص مشكلة التصدير</h1>
    
    <div class="container">
        <h2>اختبار الوظائف</h2>
        <button class="pdf-btn" onclick="testPDFFunction()">اختبار PDF</button>
        <button class="excel-btn" onclick="testExcelFunction()">اختبار Excel</button>
        <button onclick="clearLog()">مسح السجل</button>
    </div>
    
    <div class="container">
        <h2>سجل الأحداث</h2>
        <div id="log" class="log"></div>
    </div>

    <!-- نفس المكتبات والملفات المستخدمة في النظام الأصلي -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdn.sheetjs.com/xlsx-0.20.1/package/dist/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <script>
        // محاكاة debtManager
        window.debtManager = {
            debts: [
                {
                    id: 1,
                    invoiceNumber: 'INV-001',
                    customerName: 'أحمد محمد',
                    city: 'الرياض',
                    amount: 1500,
                    notes: 'ملاحظة تجريبية',
                    date: '15/12/2024'
                },
                {
                    id: 2,
                    invoiceNumber: 'INV-002',
                    customerName: 'فاطمة علي',
                    city: 'جدة',
                    amount: 2000,
                    notes: 'ملاحظة أخرى',
                    date: '16/12/2024'
                }
            ],
            previousDebts: [],
            showSuccess: function(message) {
                log('✅ نجح: ' + message);
            },
            showError: function(message) {
                log('❌ خطأ: ' + message);
            }
        };

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        // نسخ وظائف التصدير من الملف الأصلي
        function testPDFFunction() {
            log('🔄 بدء اختبار PDF...');
            
            try {
                // فحص المكتبات
                if (!window.jspdf) {
                    throw new Error('مكتبة PDF غير متوفرة');
                }
                
                if (!window.debtManager) {
                    throw new Error('نظام إدارة الديون غير جاهز');
                }
                
                log('✅ المكتبات متوفرة');
                
                // محاولة إنشاء PDF
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF({
                    orientation: 'portrait',
                    unit: 'mm',
                    format: 'a4'
                });
                
                log('✅ تم إنشاء مستند PDF');
                
                // إضافة محتوى
                doc.setFontSize(18);
                doc.setFont('helvetica', 'bold');
                doc.text('New Invoices Report', 105, 20, { align: 'center' });
                
                log('✅ تم إضافة العنوان');
                
                // إضافة البيانات
                const data = debtManager.debts;
                let yPosition = 50;
                
                data.forEach((item, index) => {
                    doc.setFontSize(10);
                    doc.setFont('helvetica', 'normal');
                    doc.text(`${index + 1}. Invoice: ${item.invoiceNumber}`, 20, yPosition);
                    doc.text(`Customer: ${item.customerName}`, 20, yPosition + 5);
                    doc.text(`City: ${item.city}`, 20, yPosition + 10);
                    doc.text(`Amount: ${item.amount} SAR`, 20, yPosition + 15);
                    yPosition += 25;
                });
                
                log('✅ تم إضافة البيانات');
                
                // حفظ الملف
                doc.save('test-report.pdf');
                
                log('✅ تم حفظ ملف PDF بنجاح!');
                debtManager.showSuccess('تم إنشاء ملف PDF بنجاح');
                
            } catch (error) {
                log('❌ خطأ في PDF: ' + error.message);
                console.error('PDF Error:', error);
                debtManager.showError('حدث خطأ أثناء إنشاء ملف PDF: ' + error.message);
            }
        }

        function testExcelFunction() {
            log('🔄 بدء اختبار Excel...');
            
            try {
                // فحص المكتبات
                if (!window.XLSX) {
                    throw new Error('مكتبة Excel غير متوفرة');
                }
                
                if (!window.debtManager) {
                    throw new Error('نظام إدارة الديون غير جاهز');
                }
                
                log('✅ المكتبات متوفرة');
                
                const data = debtManager.debts;
                
                if (data.length === 0) {
                    throw new Error('لا توجد بيانات للتصدير');
                }
                
                log(`✅ تم العثور على ${data.length} سجل`);
                
                // تحضير البيانات
                const excelData = data.map((item, index) => ({
                    'رقم الفاتورة': item.invoiceNumber,
                    'اسم العميل': item.customerName,
                    'المدينة': item.city,
                    'المبلغ': item.amount,
                    'الملاحظات': item.notes || 'لا توجد ملاحظات'
                }));
                
                log('✅ تم تحضير البيانات');
                
                // إنشاء workbook
                const wb = XLSX.utils.book_new();
                const ws = XLSX.utils.json_to_sheet(excelData);
                
                log('✅ تم إنشاء worksheet');
                
                // إضافة الورقة
                XLSX.utils.book_append_sheet(wb, ws, 'بيانات الفواتير');
                
                log('✅ تم إضافة الورقة');
                
                // حفظ الملف
                const fileName = `test-invoices-${new Date().toISOString().split('T')[0]}.xlsx`;
                XLSX.writeFile(wb, fileName);
                
                log('✅ تم حفظ ملف Excel بنجاح!');
                debtManager.showSuccess(`تم تصدير ${data.length} سجل بنجاح إلى ملف Excel`);
                
            } catch (error) {
                log('❌ خطأ في Excel: ' + error.message);
                console.error('Excel Error:', error);
                debtManager.showError('حدث خطأ أثناء تصدير البيانات إلى Excel: ' + error.message);
            }
        }

        // فحص المكتبات عند التحميل
        window.addEventListener('load', function() {
            log('🔄 فحص المكتبات...');
            
            if (typeof window.jspdf !== 'undefined') {
                log('✅ مكتبة jsPDF محملة');
            } else {
                log('❌ مكتبة jsPDF غير محملة');
            }
            
            if (typeof window.XLSX !== 'undefined') {
                log('✅ مكتبة XLSX محملة');
            } else {
                log('❌ مكتبة XLSX غير محملة');
            }
            
            if (typeof window.html2canvas !== 'undefined') {
                log('✅ مكتبة html2canvas محملة');
            } else {
                log('❌ مكتبة html2canvas غير محملة');
            }
            
            log('✅ جاهز للاختبار!');
        });
    </script>
</body>
</html>
