@echo off
chcp 65001 >nul
title 🚀 بدء تثبيت نظام إدارة ديون العملاء

echo.
echo ========================================
echo 🚀 بدء تثبيت نظام إدارة ديون العملاء
echo    تطبيق سطح مكتب احترافي
echo ========================================
echo.

:: تعيين متغيرات البيئة
set "CURRENT_DIR=%CD%"
set "PROJECT_NAME=نظام إدارة ديون العملاء"

echo 📍 مجلد المشروع: %CURRENT_DIR%
echo.

:: التحقق من Node.js
echo 🔍 فحص Node.js...
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت!
    echo.
    echo 📥 يرجى تثبيت Node.js أولاً:
    echo 1. اذهب إلى: https://nodejs.org/
    echo 2. حمل النسخة LTS (الموصى بها)
    echo 3. ثبت Node.js وأعد تشغيل الكمبيوتر
    echo 4. ثم شغل هذا الملف مرة أخرى
    echo.
    pause
    start https://nodejs.org/
    exit /b 1
)

echo ✅ Node.js مثبت
for /f "tokens=*" %%i in ('node --version 2^>nul') do echo    الإصدار: %%i
echo.

:: التحقق من npm
echo 🔍 فحص npm...
where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ npm غير متوفر
    echo يرجى إعادة تثبيت Node.js
    pause
    exit /b 1
)

echo ✅ npm متوفر
for /f "tokens=*" %%i in ('npm --version 2^>nul') do echo    الإصدار: %%i
echo.

:: التحقق من ملفات المشروع
echo 🔍 فحص ملفات المشروع...
if not exist "package.json" (
    echo ❌ ملف package.json غير موجود
    echo تأكد من تشغيل هذا الملف في مجلد المشروع الصحيح
    pause
    exit /b 1
)

echo ✅ ملفات المشروع موجودة
echo.

:: إنشاء مجلد build إذا لم يكن موجوداً
if not exist "build" (
    echo 📁 إنشاء مجلد build...
    mkdir build
)

:: تثبيت المتطلبات
echo 📦 تثبيت المتطلبات...
echo هذا قد يستغرق بضع دقائق، يرجى الانتظار...
echo.

:: محاولة تنظيف التثبيت السابق
if exist "node_modules" (
    echo 🧹 تنظيف التثبيت السابق...
    rmdir /s /q node_modules 2>nul
)
if exist "package-lock.json" (
    del package-lock.json 2>nul
)

:: تثبيت المتطلبات
echo تثبيت المكتبات...
call npm install
if %errorlevel% neq 0 (
    echo.
    echo ❌ فشل في تثبيت المتطلبات
    echo.
    echo 🔧 محاولة حل المشكلة...
    echo تنظيف ذاكرة npm...
    call npm cache clean --force
    echo.
    echo إعادة المحاولة...
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل مرة أخرى
        echo.
        echo 💡 نصائح لحل المشكلة:
        echo 1. تأكد من اتصال الإنترنت
        echo 2. شغل هذا الملف كمدير (Run as Administrator)
        echo 3. أغلق برامج الحماية مؤقتاً
        echo 4. أعد تشغيل الكمبيوتر وحاول مرة أخرى
        echo.
        pause
        exit /b 1
    )
)

echo ✅ تم تثبيت المتطلبات بنجاح!
echo.

:: عرض خيارات التشغيل
echo 🎯 اختر ما تريد فعله:
echo.
echo 1. تشغيل التطبيق مباشرة (للتجربة)
echo 2. بناء مثبت Windows (.exe)
echo 3. بناء نسخة محمولة
echo 4. إنشاء أيقونات التطبيق
echo 5. تشغيل جميع الخطوات تلقائياً
echo 0. خروج
echo.

set /p choice="اختر رقم (0-5): "

if "%choice%"=="1" goto run_app
if "%choice%"=="2" goto build_installer
if "%choice%"=="3" goto build_portable
if "%choice%"=="4" goto create_icons
if "%choice%"=="5" goto auto_all
if "%choice%"=="0" goto exit
goto invalid_choice

:run_app
echo.
echo 🚀 تشغيل التطبيق...
echo.
call npm start
goto end

:build_installer
echo.
echo 🏗️ بناء مثبت Windows...
echo هذا قد يستغرق 5-10 دقائق...
echo.
call npm run build-win
if %errorlevel% equ 0 (
    echo.
    echo 🎉 تم بناء المثبت بنجاح!
    echo.
    if exist "dist" (
        echo 📁 الملفات المُنتجة في: %CD%\dist\
        dir /b dist\*.exe 2>nul
        echo.
        set /p open="هل تريد فتح مجلد الملفات؟ (y/n): "
        if /i "!open!"=="y" explorer dist
    )
) else (
    echo ❌ فشل في بناء المثبت
)
goto end

:build_portable
echo.
echo 📱 بناء نسخة محمولة...
echo.
call npm run pack
if %errorlevel% equ 0 (
    echo ✅ تم بناء النسخة المحمولة!
    echo 📁 الملفات في: dist\win-unpacked\
) else (
    echo ❌ فشل في بناء النسخة المحمولة
)
goto end

:create_icons
echo.
echo 🎨 إنشاء أيقونات التطبيق...
if exist "build\create-icons.html" (
    start build\create-icons.html
    echo ✅ تم فتح صفحة إنشاء الأيقونات
    echo يرجى تحميل الأيقونات ووضعها في مجلد build
) else (
    echo ❌ ملف إنشاء الأيقونات غير موجود
)
goto end

:auto_all
echo.
echo 🤖 تشغيل جميع الخطوات تلقائياً...
echo.

:: إنشاء الأيقونات
echo 🎨 فتح صفحة الأيقونات...
if exist "build\create-icons.html" (
    start build\create-icons.html
    echo يرجى تحميل الأيقونات والضغط على أي زر للمتابعة...
    pause
)

:: بناء المثبت
echo 🏗️ بناء مثبت Windows...
call npm run build-win
if %errorlevel% equ 0 (
    echo ✅ تم بناء المثبت بنجاح!
    if exist "dist" (
        echo 📁 فتح مجلد الملفات...
        explorer dist
    )
) else (
    echo ❌ فشل في بناء المثبت
)
goto end

:invalid_choice
echo ❌ اختيار غير صحيح
goto end

:exit
echo 👋 شكراً لاستخدام نظام إدارة ديون العملاء
exit /b 0

:end
echo.
echo 🎯 انتهت العملية
echo.
echo 💡 نصائح:
echo - للتشغيل مرة أخرى: npm start
echo - لبناء مثبت جديد: npm run build-win
echo - للمساعدة: راجع ملف دليل_التثبيت_النهائي.md
echo.
pause
