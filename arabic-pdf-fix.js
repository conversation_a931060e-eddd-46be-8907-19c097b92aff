// 🔧 إصلاح شامل لمشاكل الترميز العربي في PDF
// هذا الملف يحل مشكلة ظهور النصوص العربية كرموز غريبة

console.log('🔧 تحميل إصلاح الترميز العربي لـ PDF...');

// وظيفة إنشاء PDF آمن مع دعم كامل للنصوص العربية والإنجليزية
function createSafePDFWithMixedLanguages(data, title, type = 'report') {
    console.log('📄 إنشاء PDF آمن مع دعم اللغات المختلطة...');
    
    try {
        if (typeof window.jspdf === 'undefined') {
            throw new Error('مكتبة jsPDF غير متوفرة');
        }

        const { jsPDF } = window.jspdf;
        
        // إنشاء PDF مع إعدادات آمنة للترميز
        const pdf = new jsPDF({
            orientation: 'portrait',
            unit: 'mm',
            format: 'a4',
            compress: false, // تعطيل الضغط لتجنب مشاكل الترميز
            precision: 16,
            putOnlyUsedFonts: true
        });

        // إعدادات الصفحة
        const pageWidth = pdf.internal.pageSize.getWidth();
        const pageHeight = pdf.internal.pageSize.getHeight();
        const margin = 15;
        const contentWidth = pageWidth - (margin * 2);
        const rowHeight = 12;
        const headerHeight = 15;
        const rowsPerPage = Math.floor((pageHeight - 100) / rowHeight);

        let currentPage = 1;
        let totalAmount = 0;

        // حساب المجموع الكلي
        data.forEach(item => {
            totalAmount += parseFloat(item.amount) || 0;
        });

        console.log(`📊 سيتم إنشاء ${Math.ceil(data.length / rowsPerPage)} صفحة`);

        // وظيفة آمنة لمعالجة النصوص
        function processSafeText(text) {
            if (!text) return '-';
            
            let cleanText = String(text).trim();
            
            // إزالة الرموز الخاصة التي تسبب مشاكل
            cleanText = cleanText.replace(/[^\u0000-\u007F\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/g, '');
            
            // تحويل الأرقام العربية إلى إنجليزية
            cleanText = cleanText.replace(/[٠-٩]/g, function(d) {
                return String.fromCharCode(d.charCodeAt(0) - '٠'.charCodeAt(0) + '0'.charCodeAt(0));
            });
            
            // تقصير النص إذا كان طويلاً
            if (cleanText.length > 25) {
                cleanText = cleanText.substring(0, 22) + '...';
            }
            
            return cleanText || '-';
        }

        // وظيفة آمنة لإضافة النص
        function addSafeText(text, x, y, options = {}) {
            try {
                const safeText = processSafeText(text);
                pdf.text(safeText, x, y, options);
                return true;
            } catch (error) {
                console.warn('تحذير في إضافة النص:', error.message);
                try {
                    pdf.text('---', x, y, options);
                } catch (e) {
                    console.error('فشل في إضافة النص البديل');
                }
                return false;
            }
        }

        // وظيفة إضافة هيدر الصفحة
        function addPageHeader(pageNum) {
            // خلفية الهيدر
            pdf.setFillColor(44, 62, 80);
            pdf.rect(margin, margin, contentWidth, 35, 'F');

            // العنوان
            pdf.setTextColor(255, 255, 255);
            pdf.setFont('helvetica', 'bold');
            pdf.setFontSize(20);
            
            const safeTitle = processSafeText(title);
            const titleX = pageWidth / 2;
            addSafeText(safeTitle, titleX, margin + 20, { align: 'center' });

            // التاريخ ورقم الصفحة
            pdf.setFontSize(11);
            const currentDate = new Date().toLocaleDateString('en-US');
            addSafeText(`Date: ${currentDate}`, margin + 5, margin + 30);
            addSafeText(`Page ${pageNum}`, pageWidth - margin - 25, margin + 30);

            return margin + 45;
        }

        // وظيفة إضافة هيدر الجدول
        function addTableHeader(startY) {
            const colWidths = [25, 45, 30, 30, 40];
            let currentX = margin;

            // خلفية هيدر الجدول
            pdf.setFillColor(52, 73, 94);
            pdf.rect(margin, startY, contentWidth, headerHeight, 'F');

            // نص هيدر الجدول
            pdf.setTextColor(255, 255, 255);
            pdf.setFont('helvetica', 'bold');
            pdf.setFontSize(12);

            const headers = ['Invoice No.', 'Customer Name', 'City', 'Amount (SAR)', 'Notes'];
            
            headers.forEach((header, index) => {
                const headerX = currentX + (colWidths[index] / 2);
                addSafeText(header, headerX, startY + 10, { align: 'center' });
                currentX += colWidths[index];
            });

            // حدود الجدول
            pdf.setDrawColor(44, 62, 80);
            pdf.setLineWidth(0.5);
            
            currentX = margin;
            headers.forEach((_, index) => {
                pdf.line(currentX, startY, currentX, startY + headerHeight);
                currentX += colWidths[index];
            });
            pdf.line(currentX, startY, currentX, startY + headerHeight);
            pdf.line(margin, startY, margin + contentWidth, startY);
            pdf.line(margin, startY + headerHeight, margin + contentWidth, startY + headerHeight);

            return startY + headerHeight;
        }

        // وظيفة إضافة صف بيانات
        function addDataRow(item, startY, rowIndex) {
            const colWidths = [25, 45, 30, 30, 40];
            let currentX = margin;

            // لون خلفية الصف
            const rowColor = rowIndex % 2 === 0 ? [255, 255, 255] : [248, 249, 250];
            pdf.setFillColor(...rowColor);
            pdf.rect(margin, startY, contentWidth, rowHeight, 'F');

            // نص البيانات
            pdf.setTextColor(44, 62, 80);
            pdf.setFont('helvetica', 'normal');
            pdf.setFontSize(10);

            // تحضير البيانات مع معالجة آمنة
            const rowData = [
                processSafeText(item.invoiceNumber),
                processSafeText(item.customerName),
                processSafeText(item.city),
                (parseFloat(item.amount) || 0).toLocaleString('en-US'),
                processSafeText(item.notes)
            ];

            rowData.forEach((data, index) => {
                const cellX = currentX + (colWidths[index] / 2);
                addSafeText(data, cellX, startY + 8, { align: 'center' });
                currentX += colWidths[index];
            });

            // حدود الصف
            pdf.setDrawColor(222, 226, 230);
            pdf.setLineWidth(0.3);
            
            currentX = margin;
            colWidths.forEach(width => {
                pdf.line(currentX, startY, currentX, startY + rowHeight);
                currentX += width;
            });
            pdf.line(currentX, startY, currentX, startY + rowHeight);
            pdf.line(margin, startY + rowHeight, margin + contentWidth, startY + rowHeight);

            return startY + rowHeight;
        }

        // وظيفة إضافة تذييل الصفحة
        function addPageFooter(pageNum, isLastPage = false) {
            const footerY = pageHeight - 20;
            
            pdf.setTextColor(108, 117, 125);
            pdf.setFont('helvetica', 'normal');
            pdf.setFontSize(9);
            
            addSafeText('Customer Debt Management System', margin, footerY);
            
            if (isLastPage) {
                pdf.setFont('helvetica', 'bold');
                pdf.setFontSize(11);
                pdf.setTextColor(44, 62, 80);
                
                const totalText = `Total Amount: ${totalAmount.toLocaleString('en-US')} SAR`;
                const totalX = pageWidth / 2;
                addSafeText(totalText, totalX, footerY - 10, { align: 'center' });
            }
        }

        // إنشاء الصفحات
        for (let i = 0; i < data.length; i += rowsPerPage) {
            if (currentPage > 1) {
                pdf.addPage();
            }

            console.log(`📄 إنشاء الصفحة ${currentPage}...`);

            let currentY = addPageHeader(currentPage);
            currentY = addTableHeader(currentY);

            const pageData = data.slice(i, i + rowsPerPage);
            pageData.forEach((item, index) => {
                currentY = addDataRow(item, currentY, i + index);
            });

            const isLastPage = (i + rowsPerPage) >= data.length;
            addPageFooter(currentPage, isLastPage);

            currentPage++;
        }

        // حفظ الملف
        const fileName = `${processSafeText(title)}_Fixed_${new Date().toISOString().split('T')[0]}.pdf`;
        pdf.save(fileName);

        console.log(`✅ تم إنشاء PDF آمن: ${fileName}`);
        
        return {
            success: true,
            fileName: fileName,
            pages: currentPage - 1,
            records: data.length
        };

    } catch (error) {
        console.error('❌ خطأ في إنشاء PDF الآمن:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// وظيفة اختبار سريع
function testSafePDF() {
    console.log('🧪 اختبار PDF الآمن...');
    
    const testData = [
        {
            invoiceNumber: '001',
            customerName: 'أحمد محمد',
            city: 'الرياض',
            amount: 1500,
            notes: 'ملاحظة تجريبية'
        },
        {
            invoiceNumber: '002',
            customerName: 'Ahmed Mohamed',
            city: 'Riyadh',
            amount: 2500,
            notes: 'Test note'
        },
        {
            invoiceNumber: '003',
            customerName: 'فاطمة علي',
            city: 'جدة',
            amount: 3500,
            notes: 'معلومات إضافية'
        }
    ];
    
    const result = createSafePDFWithMixedLanguages(testData, 'تقرير اختبار الترميز الآمن', 'test');
    
    if (result.success) {
        alert(`✅ نجح الاختبار!\n\nالملف: ${result.fileName}\nالصفحات: ${result.pages}\nالسجلات: ${result.records}\n\n🔍 افتح الملف وتحقق من أن النصوص تظهر بشكل صحيح`);
    } else {
        alert(`❌ فشل الاختبار: ${result.error}`);
    }
}

// تصدير الوظائف
if (typeof window !== 'undefined') {
    window.createSafePDFWithMixedLanguages = createSafePDFWithMixedLanguages;
    window.testSafePDF = testSafePDF;
    console.log('✅ وظائف PDF الآمن متوفرة عالمياً');
}

console.log('🔥 تم تحميل إصلاح الترميز العربي بنجاح');
