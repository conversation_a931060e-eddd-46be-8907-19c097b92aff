<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الحل الجذري النهائي للترميز العربي</title>
    <script src="https://unpkg.com/jspdf@latest/dist/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="ultimate-arabic-pdf-solution.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 36px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .solution-section {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            border: 3px solid #28a745;
            border-radius: 15px;
            padding: 30px;
            margin: 25px 0;
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }
        .test-section {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border: 3px solid #ffc107;
            border-radius: 15px;
            padding: 30px;
            margin: 25px 0;
            box-shadow: 0 5px 15px rgba(255, 193, 7, 0.3);
        }
        .test-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 20px 30px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 18px;
            margin: 15px;
            transition: all 0.3s ease;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }
        .test-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.5);
        }
        .test-btn.ultimate {
            background: linear-gradient(135deg, #8e44ad, #9b59b6);
            font-size: 20px;
            padding: 25px 40px;
        }
        .test-btn.ultimate:hover {
            box-shadow: 0 8px 25px rgba(142, 68, 173, 0.5);
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .comparison-table th,
        .comparison-table td {
            border: 2px solid #dee2e6;
            padding: 15px;
            text-align: center;
        }
        .comparison-table th {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            font-weight: bold;
            font-size: 16px;
        }
        .old-method {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
        }
        .new-method {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
        }
        .test-data {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 16px;
        }
        .log {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            direction: ltr;
            text-align: left;
        }
        .arabic-text {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
            background: #e8f5e8;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            background: #e8f5e8;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 5px solid #28a745;
        }
        .feature-list li::before {
            content: "✅ ";
            font-weight: bold;
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 الحل الجذري النهائي للترميز العربي</h1>
        
        <div class="solution-section">
            <h3>🚀 الحل الجذري الجديد - HTML2Canvas + jsPDF:</h3>
            <p><strong>بدلاً من محاولة إصلاح مشاكل الترميز، نحن نتجاوزها تماماً!</strong></p>
            
            <ul class="feature-list">
                <li><strong>تحويل HTML إلى صورة:</strong> النصوص تظهر كما هي مكتوبة تماماً</li>
                <li><strong>لا مشاكل ترميز:</strong> الصورة تحتفظ بالنص الأصلي</li>
                <li><strong>دعم كامل للعربية:</strong> أسماء ومدن وملاحظات عربية</li>
                <li><strong>دعم كامل للإنجليزية:</strong> نصوص إنجليزية واضحة</li>
                <li><strong>نصوص مختلطة:</strong> عربية وإنجليزية في نفس الملف</li>
                <li><strong>تنسيق احترافي:</strong> جداول منسقة وألوان جذابة</li>
                <li><strong>جودة عالية:</strong> دقة 300 DPI للطباعة</li>
                <li><strong>صفحات متعددة:</strong> تقسيم تلقائي للبيانات الكبيرة</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>📊 مقارنة الطرق:</h3>
            
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>العنصر</th>
                        <th>الطرق السابقة</th>
                        <th>الحل الجذري الجديد</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>النصوص العربية</strong></td>
                        <td class="old-method">رموز غريبة (ÞªÞÞÞ)</td>
                        <td class="new-method">نص واضح (أحمد محمد)</td>
                    </tr>
                    <tr>
                        <td><strong>النصوص الإنجليزية</strong></td>
                        <td class="old-method">تعمل أحياناً</td>
                        <td class="new-method">تعمل دائماً</td>
                    </tr>
                    <tr>
                        <td><strong>النصوص المختلطة</strong></td>
                        <td class="old-method">مشاكل كبيرة</td>
                        <td class="new-method">دعم كامل</td>
                    </tr>
                    <tr>
                        <td><strong>التنسيق</strong></td>
                        <td class="old-method">بسيط</td>
                        <td class="new-method">احترافي مع ألوان</td>
                    </tr>
                    <tr>
                        <td><strong>الجودة</strong></td>
                        <td class="old-method">متوسطة</td>
                        <td class="new-method">عالية جداً</td>
                    </tr>
                    <tr>
                        <td><strong>الاستقرار</strong></td>
                        <td class="old-method">أخطاء متكررة</td>
                        <td class="new-method">استقرار كامل</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <h3>🧪 اختبارات شاملة:</h3>
            
            <div class="arabic-text">
                <strong>البيانات التجريبية:</strong><br>
                • أحمد محمد العلي - الرياض - 1500.75 ر.س<br>
                • فاطمة عبدالله - جدة - 2500.50 ر.س<br>
                • محمد سالم - الدمام - 3750.25 ر.س
            </div>

            <div style="text-align: center; margin: 40px 0;">
                <button class="test-btn ultimate" onclick="testUltimateArabicSolution()">
                    🔥 اختبار الحل الجذري النهائي
                </button>
                
                <button class="test-btn" onclick="testMixedContent()">
                    🌍 اختبار المحتوى المختلط
                </button>
                
                <button class="test-btn" onclick="testLargeDataset()">
                    📚 اختبار بيانات كبيرة
                </button>
                
                <button class="test-btn" onclick="testSpecialCharacters()">
                    🔤 اختبار الرموز الخاصة
                </button>
            </div>
        </div>

        <div id="log" class="log" style="display: none;"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            logDiv.style.display = 'block';
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type === 'error' ? 'color: red;' : type === 'success' ? 'color: green;' : type === 'warning' ? 'color: orange;' : '';
            logDiv.innerHTML += `<div style="${colorClass}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            const logDiv = document.getElementById('log');
            logDiv.innerHTML = '';
            logDiv.style.display = 'none';
        }

        async function testUltimateArabicSolution() {
            clearLog();
            log('🔥 اختبار الحل الجذري النهائي للترميز العربي...', 'info');
            
            if (typeof window.testArabicPDFSolution === 'function') {
                try {
                    log('📋 إنشاء بيانات تجريبية عربية...', 'info');
                    const result = await window.testArabicPDFSolution();
                    
                    if (result.success) {
                        log(`✅ نجح الحل الجذري: ${result.fileName}`, 'success');
                        log(`🔧 الطريقة المستخدمة: ${result.method}`, 'info');
                        log(`📊 عدد السجلات: ${result.records}`, 'info');
                    } else {
                        log(`❌ فشل الحل الجذري: ${result.error}`, 'error');
                    }
                } catch (error) {
                    log(`❌ خطأ في الاختبار: ${error.message}`, 'error');
                }
            } else {
                log('❌ وظيفة الاختبار غير متوفرة', 'error');
                alert('❌ وظيفة الاختبار غير متوفرة. تأكد من تحميل ultimate-arabic-pdf-solution.js');
            }
        }

        async function testMixedContent() {
            clearLog();
            log('🌍 اختبار المحتوى المختلط (عربي + إنجليزي)...', 'info');
            
            const mixedData = [
                {
                    invoiceNumber: '001',
                    customerName: 'أحمد محمد Ahmed',
                    city: 'الرياض Riyadh',
                    amount: 1500.75,
                    notes: 'ملاحظة عربية Arabic note'
                },
                {
                    invoiceNumber: '002',
                    customerName: 'Fatima فاطمة',
                    city: 'Jeddah جدة',
                    amount: 2500.50,
                    notes: 'Mixed content محتوى مختلط'
                },
                {
                    invoiceNumber: '003',
                    customerName: 'محمد Salem',
                    city: 'Dammam الدمام',
                    amount: 3750.25,
                    notes: 'English and عربي together'
                }
            ];
            
            if (typeof window.createArabicPDFFromHTML === 'function') {
                try {
                    log('📋 إنشاء PDF بمحتوى مختلط...', 'info');
                    const result = await window.createArabicPDFFromHTML(mixedData, 'اختبار المحتوى المختلط Mixed Content Test', 'mixed');
                    
                    if (result.success) {
                        log(`✅ نجح اختبار المحتوى المختلط: ${result.fileName}`, 'success');
                        alert(`✅ نجح الاختبار!\n\nالملف: ${result.fileName}\nالطريقة: ${result.method}\nالسجلات: ${result.records}\n\n🔍 افتح الملف وتحقق من أن النصوص العربية والإنجليزية تظهر معاً بوضوح`);
                    } else {
                        log(`❌ فشل اختبار المحتوى المختلط: ${result.error}`, 'error');
                        alert(`❌ فشل الاختبار: ${result.error}`);
                    }
                } catch (error) {
                    log(`❌ خطأ في اختبار المحتوى المختلط: ${error.message}`, 'error');
                }
            }
        }

        async function testLargeDataset() {
            clearLog();
            log('📚 اختبار بيانات كبيرة...', 'info');
            
            const largeData = [];
            const arabicNames = ['أحمد محمد', 'فاطمة علي', 'محمد سالم', 'نورا أحمد', 'خالد عبدالله', 'سارة محمود', 'عمر حسن', 'ليلى يوسف'];
            const arabicCities = ['الرياض', 'جدة', 'الدمام', 'مكة المكرمة', 'المدينة المنورة', 'الطائف', 'أبها', 'تبوك'];
            const arabicNotes = ['ملاحظة تجريبية', 'معلومات إضافية', 'تفاصيل مهمة', 'بيانات شاملة', 'ملاحظة مفصلة'];
            
            for (let i = 1; i <= 30; i++) {
                largeData.push({
                    invoiceNumber: String(i).padStart(3, '0'),
                    customerName: arabicNames[Math.floor(Math.random() * arabicNames.length)],
                    city: arabicCities[Math.floor(Math.random() * arabicCities.length)],
                    amount: Math.floor(Math.random() * 5000) + 500,
                    notes: arabicNotes[Math.floor(Math.random() * arabicNotes.length)]
                });
            }
            
            log(`📊 تم إنشاء ${largeData.length} سجل للاختبار...`, 'info');
            
            if (typeof window.createArabicPDFFromHTML === 'function') {
                try {
                    const result = await window.createArabicPDFFromHTML(largeData, 'اختبار البيانات الكبيرة Large Dataset Test', 'large');
                    
                    if (result.success) {
                        log(`✅ نجح اختبار البيانات الكبيرة: ${result.fileName}`, 'success');
                        alert(`✅ نجح الاختبار!\n\nالملف: ${result.fileName}\nالسجلات: ${result.records}\n\n🔍 تحقق من أن جميع النصوص العربية واضحة في الملف`);
                    } else {
                        log(`❌ فشل اختبار البيانات الكبيرة: ${result.error}`, 'error');
                    }
                } catch (error) {
                    log(`❌ خطأ في اختبار البيانات الكبيرة: ${error.message}`, 'error');
                }
            }
        }

        async function testSpecialCharacters() {
            clearLog();
            log('🔤 اختبار الرموز الخاصة...', 'info');
            
            const specialData = [
                {
                    invoiceNumber: '001',
                    customerName: 'أحمد محمد (الأب)',
                    city: 'الرياض - المملكة',
                    amount: 1500.75,
                    notes: 'ملاحظة: دفع جزئي 50%'
                },
                {
                    invoiceNumber: '002',
                    customerName: 'شركة الأعمال & الشراكة',
                    city: 'جدة @ السعودية',
                    amount: 2500.50,
                    notes: 'تفاصيل: دفعة أولى + رسوم'
                },
                {
                    invoiceNumber: '003',
                    customerName: 'محمد علي [المدير]',
                    city: 'الدمام / الشرقية',
                    amount: 3750.25,
                    notes: 'ملاحظة خاصة: "دفع نقدي"'
                }
            ];
            
            if (typeof window.createArabicPDFFromHTML === 'function') {
                try {
                    const result = await window.createArabicPDFFromHTML(specialData, 'اختبار الرموز الخاصة Special Characters', 'special');
                    
                    if (result.success) {
                        log(`✅ نجح اختبار الرموز الخاصة: ${result.fileName}`, 'success');
                        alert(`✅ نجح الاختبار!\n\nالملف: ${result.fileName}\n\n🔍 تحقق من معالجة الرموز الخاصة مثل الأقواس والنقاط والرموز`);
                    } else {
                        log(`❌ فشل اختبار الرموز الخاصة: ${result.error}`, 'error');
                    }
                } catch (error) {
                    log(`❌ خطأ في اختبار الرموز الخاصة: ${error.message}`, 'error');
                }
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('🔥 صفحة اختبار الحل الجذري النهائي جاهزة', 'success');
            
            if (typeof window.createArabicPDFFromHTML === 'function') {
                log('✅ الحل الجذري متوفر ومحمل بنجاح', 'success');
            } else {
                log('❌ الحل الجذري غير متوفر', 'error');
            }
            
            if (typeof window.html2canvas !== 'undefined') {
                log('✅ مكتبة html2canvas متوفرة', 'success');
            } else {
                log('❌ مكتبة html2canvas غير متوفرة', 'error');
            }
            
            if (typeof window.jspdf !== 'undefined') {
                log('✅ مكتبة jsPDF متوفرة', 'success');
            } else {
                log('❌ مكتبة jsPDF غير متوفرة', 'error');
            }
        });
    </script>
</body>
</html>
