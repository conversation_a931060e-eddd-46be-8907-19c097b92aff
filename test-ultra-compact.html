<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التصميم المدمج جداً</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 50px rgba(0,0,0,0.4);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 42px;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .compact-banner {
            background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
            border: 4px solid #e17055;
            border-radius: 20px;
            padding: 40px;
            margin: 30px 0;
            box-shadow: 0 10px 30px rgba(225, 112, 85, 0.4);
            text-align: center;
        }
        
        /* نسخ الأنماط المدمجة الجديدة */
        .stats-section {
            background: #f8fafc;
            border-radius: 8px;
            padding: 10px;
            border: 1px solid #e2e8f0;
            margin: 20px 0;
        }
        .stats-section h5 {
            margin: 0 0 8px 0;
            color: #1e293b;
            font-weight: 600;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        .top-customers-list {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }
        .top-customer-item {
            background: linear-gradient(135deg, #ffffff, #f8fafc);
            border-radius: 6px;
            padding: 3px 8px;
            display: flex;
            align-items: center;
            gap: 6px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            min-height: 24px;
            max-height: 24px;
        }
        .top-customer-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            border-color: #4f46e5;
        }
        .top-customer-item:hover .customer-rank {
            transform: scale(1.1);
            box-shadow: 0 3px 6px rgba(79, 70, 229, 0.4);
        }
        .top-customer-item:hover .customer-amount {
            background: linear-gradient(135deg, #a7f3d0, #6ee7b7);
            border-color: #059669;
            transform: scale(1.05);
        }
        .customer-rank {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 0.6rem;
            box-shadow: 0 1px 2px rgba(79, 70, 229, 0.3);
            flex-shrink: 0;
        }
        .customer-info {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
            min-width: 0;
        }
        .customer-details {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 8px;
            flex: 1;
            min-width: 0;
        }
        .customer-stats {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 1px;
            flex-shrink: 0;
        }
        .customer-name {
            font-weight: 600;
            color: #1e293b;
            font-size: 0.7rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100px;
        }
        .customer-city {
            font-size: 0.65rem;
            color: #64748b;
            display: flex;
            align-items: center;
            gap: 2px;
            white-space: nowrap;
        }
        .customer-city::before {
            content: "📍";
            font-size: 0.6rem;
        }
        .customer-amount {
            font-weight: 700;
            color: #059669;
            font-size: 0.7rem;
            background: linear-gradient(135deg, #d1fae5, #a7f3d0);
            padding: 1px 4px;
            border-radius: 3px;
            border: 1px solid #10b981;
            white-space: nowrap;
        }
        .customer-invoices {
            font-size: 0.6rem;
            color: #64748b;
            white-space: nowrap;
        }
        .customer-invoices-list {
            font-size: 8px;
            color: #6366f1;
            display: inline-block;
            margin: 0;
            font-style: italic;
            background: linear-gradient(135deg, #e0e7ff, #c7d2fe);
            padding: 0px 3px;
            border-radius: 2px;
            border: 1px solid #a5b4fc;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 80px;
        }
        
        .comparison-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 30px 0;
        }
        .before-after {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            border: 2px solid #dee2e6;
        }
        .before {
            border-color: #dc3545;
        }
        .after {
            border-color: #28a745;
        }
        .before h3 {
            color: #dc3545;
        }
        .after h3 {
            color: #28a745;
        }
        .old-item {
            background: white;
            padding: 12px;
            margin: 10px 0;
            border-radius: 8px;
            min-height: 50px;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .old-rank {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📦 التصميم المدمج جداً</h1>
        
        <div class="compact-banner">
            <h2 style="color: #d63031; font-size: 32px; margin-bottom: 20px;">🔥 أقصى درجات الإدماج</h2>
            <p style="font-size: 18px; color: #d63031;">ارتفاع 24px فقط مع توزيع عرضي للبيانات</p>
        </div>

        <div class="comparison-section">
            <div class="before-after before">
                <h3>❌ قبل التحسين (ارتفاع 50px+)</h3>
                <div class="old-item">
                    <div class="old-rank">1</div>
                    <div style="flex: 1;">
                        <div style="font-size: 16px; font-weight: bold;">أحمد محمد العلي</div>
                        <div style="font-size: 14px; color: #666;">الرياض</div>
                        <div style="font-size: 12px; color: #666; margin-top: 5px;">الفواتير: 001, 002</div>
                    </div>
                    <div style="text-align: left;">
                        <div style="font-size: 16px; color: #059669; font-weight: bold;">15,750.75 ر.س</div>
                        <div style="font-size: 12px; color: #666;">2 فاتورة</div>
                    </div>
                </div>
                <p style="color: #dc3545; font-size: 12px;">ارتفاع كبير + تخطيط عمودي</p>
            </div>

            <div class="before-after after">
                <h3>✅ بعد التحسين (ارتفاع 24px)</h3>
                <div class="stats-section" style="margin: 0;">
                    <div class="top-customers-list">
                        <div class="top-customer-item">
                            <span class="customer-rank">1</span>
                            <div class="customer-info">
                                <div class="customer-details">
                                    <span class="customer-name" title="أحمد محمد العلي">أحمد محمد العلي</span>
                                    <span class="customer-city">الرياض</span>
                                    <span class="customer-invoices-list" title="الفواتير: 001, 002">001, 002</span>
                                </div>
                                <div class="customer-stats">
                                    <span class="customer-amount">15,750.75 ر.س</span>
                                    <span class="customer-invoices">2ف</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <p style="color: #28a745; font-size: 12px;">ارتفاع مدمج + تخطيط عرضي</p>
            </div>
        </div>

        <div class="stats-section">
            <h5><i class="fas fa-crown" style="color: #fbbf24;"></i> أكبر العملاء - التصميم المدمج</h5>
            <div class="top-customers-list">
                <div class="top-customer-item">
                    <span class="customer-rank">1</span>
                    <div class="customer-info">
                        <div class="customer-details">
                            <span class="customer-name" title="أحمد محمد العلي الطويل جداً">أحمد محمد العلي الطويل جداً</span>
                            <span class="customer-city">الرياض</span>
                            <span class="customer-invoices-list" title="الفواتير: 001, 002, 003, 004">001, 002, 003, 004</span>
                        </div>
                        <div class="customer-stats">
                            <span class="customer-amount">15,750.75 ر.س</span>
                            <span class="customer-invoices">4ف</span>
                        </div>
                    </div>
                </div>

                <div class="top-customer-item">
                    <span class="customer-rank">2</span>
                    <div class="customer-info">
                        <div class="customer-details">
                            <span class="customer-name" title="فاطمة عبدالله الزهراني">فاطمة عبدالله الزهراني</span>
                            <span class="customer-city">جدة</span>
                            <span class="customer-invoices-list" title="الفواتير: 005, 006">005, 006</span>
                        </div>
                        <div class="customer-stats">
                            <span class="customer-amount">12,500.50 ر.س</span>
                            <span class="customer-invoices">2ف</span>
                        </div>
                    </div>
                </div>

                <div class="top-customer-item">
                    <span class="customer-rank">3</span>
                    <div class="customer-info">
                        <div class="customer-details">
                            <span class="customer-name" title="محمد سالم القحطاني">محمد سالم القحطاني</span>
                            <span class="customer-city">الدمام</span>
                            <span class="customer-invoices-list" title="الفواتير: 007">007</span>
                        </div>
                        <div class="customer-stats">
                            <span class="customer-amount">8,750.25 ر.س</span>
                            <span class="customer-invoices">1ف</span>
                        </div>
                    </div>
                </div>

                <div class="top-customer-item">
                    <span class="customer-rank">4</span>
                    <div class="customer-info">
                        <div class="customer-details">
                            <span class="customer-name" title="خالد أحمد المطيري">خالد أحمد المطيري</span>
                            <span class="customer-city">المدينة المنورة</span>
                            <span class="customer-invoices-list" title="الفواتير: 008, 009, 010">008, 009, 010</span>
                        </div>
                        <div class="customer-stats">
                            <span class="customer-amount">7,200.00 ر.س</span>
                            <span class="customer-invoices">3ف</span>
                        </div>
                    </div>
                </div>

                <div class="top-customer-item">
                    <span class="customer-rank">5</span>
                    <div class="customer-info">
                        <div class="customer-details">
                            <span class="customer-name" title="نورا عبدالله الغامدي">نورا عبدالله الغامدي</span>
                            <span class="customer-city">مكة المكرمة</span>
                            <span class="customer-invoices-list" title="الفواتير: 011">011</span>
                        </div>
                        <div class="customer-stats">
                            <span class="customer-amount">5,800.25 ر.س</span>
                            <span class="customer-invoices">1ف</span>
                        </div>
                    </div>
                </div>

                <div class="top-customer-item">
                    <span class="customer-rank">6</span>
                    <div class="customer-info">
                        <div class="customer-details">
                            <span class="customer-name" title="سارة أحمد الشهري">سارة أحمد الشهري</span>
                            <span class="customer-city">أبها</span>
                            <span class="customer-invoices-list" title="الفواتير: 012, 013">012, 013</span>
                        </div>
                        <div class="customer-stats">
                            <span class="customer-amount">4,500.00 ر.س</span>
                            <span class="customer-invoices">2ف</span>
                        </div>
                    </div>
                </div>

                <div class="top-customer-item">
                    <span class="customer-rank">7</span>
                    <div class="customer-info">
                        <div class="customer-details">
                            <span class="customer-name" title="عبدالله محمد الحربي">عبدالله محمد الحربي</span>
                            <span class="customer-city">الطائف</span>
                            <span class="customer-invoices-list" title="الفواتير: 014">014</span>
                        </div>
                        <div class="customer-stats">
                            <span class="customer-amount">3,200.75 ر.س</span>
                            <span class="customer-invoices">1ف</span>
                        </div>
                    </div>
                </div>

                <div class="top-customer-item">
                    <span class="customer-rank">8</span>
                    <div class="customer-info">
                        <div class="customer-details">
                            <span class="customer-name" title="مريم سالم العتيبي">مريم سالم العتيبي</span>
                            <span class="customer-city">بريدة</span>
                            <span class="customer-invoices-list" title="الفواتير: 015, 016, 017">015, 016, 017</span>
                        </div>
                        <div class="customer-stats">
                            <span class="customer-amount">2,800.50 ر.س</span>
                            <span class="customer-invoices">3ف</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div style="background: #f8f9fa; padding: 20px; border-radius: 15px; margin: 30px 0;">
            <h3>📏 مقارنة الأحجام الجديدة:</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
                <div>
                    <h4 style="color: #dc3545;">قبل التحسين:</h4>
                    <ul style="font-size: 14px; color: #666;">
                        <li>ارتفاع العنصر: 50px+</li>
                        <li>رقم الترتيب: 30px</li>
                        <li>تخطيط: عمودي</li>
                        <li>المسافات: 10px</li>
                        <li>أرقام الفواتير: سطر منفصل</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color: #28a745;">بعد التحسين:</h4>
                    <ul style="font-size: 14px; color: #666;">
                        <li>ارتفاع العنصر: 24px</li>
                        <li>رقم الترتيب: 18px</li>
                        <li>تخطيط: عرضي</li>
                        <li>المسافات: 2px</li>
                        <li>أرقام الفواتير: نفس السطر</li>
                    </ul>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; padding: 20px; background: #ffeaa7; border-radius: 15px;">
            <h3 style="color: #d63031;">🔥 تم تحقيق أقصى إدماج!</h3>
            <p style="color: #d63031; font-size: 18px;">ارتفاع 24px فقط مع عرض 8 عملاء في مساحة صغيرة جداً</p>
            <div style="margin-top: 20px;">
                <span style="background: #e17055; color: white; padding: 10px 20px; border-radius: 25px; font-weight: bold;">
                    توفير 50%+ من المساحة
                </span>
            </div>
        </div>
    </div>
</body>
</html>
