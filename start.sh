#!/bin/bash

echo "========================================"
echo "    نظام إدارة ديون العملاء"
echo "    Customer Debt Management System"
echo "========================================"
echo

echo "[1/4] تحقق من تثبيت Python..."
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ خطأ: Python غير مثبت على النظام"
        echo "يرجى تثبيت Python 3.7 أو أحدث"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi
echo "✅ Python مثبت بنجاح"

echo
echo "[2/4] تثبيت المتطلبات..."
cd python
if ! $PYTHON_CMD -m pip install -r requirements.txt > /dev/null 2>&1; then
    echo "❌ خطأ في تثبيت المتطلبات"
    echo "يرجى التحقق من الاتصال بالإنترنت"
    exit 1
fi
echo "✅ تم تثبيت المتطلبات بنجاح"

echo
echo "[3/4] إنشاء مجلد البيانات..."
mkdir -p ../data
if [ ! -f "../data/customers.json" ]; then
    echo '{"debts": []}' > ../data/customers.json
fi
echo "✅ تم إعداد قاعدة البيانات"

echo
echo "[4/4] تشغيل الخادم..."
echo "🌐 سيتم فتح النظام على: http://localhost:5000"
echo "📊 لوحة الإدارة جاهزة للاستخدام"
echo
echo "⚠️  للإيقاف اضغط Ctrl+C"
echo

# Try to open browser
if command -v xdg-open &> /dev/null; then
    xdg-open http://localhost:5000 &
elif command -v open &> /dev/null; then
    open http://localhost:5000 &
fi

sleep 3

$PYTHON_CMD app.py

echo
echo "تم إيقاف الخادم"
