@echo off
chcp 65001 >nul
title نظام إدارة ديون العملاء - تشغيل سطح المكتب

echo.
echo ========================================
echo    نظام إدارة ديون العملاء
echo    تشغيل كبرنامج سطح مكتب
echo ========================================
echo.

:: التحقق من وجود Node.js
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ خطأ: Node.js غير مثبت على النظام
    echo.
    echo 📥 يرجى تحميل وتثبيت Node.js من:
    echo https://nodejs.org/
    echo.
    pause
    exit /b 1
)

:: عرض إصدار Node.js
echo ✅ Node.js مثبت - الإصدار:
node --version
echo.

:: التحقق من وجود package.json
if not exist "package.json" (
    echo ❌ خطأ: ملف package.json غير موجود
    echo تأكد من تشغيل الأمر في مجلد المشروع الصحيح
    echo.
    pause
    exit /b 1
)

:: التحقق من وجود node_modules
if not exist "node_modules" (
    echo 📦 تثبيت المتطلبات لأول مرة...
    echo هذا قد يستغرق بضع دقائق...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت المتطلبات
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت المتطلبات بنجاح
    echo.
)

:: عرض الخيارات
echo 🚀 اختر طريقة التشغيل:
echo.
echo 1. تشغيل التطبيق (عادي)
echo 2. تشغيل التطبيق (مع أدوات المطور)
echo 3. بناء التطبيق للتوزيع
echo 4. بناء لـ Windows فقط
echo 5. بناء لجميع المنصات
echo 6. تنظيف وإعادة تثبيت المتطلبات
echo 7. عرض معلومات المشروع
echo 0. خروج
echo.

set /p choice="اختر رقم (0-7): "

if "%choice%"=="1" goto run_normal
if "%choice%"=="2" goto run_dev
if "%choice%"=="3" goto build_all
if "%choice%"=="4" goto build_win
if "%choice%"=="5" goto build_all_platforms
if "%choice%"=="6" goto clean_install
if "%choice%"=="7" goto show_info
if "%choice%"=="0" goto exit
goto invalid_choice

:run_normal
echo.
echo 🚀 تشغيل التطبيق...
echo.
npm start
goto end

:run_dev
echo.
echo 🔧 تشغيل التطبيق مع أدوات المطور...
echo.
npm run dev
goto end

:build_all
echo.
echo 🏗️ بناء التطبيق للتوزيع...
echo هذا قد يستغرق عدة دقائق...
echo.
npm run build
if %errorlevel% equ 0 (
    echo ✅ تم بناء التطبيق بنجاح!
    echo 📁 الملفات متوفرة في مجلد: dist\
    echo.
    if exist "dist" (
        echo 📋 الملفات المُنتجة:
        dir /b dist\*.exe 2>nul
        dir /b dist\*.msi 2>nul
    )
) else (
    echo ❌ فشل في بناء التطبيق
)
goto end

:build_win
echo.
echo 🪟 بناء التطبيق لـ Windows...
echo.
npm run build-win
if %errorlevel% equ 0 (
    echo ✅ تم بناء التطبيق لـ Windows بنجاح!
    echo 📁 الملفات متوفرة في مجلد: dist\
) else (
    echo ❌ فشل في بناء التطبيق
)
goto end

:build_all_platforms
echo.
echo 🌍 بناء التطبيق لجميع المنصات...
echo هذا قد يستغرق وقت طويل...
echo.
echo 🪟 بناء Windows...
npm run build-win
echo 🍎 بناء Mac...
npm run build-mac
echo 🐧 بناء Linux...
npm run build-linux
echo ✅ تم الانتهاء من البناء لجميع المنصات
goto end

:clean_install
echo.
echo 🧹 تنظيف وإعادة تثبيت المتطلبات...
echo.
if exist "node_modules" (
    echo حذف node_modules...
    rmdir /s /q node_modules
)
if exist "package-lock.json" (
    echo حذف package-lock.json...
    del package-lock.json
)
echo إعادة تثبيت المتطلبات...
npm install
if %errorlevel% equ 0 (
    echo ✅ تم تنظيف وإعادة تثبيت المتطلبات بنجاح
) else (
    echo ❌ فشل في إعادة التثبيت
)
goto end

:show_info
echo.
echo 📋 معلومات المشروع:
echo ==================
echo.
if exist "package.json" (
    findstr /c:"\"name\"" /c:"\"version\"" /c:"\"description\"" package.json
)
echo.
echo 📊 إحصائيات:
if exist "html" echo ✅ ملفات HTML موجودة
if exist "css" echo ✅ ملفات CSS موجودة  
if exist "javascript" echo ✅ ملفات JavaScript موجودة
if exist "electron" echo ✅ ملفات Electron موجودة
if exist "build" echo ✅ مجلد البناء موجود
if exist "node_modules" echo ✅ المتطلبات مثبتة
echo.
goto end

:invalid_choice
echo.
echo ❌ اختيار غير صحيح. يرجى اختيار رقم من 0 إلى 7
echo.
pause
goto start

:exit
echo.
echo 👋 شكراً لاستخدام نظام إدارة ديون العملاء
echo.
exit /b 0

:end
echo.
echo 🎯 انتهت العملية
echo.
pause
