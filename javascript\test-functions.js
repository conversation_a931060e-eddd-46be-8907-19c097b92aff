// Test Functions for Debugging

// Test PDF function
function testPDF() {
    console.log('🧪 Testing PDF function...');

    // Check if jsPDF is loaded
    if (typeof window.jspdf === 'undefined') {
        console.error('❌ jsPDF library not loaded');
        alert('مكتبة PDF غير محملة');
        return;
    }

    console.log('✅ jsPDF library loaded');

    // Check if debtManager exists
    if (typeof window.debtManager === 'undefined') {
        console.error('❌ debtManager not available');
        alert('نظام إدارة الديون غير جاهز');
        return;
    }

    console.log('✅ debtManager available');

    // Try to create a simple PDF
    try {
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF();
        doc.text('Test PDF', 20, 20);
        doc.save('test.pdf');
        console.log('✅ PDF test successful');
        alert('تم إنشاء PDF تجريبي بنجاح');
    } catch (error) {
        console.error('❌ PDF test failed:', error);
        alert('فشل في إنشاء PDF: ' + error.message);
    }
}

// Test CSV export function
function testCSV() {
    console.log('🧪 Testing CSV export...');

    // Create test data
    const testData = [
        { invoiceNumber: 'TEST001', customerName: 'عميل تجريبي', city: 'الرياض', amount: 1000, notes: 'اختبار' }
    ];

    try {
        // Create CSV content
        let csvContent = '\uFEFF'; // UTF-8 BOM
        csvContent += 'رقم الفاتورة,اسم العميل,المدينة,المبلغ,الملاحظات\n';

        testData.forEach(item => {
            const row = [
                `"${item.invoiceNumber}"`,
                `"${item.customerName}"`,
                `"${item.city}"`,
                `"${item.amount}"`,
                `"${item.notes}"`
            ];
            csvContent += row.join(',') + '\n';
        });

        // Create and download file
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        link.setAttribute('href', url);
        link.setAttribute('download', 'test.csv');
        link.style.visibility = 'hidden';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        console.log('✅ CSV test successful');
        alert('تم إنشاء ملف CSV تجريبي بنجاح');

    } catch (error) {
        console.error('❌ CSV test failed:', error);
        alert('فشل في إنشاء CSV: ' + error.message);
    }
}

// Test notification function
function testNotification() {
    console.log('🧪 Testing notifications...');

    if (typeof window.debtManager === 'undefined') {
        console.error('❌ debtManager not available');
        alert('نظام إدارة الديون غير جاهز');
        return;
    }

    try {
        debtManager.showSuccess('هذا اختبار للإشعارات الناجحة ✅');
        setTimeout(() => {
            debtManager.showError('هذا اختبار للإشعارات الخطأ ❌');
        }, 2000);

        console.log('✅ Notification test successful');
    } catch (error) {
        console.error('❌ Notification test failed:', error);
        alert('فشل في اختبار الإشعارات: ' + error.message);
    }
}

// Test file import function
function testFileImport() {
    console.log('🧪 Testing file import...');

    // Create a test CSV content
    const testCSVContent = '\uFEFF' + // UTF-8 BOM
        'رقم الفاتورة,اسم العميل,المدينة,المبلغ,الملاحظات\n' +
        '"TEST001","عميل تجريبي 1","الرياض","1500","اختبار 1"\n' +
        '"TEST002","عميل تجريبي 2","جدة","2000","اختبار 2"';

    // Create a blob and file
    const blob = new Blob([testCSVContent], { type: 'text/csv' });
    const file = new File([blob], 'test.csv', { type: 'text/csv' });

    // Test the import function
    if (typeof window.importFromCSV === 'function') {
        try {
            importFromCSV(file, 'new');
            console.log('✅ File import test initiated');
        } catch (error) {
            console.error('❌ File import test failed:', error);
            alert('فشل في اختبار استيراد الملف: ' + error.message);
        }
    } else {
        console.error('❌ importFromCSV function not available');
        alert('وظيفة استيراد CSV غير متوفرة');
    }
}

// Check all libraries
function checkLibraries() {
    console.log('🔍 Checking all libraries...');

    const libraries = [
        { name: 'jsPDF', check: () => typeof window.jspdf !== 'undefined' },
        { name: 'XLSX', check: () => typeof window.XLSX !== 'undefined' },
        { name: 'html2canvas', check: () => typeof window.html2canvas !== 'undefined' },
        { name: 'debtManager', check: () => typeof window.debtManager !== 'undefined' },
        { name: 'exportToCSV', check: () => typeof window.exportToCSV === 'function' },
        { name: 'importFromFile', check: () => typeof window.importFromFile === 'function' },
        { name: 'printPDF', check: () => typeof window.printPDF === 'function' }
    ];

    let report = '📊 تقرير المكتبات:\n\n';
    let allGood = true;

    libraries.forEach(lib => {
        const status = lib.check();
        const icon = status ? '✅' : '❌';
        report += `${icon} ${lib.name}: ${status ? 'محمل' : 'غير محمل'}\n`;
        if (!status) allGood = false;
    });

    console.log(report);
    alert(report + (allGood ? '\n🎉 جميع المكتبات محملة بنجاح!' : '\n⚠️ بعض المكتبات غير محملة'));
}

// Add test buttons to page
function addTestButtons() {
    const testContainer = document.createElement('div');
    testContainer.style.cssText = `
        position: fixed;
        bottom: 20px;
        left: 20px;
        background: white;
        padding: 15px;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        z-index: 9999;
        direction: ltr;
    `;

    testContainer.innerHTML = `
        <h6 style="margin: 0 0 10px 0; color: #333;">Test Functions</h6>
        <button onclick="checkLibraries()" style="margin: 2px; padding: 5px 10px; font-size: 12px;">Check Libraries</button><br>
        <button onclick="testNotification()" style="margin: 2px; padding: 5px 10px; font-size: 12px;">Test Notifications</button><br>
        <button onclick="testCSV()" style="margin: 2px; padding: 5px 10px; font-size: 12px;">Test CSV</button><br>
        <button onclick="testPDF()" style="margin: 2px; padding: 5px 10px; font-size: 12px;">Test PDF</button><br>
        <button onclick="testFileImport()" style="margin: 2px; padding: 5px 10px; font-size: 12px;">Test Import</button><br>
        <button onclick="document.body.removeChild(this.parentElement)" style="margin: 2px; padding: 5px 10px; font-size: 12px; background: #ff4444; color: white;">Hide</button>
    `;

    document.body.appendChild(testContainer);
}

// Make functions global
window.testPDF = testPDF;
window.testCSV = testCSV;
window.testNotification = testNotification;
window.testFileImport = testFileImport;
window.checkLibraries = checkLibraries;
window.addTestButtons = addTestButtons;

// Auto-add test buttons when page loads (disabled by default)
// document.addEventListener('DOMContentLoaded', () => {
//     setTimeout(addTestButtons, 2000);
// });

console.log('🧪 Test functions loaded. Use addTestButtons() to show test panel.');
