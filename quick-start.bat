@echo off
chcp 65001 >nul
title نظام إدارة ديون العملاء - بدء سريع
color 0B

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    نظام إدارة ديون العملاء                    ║
echo ║                  Customer Debt Management System             ║
echo ║                         بدء سريع                            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo [المرحلة 1/3] فحص النظام...
echo.

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo.
    echo 💡 يرجى تثبيت Python من: https://python.org
    echo    اختر "Add Python to PATH" أثناء التثبيت
    echo.
    pause
    exit /b 1
)
echo ✅ Python متوفر

REM Check pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير متوفر
    pause
    exit /b 1
)
echo ✅ pip متوفر

REM Check if data directory exists
if not exist "data" (
    echo 📁 إنشاء مجلد البيانات...
    mkdir "data"
)

REM Check if database file exists
if not exist "data\customers.json" (
    echo 💾 إنشاء قاعدة البيانات...
    echo {"debts": []} > "data\customers.json"
)

echo ✅ فحص النظام مكتمل
echo.

echo [المرحلة 2/3] تثبيت المتطلبات...
cd /d "%~dp0python"

echo 📦 تثبيت Flask والمكتبات المطلوبة...
pip install -r requirements.txt --quiet --disable-pip-version-check --user
if errorlevel 1 (
    echo ⚠️  محاولة تثبيت بدون --user...
    pip install -r requirements.txt --quiet --disable-pip-version-check
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المتطلبات
        echo.
        echo 💡 جرب الحلول التالية:
        echo    1. تشغيل كمدير (Run as Administrator)
        echo    2. تحديث pip: python -m pip install --upgrade pip
        echo    3. استخدام: pip install flask flask-cors
        echo.
        pause
        exit /b 1
    )
)

echo ✅ تم تثبيت المتطلبات
echo.

echo [المرحلة 3/3] تشغيل النظام...
cd /d "%~dp0"

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║  🚀 النظام جاهز للتشغيل                                      ║
echo ║                                                              ║
echo ║  🌐 العنوان: http://localhost:5000                          ║
echo ║  📊 لوحة الإدارة ستفتح تلقائياً                             ║
echo ║  💾 البيانات: data\customers.json                           ║
echo ║                                                              ║
echo ║  ⚠️  للإيقاف: اضغط Ctrl+C                                   ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo انتظار 3 ثوان لفتح المتصفح...
timeout /t 3 >nul

echo 🌐 فتح المتصفح...
start "" "http://localhost:5000"

echo.
echo 🚀 بدء تشغيل الخادم...
echo ════════════════════════════════════════════════════════════════
cd /d "%~dp0python"
python app.py

echo.
echo ════════════════════════════════════════════════════════════════
echo 🛑 تم إيقاف الخادم
echo 💾 البيانات محفوظة في: %~dp0data\customers.json
echo 🔄 لإعادة التشغيل: قم بتشغيل هذا الملف مرة أخرى
echo ════════════════════════════════════════════════════════════════
echo.
pause
