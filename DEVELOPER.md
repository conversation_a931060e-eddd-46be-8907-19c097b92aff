# دليل المطور - نظام إدارة ديون العملاء

## 🏗️ هيكل المشروع

```
الديون/
├── html/                    # ملفات HTML
│   └── index.html          # الواجهة الرئيسية
├── css/                    # ملفات التنسيق
│   └── style.css          # التنسيقات الرئيسية
├── javascript/             # ملفات JavaScript
│   └── app.js             # منطق الواجهة الأمامية
├── python/                 # ملفات Python
│   ├── app.py             # خادم Flask الرئيسي
│   ├── database.py        # مدير قاعدة البيانات
│   ├── requirements.txt   # متطلبات Python
│   └── test_system.py     # اختبارات النظام
├── data/                   # مجلد البيانات
│   └── customers.json     # ملف قاعدة البيانات
├── config.json            # ملف التكوين
├── start.bat              # ملف تشغيل Windows
├── start.sh               # ملف تشغيل Linux/Mac
├── README.md              # دليل المشروع
├── دليل_المستخدم.md       # دليل المستخدم
└── DEVELOPER.md           # هذا الملف
```

## 🔧 التقنيات المستخدمة

### Frontend
- **HTML5**: هيكل الصفحة مع دعم RTL
- **CSS3**: تنسيقات متقدمة مع Bootstrap 5
- **JavaScript ES6+**: منطق التطبيق والتفاعل
- **Bootstrap 5**: إطار عمل UI متجاوب
- **Font Awesome**: أيقونات احترافية

### Backend
- **Python 3.7+**: لغة البرمجة الأساسية
- **Flask 2.3.3**: إطار عمل الويب
- **Flask-CORS**: دعم CORS للطلبات
- **JSON**: تخزين البيانات

## 📡 API Endpoints

### GET /
- **الوصف**: الصفحة الرئيسية
- **الاستجابة**: HTML page

### GET /api/debts
- **الوصف**: جلب جميع السجلات
- **الاستجابة**: 
```json
{
  "success": true,
  "data": [...],
  "total_records": 10
}
```

### POST /api/debts
- **الوصف**: إضافة سجل جديد
- **البيانات المطلوبة**:
```json
{
  "invoiceNumber": "INV-001",
  "customerName": "أحمد محمد",
  "city": "الرياض",
  "amount": 1500.50,
  "notes": "ملاحظات"
}
```

### DELETE /api/debts/{id}
- **الوصف**: حذف سجل محدد
- **المعاملات**: id (integer)

### GET /api/debts/customer/{name}/{city}
- **الوصف**: جلب سجلات عميل محدد
- **المعاملات**: name, city (strings)

### GET /api/debts/search?q={term}
- **الوصف**: البحث في السجلات
- **المعاملات**: q (search term)

### GET /api/statistics
- **الوصف**: جلب الإحصائيات
- **الاستجابة**:
```json
{
  "success": true,
  "data": {
    "total_amount": 15000.50,
    "total_customers": 5,
    "total_invoices": 10,
    "top_customers": [...],
    "last_updated": "2024-01-01T12:00:00"
  }
}
```

### GET /api/export
- **الوصف**: تصدير جميع البيانات
- **الاستجابة**: JSON file

### POST /api/import
- **الوصف**: استيراد البيانات
- **البيانات**: JSON array of debt records

## 🗄️ هيكل قاعدة البيانات

### ملف customers.json
```json
{
  "debts": [
    {
      "id": 1,
      "invoice_number": "INV-001",
      "customer_name": "أحمد محمد",
      "city": "الرياض",
      "amount": 1500.50,
      "notes": "ملاحظات",
      "date": "2024-01-01",
      "timestamp": "2024-01-01T12:00:00.000Z",
      "created_at": "2024-01-01T12:00:00.000Z"
    }
  ]
}
```

### حقول السجل
- **id**: معرف فريد (auto-increment)
- **invoice_number**: رقم الفاتورة (فريد)
- **customer_name**: اسم العميل
- **city**: المدينة
- **amount**: مبلغ الدين (float)
- **notes**: الملاحظات (اختياري)
- **date**: تاريخ الإدخال (YYYY-MM-DD)
- **timestamp**: طابع زمني ISO
- **created_at**: وقت الإنشاء

## 🔒 الأمان والتحقق

### التحقق من البيانات
```python
# في app.py
required_fields = ['invoiceNumber', 'customerName', 'city', 'amount']
for field in required_fields:
    if field not in data or not data[field]:
        return error_response(f'Missing required field: {field}')
```

### منع التكرار
```python
# في database.py
def invoice_exists(self, invoice_number: str) -> bool:
    for debt in self.data['debts']:
        if debt.get('invoice_number', '') == invoice_number:
            return True
    return False
```

### النسخ الاحتياطية
```python
# نسخة احتياطية تلقائية قبل كل حفظ
if os.path.exists(self.data_file):
    backup_file = f"{self.data_file}.bak"
    with open(self.data_file, 'r', encoding='utf-8') as src:
        with open(backup_file, 'w', encoding='utf-8') as dst:
            dst.write(src.read())
```

## 🧪 الاختبارات

### تشغيل الاختبارات
```bash
cd python
python test_system.py
```

### اختبارات متاحة
1. **اختبار الاتصال**: التحقق من عمل الخادم
2. **اختبار إضافة السجلات**: إضافة بيانات تجريبية
3. **اختبار استرجاع السجلات**: جلب البيانات
4. **اختبار سجل العميل**: جلب سجل عميل محدد
5. **اختبار البحث**: البحث في السجلات
6. **اختبار الإحصائيات**: جلب الإحصائيات

### إضافة اختبارات جديدة
```python
def test_new_feature(self):
    """Test new feature"""
    try:
        # اختبار الميزة الجديدة
        response = requests.get(f"{self.base_url}/api/new-endpoint")
        if response.status_code == 200:
            print("✅ الاختبار نجح")
            return True
        else:
            print("❌ الاختبار فشل")
            return False
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False
```

## 🚀 التطوير والتحسين

### إضافة ميزة جديدة

#### 1. إضافة endpoint جديد
```python
# في app.py
@app.route('/api/new-feature', methods=['GET'])
def new_feature():
    try:
        # منطق الميزة الجديدة
        result = process_new_feature()
        return jsonify({
            'success': True,
            'data': result
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
```

#### 2. إضافة وظيفة في قاعدة البيانات
```python
# في database.py
def new_database_function(self, parameter):
    """وصف الوظيفة الجديدة"""
    try:
        # منطق قاعدة البيانات
        result = self.process_data(parameter)
        self.save_data()
        return result
    except Exception as e:
        print(f"❌ خطأ في الوظيفة الجديدة: {e}")
        raise
```

#### 3. إضافة وظيفة في الواجهة الأمامية
```javascript
// في app.js
newFeature() {
    try {
        // منطق الواجهة الأمامية
        const result = this.processNewFeature();
        this.updateUI(result);
        this.showSuccess('تم تنفيذ الميزة الجديدة بنجاح');
    } catch (error) {
        this.showError('خطأ في تنفيذ الميزة الجديدة');
        console.error(error);
    }
}
```

### تحسينات مقترحة

#### قاعدة البيانات
- [ ] الترقية إلى SQLite
- [ ] إضافة فهرسة للبحث السريع
- [ ] تحسين أداء الاستعلامات

#### الواجهة
- [ ] إضافة وضع ليلي/نهاري
- [ ] تحسين تجربة المستخدم على الهاتف
- [ ] إضافة اختصارات لوحة المفاتيح

#### الميزات
- [ ] تقارير مفصلة
- [ ] تصدير إلى Excel/PDF
- [ ] نظام إشعارات
- [ ] نظام مستخدمين متعدد

#### الأمان
- [ ] تشفير البيانات
- [ ] نظام مصادقة
- [ ] سجل العمليات (audit log)

## 🐛 استكشاف الأخطاء للمطورين

### أخطاء شائعة

#### خطأ في استيراد الوحدات
```bash
ModuleNotFoundError: No module named 'flask'
```
**الحل**: `pip install -r requirements.txt`

#### خطأ في ترميز الملفات
```bash
UnicodeDecodeError: 'utf-8' codec can't decode
```
**الحل**: تأكد من حفظ الملفات بترميز UTF-8

#### خطأ في JSON
```bash
json.JSONDecodeError: Expecting ',' delimiter
```
**الحل**: تحقق من صحة تنسيق JSON في ملف البيانات

### أدوات التطوير

#### تشغيل في وضع التطوير
```bash
export FLASK_ENV=development
export FLASK_DEBUG=1
python app.py
```

#### مراقبة الملفات
```bash
# استخدام watchdog لإعادة التشغيل التلقائي
pip install watchdog
watchmedo auto-restart --patterns="*.py" --recursive python app.py
```

#### فحص الكود
```bash
# استخدام flake8 لفحص جودة الكود
pip install flake8
flake8 python/
```

## 📦 النشر والتوزيع

### إعداد الإنتاج
1. تعطيل وضع التطوير
2. استخدام خادم WSGI مثل Gunicorn
3. إعداد قاعدة بيانات أكثر قوة
4. إضافة HTTPS

### Docker (اختياري)
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY python/requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5000
CMD ["python", "python/app.py"]
```

---

**🔧 هذا الدليل يساعد المطورين على فهم وتطوير النظام بكفاءة**
