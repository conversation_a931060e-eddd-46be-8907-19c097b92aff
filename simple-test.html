<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مبسط</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .dropdown {
            position: relative;
            display: inline-block;
        }
        .dropdown-content {
            display: none;
            position: absolute;
            background: white;
            min-width: 200px;
            box-shadow: 0px 8px 16px rgba(0,0,0,0.2);
            z-index: 1;
            border-radius: 5px;
        }
        .dropdown-content.show {
            display: block;
        }
        .dropdown-content a {
            color: black;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
        }
        .dropdown-content a:hover {
            background: #f1f1f1;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🧪 اختبار مبسط للوظائف</h1>
    
    <div class="container">
        <h2>إضافة بيانات تجريبية</h2>
        <button onclick="addTestData()">إضافة بيانات تجريبية</button>
        <button onclick="clearData()">مسح البيانات</button>
    </div>
    
    <div class="container">
        <h2>اختبار القائمة المنسدلة</h2>
        <div class="dropdown">
            <button onclick="toggleDropdown('testDropdown')">قائمة الاختبار ⋮</button>
            <div class="dropdown-content" id="testDropdown">
                <a href="#" onclick="testPDF()">📄 اختبار PDF</a>
                <a href="#" onclick="testExcel()">📊 اختبار Excel</a>
                <a href="#" onclick="testImport()">📥 اختبار الاستيراد</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <h2>اختبار مباشر</h2>
        <button onclick="directPDFTest()">PDF مباشر</button>
        <button onclick="directExcelTest()">Excel مباشر</button>
        <button onclick="checkEverything()">فحص شامل</button>
    </div>
    
    <div class="container">
        <h2>سجل الأحداث</h2>
        <div id="log" class="log"></div>
    </div>

    <!-- نفس المكتبات والملفات -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdn.sheetjs.com/xlsx-0.20.1/package/dist/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        // محاكاة debtManager
        window.debtManager = {
            debts: [],
            previousDebts: [],
            showSuccess: function(message) {
                log('✅ ' + message);
                alert('نجح: ' + message);
            },
            showError: function(message) {
                log('❌ ' + message);
                alert('خطأ: ' + message);
            }
        };

        // وظائف القائمة المنسدلة
        function toggleDropdown(dropdownId) {
            log('🔄 محاولة فتح القائمة: ' + dropdownId);
            const dropdown = document.getElementById(dropdownId);
            if (dropdown) {
                dropdown.classList.toggle('show');
                log('✅ تم تبديل حالة القائمة');
            } else {
                log('❌ لم يتم العثور على القائمة');
            }
        }

        function closeAllDropdowns() {
            log('🔄 إغلاق جميع القوائم');
            document.querySelectorAll('.dropdown-content').forEach(dropdown => {
                dropdown.classList.remove('show');
            });
        }

        // إغلاق القوائم عند النقر خارجها
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.dropdown')) {
                closeAllDropdowns();
            }
        });

        // إضافة بيانات تجريبية
        function addTestData() {
            window.debtManager.debts = [
                {
                    id: 1,
                    invoiceNumber: 'INV-001',
                    customerName: 'أحمد محمد',
                    city: 'الرياض',
                    amount: 1500,
                    notes: 'ملاحظة تجريبية',
                    date: '15/12/2024'
                },
                {
                    id: 2,
                    invoiceNumber: 'INV-002',
                    customerName: 'فاطمة علي',
                    city: 'جدة',
                    amount: 2000,
                    notes: 'ملاحظة أخرى',
                    date: '16/12/2024'
                }
            ];
            
            window.debtManager.previousDebts = [
                {
                    id: 3,
                    invoiceNumber: 'OLD-001',
                    customerName: 'محمد أحمد',
                    city: 'الدمام',
                    amount: 800,
                    notes: 'دين سابق',
                    date: '10/12/2024'
                }
            ];
            
            log('✅ تم إضافة البيانات التجريبية');
            log(`📊 الفواتير الجديدة: ${window.debtManager.debts.length}`);
            log(`📊 الديون السابقة: ${window.debtManager.previousDebts.length}`);
        }

        function clearData() {
            window.debtManager.debts = [];
            window.debtManager.previousDebts = [];
            log('🗑️ تم مسح جميع البيانات');
        }

        // اختبار PDF مباشر
        function directPDFTest() {
            log('🔄 اختبار PDF مباشر...');
            
            try {
                if (!window.jspdf) {
                    throw new Error('مكتبة jsPDF غير متوفرة');
                }

                const { jsPDF } = window.jspdf;
                const doc = new jsPDF();
                
                doc.setFontSize(16);
                doc.text('Test PDF Document', 20, 20);
                doc.text('Arabic Test: اختبار النص العربي', 20, 40);
                
                const data = window.debtManager.debts;
                if (data.length > 0) {
                    let yPos = 60;
                    data.forEach((item, index) => {
                        doc.setFontSize(10);
                        doc.text(`${index + 1}. ${item.invoiceNumber} - ${item.customerName} - ${item.amount} SAR`, 20, yPos);
                        yPos += 10;
                    });
                }
                
                doc.save('direct-test.pdf');
                log('✅ تم إنشاء PDF مباشر بنجاح!');
                
            } catch (error) {
                log('❌ خطأ في PDF: ' + error.message);
            }
        }

        // اختبار Excel مباشر
        function directExcelTest() {
            log('🔄 اختبار Excel مباشر...');
            
            try {
                if (!window.XLSX) {
                    throw new Error('مكتبة XLSX غير متوفرة');
                }

                const data = window.debtManager.debts.map(item => ({
                    'رقم الفاتورة': item.invoiceNumber,
                    'اسم العميل': item.customerName,
                    'المدينة': item.city,
                    'المبلغ': item.amount,
                    'الملاحظات': item.notes
                }));

                const wb = XLSX.utils.book_new();
                const ws = XLSX.utils.json_to_sheet(data);
                XLSX.utils.book_append_sheet(wb, ws, 'اختبار');
                
                XLSX.writeFile(wb, 'direct-test.xlsx');
                log('✅ تم إنشاء Excel مباشر بنجاح!');
                
            } catch (error) {
                log('❌ خطأ في Excel: ' + error.message);
            }
        }

        // اختبار من القائمة
        function testPDF() {
            closeAllDropdowns();
            log('🔄 اختبار PDF من القائمة...');
            
            if (typeof printPDF === 'function') {
                log('✅ وظيفة printPDF موجودة');
                try {
                    printPDF('new');
                } catch (error) {
                    log('❌ خطأ في استدعاء printPDF: ' + error.message);
                }
            } else {
                log('❌ وظيفة printPDF غير موجودة');
                directPDFTest();
            }
        }

        function testExcel() {
            closeAllDropdowns();
            log('🔄 اختبار Excel من القائمة...');
            
            if (typeof exportToExcel === 'function') {
                log('✅ وظيفة exportToExcel موجودة');
                try {
                    exportToExcel('new');
                } catch (error) {
                    log('❌ خطأ في استدعاء exportToExcel: ' + error.message);
                }
            } else {
                log('❌ وظيفة exportToExcel غير موجودة');
                directExcelTest();
            }
        }

        function testImport() {
            closeAllDropdowns();
            log('🔄 اختبار الاستيراد من القائمة...');
            
            if (typeof importFromExcel === 'function') {
                log('✅ وظيفة importFromExcel موجودة');
                log('💡 لاختبار الاستيراد، اختر ملف Excel');
            } else {
                log('❌ وظيفة importFromExcel غير موجودة');
            }
        }

        function checkEverything() {
            log('🔍 فحص شامل...');
            
            // فحص المكتبات
            log('📚 فحص المكتبات:');
            log('jsPDF: ' + (typeof window.jspdf !== 'undefined' ? '✅' : '❌'));
            log('XLSX: ' + (typeof window.XLSX !== 'undefined' ? '✅' : '❌'));
            log('html2canvas: ' + (typeof window.html2canvas !== 'undefined' ? '✅' : '❌'));
            
            // فحص الوظائف
            log('🔧 فحص الوظائف:');
            log('printPDF: ' + (typeof printPDF !== 'undefined' ? '✅' : '❌'));
            log('exportToExcel: ' + (typeof exportToExcel !== 'undefined' ? '✅' : '❌'));
            log('importFromExcel: ' + (typeof importFromExcel !== 'undefined' ? '✅' : '❌'));
            log('toggleDropdown: ' + (typeof toggleDropdown !== 'undefined' ? '✅' : '❌'));
            log('closeAllDropdowns: ' + (typeof closeAllDropdowns !== 'undefined' ? '✅' : '❌'));
            
            // فحص البيانات
            log('📊 فحص البيانات:');
            log('debtManager: ' + (typeof window.debtManager !== 'undefined' ? '✅' : '❌'));
            log('debts: ' + (window.debtManager && window.debtManager.debts ? window.debtManager.debts.length + ' عنصر' : '❌'));
            log('previousDebts: ' + (window.debtManager && window.debtManager.previousDebts ? window.debtManager.previousDebts.length + ' عنصر' : '❌'));
        }

        // تحميل الملفات الأصلية
        window.addEventListener('load', function() {
            log('🎯 بدء تحميل الملفات...');
            
            // إضافة البيانات التجريبية
            addTestData();
            
            // محاولة تحميل الملفات الأصلية
            const scripts = [
                '../javascript/arabic-font.js',
                '../javascript/app.js',
                '../javascript/export-import.js'
            ];
            
            let loadedCount = 0;
            
            scripts.forEach((src, index) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = function() {
                    loadedCount++;
                    log(`✅ تم تحميل: ${src}`);
                    
                    if (loadedCount === scripts.length) {
                        log('🎉 تم تحميل جميع الملفات!');
                        setTimeout(checkEverything, 1000);
                    }
                };
                script.onerror = function() {
                    log(`❌ فشل تحميل: ${src}`);
                };
                document.head.appendChild(script);
            });
        });
    </script>
</body>
</html>
