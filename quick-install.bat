@echo off
chcp 65001 >nul
title التثبيت السريع - نظام إدارة ديون العملاء

echo.
echo ⚡ التثبيت السريع لنظام إدارة ديون العملاء
echo ================================================
echo.

:: التحقق من Node.js
echo 🔍 فحص Node.js...
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo.
    echo 📥 يرجى تثبيت Node.js أولاً من: https://nodejs.org/
    echo بعد التثبيت، أعد تشغيل هذا الملف
    pause
    start https://nodejs.org/
    exit /b 1
)

echo ✅ Node.js مثبت
node --version
echo.

:: التحقق من ملفات المشروع
if not exist "package.json" (
    echo ❌ ملفات المشروع غير موجودة
    echo تأكد من تشغيل هذا الملف في مجلد المشروع
    pause
    exit /b 1
)

echo ✅ ملفات المشروع موجودة
echo.

:: تثبيت المتطلبات
echo 📦 تثبيت المتطلبات...
echo هذا قد يستغرق بضع دقائق...
echo.

npm install
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت المتطلبات
    echo.
    echo 🔧 محاولة حل المشكلة...
    if exist "node_modules" rmdir /s /q node_modules
    if exist "package-lock.json" del package-lock.json
    echo.
    echo إعادة المحاولة...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل مرة أخرى
        echo يرجى التحقق من اتصال الإنترنت وإعادة المحاولة
        pause
        exit /b 1
    )
)

echo ✅ تم تثبيت المتطلبات بنجاح
echo.

:: إنشاء الأيقونات
echo 🎨 إنشاء الأيقونات...
if not exist "build" mkdir build

:: فتح صفحة الأيقونات
if exist "build\create-icons.html" (
    echo فتح صفحة إنشاء الأيقونات...
    start build\create-icons.html
    echo.
    echo 📋 يرجى:
    echo 1. تحميل الأيقونات من الصفحة المفتوحة
    echo 2. وضعها في مجلد build
    echo 3. الضغط على أي زر هنا للمتابعة
    echo.
    pause
)

:: التحقق من وجود الأيقونات
if not exist "build\icon.png" (
    echo ⚠️ لم يتم العثور على icon.png
    echo سيتم إنشاء أيقونة افتراضية...
    echo.
)

:: بناء التطبيق
echo 🏗️ بناء تطبيق سطح المكتب...
echo هذا قد يستغرق 5-10 دقائق...
echo.

npm run build-win
if %errorlevel% equ 0 (
    echo.
    echo 🎉 تم بناء التطبيق بنجاح!
    echo.
    if exist "dist" (
        echo 📁 الملفات المُنتجة:
        dir /b dist\*.exe 2>nul
        echo.
        echo 📍 مكان الملفات: %CD%\dist\
        echo.
        echo 🚀 يمكنك الآن:
        echo 1. تشغيل Setup.exe لتثبيت التطبيق
        echo 2. أو تشغيل النسخة المحمولة مباشرة
        echo.
        set /p open="هل تريد فتح مجلد الملفات؟ (y/n): "
        if /i "!open!"=="y" explorer dist
        echo.
        set /p install="هل تريد تشغيل المثبت الآن؟ (y/n): "
        if /i "!install!"=="y" (
            for %%f in (dist\*Setup*.exe) do (
                echo تشغيل المثبت: %%f
                start "" "%%f"
                goto installed
            )
        )
        :installed
    )
) else (
    echo ❌ فشل في بناء التطبيق
    echo.
    echo 🔧 نصائح لحل المشكلة:
    echo 1. تأكد من اتصال الإنترنت
    echo 2. أعد تشغيل الكمبيوتر وحاول مرة أخرى
    echo 3. تأكد من وجود مساحة كافية على القرص الصلب
    echo 4. جرب تشغيل install-windows.bat للخيارات المتقدمة
)

echo.
echo 🎯 انتهت عملية التثبيت السريع
echo.
pause
