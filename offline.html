<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>غير متصل - نظام إدارة ديون العملاء</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
            color: #333;
        }

        .offline-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 90%;
        }

        .offline-icon {
            font-size: 80px;
            color: #e74c3c;
            margin-bottom: 20px;
        }

        .offline-title {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .offline-message {
            font-size: 16px;
            color: #7f8c8d;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .offline-features {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: right;
        }

        .offline-features h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .offline-features ul {
            list-style: none;
            padding: 0;
        }

        .offline-features li {
            padding: 8px 0;
            color: #34495e;
            border-bottom: 1px solid #ecf0f1;
        }

        .offline-features li:last-child {
            border-bottom: none;
        }

        .offline-features li::before {
            content: "✅";
            margin-left: 10px;
        }

        .retry-btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .retry-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .home-btn {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }

        .home-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }

        .status-offline {
            background: #e74c3c;
            animation: pulse 2s infinite;
        }

        .status-online {
            background: #27ae60;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .connection-status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            font-weight: bold;
        }

        .offline-status {
            background: #ffeaa7;
            color: #d63031;
            border: 2px solid #fdcb6e;
        }

        .online-status {
            background: #d1f2eb;
            color: #00b894;
            border: 2px solid #00cec9;
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">📡</div>
        <h1 class="offline-title">غير متصل بالإنترنت</h1>
        <p class="offline-message">
            لا تقلق! يمكنك الاستمرار في استخدام نظام إدارة الديون حتى بدون إنترنت.
            جميع بياناتك محفوظة محلي<|im_start|> على جهازك.
        </p>

        <div class="offline-features">
            <h3>الميزات المتاحة بدون إنترنت:</h3>
            <ul>
                <li>إضافة وتعديل وحذف الديون</li>
                <li>عرض الإحصائيات والتحليلات</li>
                <li>البحث والفرز</li>
                <li>تصدير البيانات (CSV/Excel)</li>
                <li>حفظ البيانات محلي<|im_start|></li>
            </ul>
        </div>

        <div class="connection-status" id="connectionStatus">
            <span class="status-indicator status-offline" id="statusIndicator"></span>
            <span id="statusText">غير متصل</span>
        </div>

        <div style="margin-top: 30px;">
            <button class="retry-btn" onclick="checkConnection()">
                🔄 فحص الاتصال
            </button>
            <a href="./index.html" class="home-btn">
                🏠 العودة للتطبيق
            </a>
        </div>
    </div>

    <script>
        // فحص حالة الاتصال
        function updateConnectionStatus() {
            const statusIndicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('statusText');
            const connectionStatus = document.getElementById('connectionStatus');

            if (navigator.onLine) {
                statusIndicator.className = 'status-indicator status-online';
                statusText.textContent = 'متصل';
                connectionStatus.className = 'connection-status online-status';
            } else {
                statusIndicator.className = 'status-indicator status-offline';
                statusText.textContent = 'غير متصل';
                connectionStatus.className = 'connection-status offline-status';
            }
        }

        // فحص الاتصال يدوي<|im_start|>
        function checkConnection() {
            updateConnectionStatus();
            
            if (navigator.onLine) {
                // إذا كان متصل، حاول الذهاب للصفحة الرئيسية
                window.location.href = './index.html';
            } else {
                // إظهار رسالة
                alert('لا يزال الجهاز غير متصل بالإنترنت. يمكنك الاستمرار في استخدام التطبيق بدون إنترنت.');
            }
        }

        // مراقبة تغيير حالة الاتصال
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);

        // فحص أولي
        updateConnectionStatus();

        // فحص دوري كل 30 ثانية
        setInterval(updateConnectionStatus, 30000);
    </script>
</body>
</html>
