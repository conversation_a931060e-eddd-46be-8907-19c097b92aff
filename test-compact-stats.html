<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإحصائيات المدمجة</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 50px rgba(0,0,0,0.4);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 42px;
            background: linear-gradient(135deg, #28a745, #20c997);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .compact-banner {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            border: 4px solid #28a745;
            border-radius: 20px;
            padding: 40px;
            margin: 30px 0;
            box-shadow: 0 10px 30px rgba(40, 167, 69, 0.4);
            text-align: center;
        }
        .improvement-list {
            list-style: none;
            padding: 0;
        }
        .improvement-list li {
            background: #e8f5e8;
            margin: 15px 0;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #28a745;
        }
        .improvement-list li::before {
            content: "✅ ";
            font-weight: bold;
            color: #28a745;
            font-size: 18px;
        }
        .comparison-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 30px 0;
        }
        .before-after {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            border: 2px solid #dee2e6;
        }
        .before {
            border-color: #dc3545;
        }
        .after {
            border-color: #28a745;
        }
        .before h3 {
            color: #dc3545;
        }
        .after h3 {
            color: #28a745;
        }
        .stats-demo {
            background: #f8fafc;
            border-radius: 8px;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #e2e8f0;
        }
        .customer-item-demo {
            background: white;
            border-radius: 6px;
            padding: 8px;
            margin: 6px 0;
            display: flex;
            align-items: center;
            gap: 10px;
            border: 1px solid #e2e8f0;
            font-size: 14px;
        }
        .rank-demo {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 12px;
        }
        .customer-info-demo {
            flex: 1;
        }
        .customer-name-demo {
            font-weight: 600;
            color: #1e293b;
            font-size: 14px;
        }
        .customer-city-demo {
            font-size: 12px;
            color: #64748b;
        }
        .invoice-list-demo {
            font-size: 10px;
            color: #6c757d;
            font-style: italic;
            margin-top: 2px;
        }
        .amount-demo {
            font-weight: 600;
            color: #059669;
            font-size: 14px;
        }
        .debt-item-demo {
            background: white;
            border-radius: 4px;
            padding: 6px;
            margin: 4px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid #e2e8f0;
            font-size: 12px;
        }
        .debt-info-demo {
            display: flex;
            flex-direction: column;
        }
        .debt-customer-demo {
            font-weight: 500;
            color: #1e293b;
            font-size: 12px;
        }
        .debt-invoice-demo {
            font-size: 9px;
            color: #6c757d;
            font-style: italic;
            margin-top: 1px;
        }
        .debt-amount-demo {
            font-weight: 600;
            color: #059669;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 الإحصائيات المدمجة</h1>
        
        <div class="compact-banner">
            <h2 style="color: #155724; font-size: 32px; margin-bottom: 20px;">✅ تم تحسين الإحصائيات المتقدمة</h2>
            <p style="font-size: 18px; color: #155724;">تصميم مدمج لعرض بيانات أكثر في مساحة أقل</p>
        </div>

        <ul class="improvement-list">
            <li><strong>إزالة المربع الأزرق:</strong> لا مزيد من الرسالة التوضيحية المزعجة</li>
            <li><strong>تصغير المساحات:</strong> تقليل padding وmargin في جميع العناصر</li>
            <li><strong>تصغير الخطوط:</strong> أحجام خطوط أصغر للعناوين والنصوص</li>
            <li><strong>زيادة البيانات:</strong> عرض 8 عملاء بدلاً من 5</li>
            <li><strong>زيادة الديون:</strong> عرض 5 ديون بدلاً من 3 في كل قسم</li>
            <li><strong>تحسين التخطيط:</strong> استغلال أفضل للمساحة المتاحة</li>
        </ul>

        <div class="comparison-section">
            <div class="before-after before">
                <h3>❌ قبل التحسين</h3>
                <div style="background: #f8fafc; padding: 15px; border-radius: 12px; margin: 10px 0;">
                    <div style="background: linear-gradient(135deg, #d1ecf1, #bee5eb); padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: center; font-size: 14px;">
                        الإحصائيات المتقدمة تعتمد على الديون الجديدة فقط
                    </div>
                    <h5 style="margin: 0 0 15px 0; font-size: 20px;">أكبر العملاء</h5>
                    <div style="background: white; padding: 12px; margin: 10px 0; border-radius: 8px; font-size: 16px;">
                        <span style="background: #667eea; color: white; width: 30px; height: 30px; border-radius: 50%; display: inline-flex; align-items: center; justify-content: center; margin-left: 15px;">1</span>
                        أحمد محمد العلي - الرياض
                        <div style="font-size: 14px; color: #6c757d; margin-top: 5px;">الفواتير: 001, 002</div>
                        <div style="color: #059669; font-weight: bold; float: left;">3,700.25 ر.س</div>
                    </div>
                    <p style="color: #666; font-size: 12px;">عرض 5 عملاء فقط</p>
                </div>
            </div>

            <div class="before-after after">
                <h3>✅ بعد التحسين</h3>
                <div class="stats-demo">
                    <h5 style="margin: 0 0 8px 0; font-size: 17px;">أكبر العملاء</h5>
                    <div class="customer-item-demo">
                        <div class="rank-demo">1</div>
                        <div class="customer-info-demo">
                            <div class="customer-name-demo">أحمد محمد العلي</div>
                            <div class="customer-city-demo">الرياض</div>
                            <div class="invoice-list-demo">الفواتير: 001, 002</div>
                        </div>
                        <div class="amount-demo">3,700.25 ر.س</div>
                    </div>
                    <div class="customer-item-demo">
                        <div class="rank-demo">2</div>
                        <div class="customer-info-demo">
                            <div class="customer-name-demo">فاطمة الزهراني</div>
                            <div class="customer-city-demo">جدة</div>
                            <div class="invoice-list-demo">الفواتير: 003</div>
                        </div>
                        <div class="amount-demo">3,750.25 ر.س</div>
                    </div>
                    <p style="color: #666; font-size: 10px; margin: 5px 0 0 0;">عرض 8 عملاء</p>
                </div>
            </div>
        </div>

        <div class="comparison-section">
            <div class="before-after before">
                <h3>❌ أكبر الديون (قبل)</h3>
                <div style="background: #f8fafc; padding: 15px; border-radius: 12px;">
                    <h6 style="margin: 0 0 15px 0; font-size: 14px;">أكبر الديون</h6>
                    <div style="background: white; padding: 10px; margin: 8px 0; border-radius: 6px; display: flex; justify-content: space-between; font-size: 14px;">
                        <span>فاطمة الزهراني</span>
                        <span style="color: #059669; font-weight: bold;">3,750.25 ر.س</span>
                    </div>
                    <p style="color: #666; font-size: 12px;">عرض 3 ديون فقط</p>
                </div>
            </div>

            <div class="before-after after">
                <h3>✅ أكبر الديون (بعد)</h3>
                <div class="stats-demo">
                    <h6 style="margin: 0 0 6px 0; font-size: 13px;">أكبر الديون</h6>
                    <div class="debt-item-demo">
                        <div class="debt-info-demo">
                            <div class="debt-customer-demo">فاطمة الزهراني</div>
                            <div class="debt-invoice-demo">فاتورة: 003</div>
                        </div>
                        <div class="debt-amount-demo">3,750.25 ر.س</div>
                    </div>
                    <div class="debt-item-demo">
                        <div class="debt-info-demo">
                            <div class="debt-customer-demo">أحمد العلي</div>
                            <div class="debt-invoice-demo">فاتورة: 002</div>
                        </div>
                        <div class="debt-amount-demo">2,200.50 ر.س</div>
                    </div>
                    <p style="color: #666; font-size: 10px; margin: 5px 0 0 0;">عرض 5 ديون</p>
                </div>
            </div>
        </div>

        <div style="background: #f8f9fa; padding: 20px; border-radius: 15px; margin: 30px 0;">
            <h3>📏 مقارنة الأحجام:</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
                <div>
                    <h4 style="color: #dc3545;">قبل التحسين:</h4>
                    <ul style="font-size: 14px; color: #666;">
                        <li>عناوين: 20px</li>
                        <li>أسماء العملاء: 17px</li>
                        <li>أرقام الفواتير: 12px</li>
                        <li>المساحة الداخلية: 15px</li>
                        <li>المسافات: 20px</li>
                        <li>عدد العملاء: 5</li>
                        <li>عدد الديون: 3</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color: #28a745;">بعد التحسين:</h4>
                    <ul style="font-size: 14px; color: #666;">
                        <li>عناوين: 17px</li>
                        <li>أسماء العملاء: 14px</li>
                        <li>أرقام الفواتير: 10px</li>
                        <li>المساحة الداخلية: 8px</li>
                        <li>المسافات: 12px</li>
                        <li>عدد العملاء: 8</li>
                        <li>عدد الديون: 5</li>
                    </ul>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; padding: 20px; background: #d4edda; border-radius: 15px;">
            <h3 style="color: #155724;">🎉 تم تحسين الإحصائيات المتقدمة!</h3>
            <p style="color: #155724; font-size: 18px;">الآن تعرض بيانات أكثر في مساحة أقل مع تصميم مدمج ونظيف</p>
            <div style="margin-top: 20px;">
                <span style="background: #28a745; color: white; padding: 10px 20px; border-radius: 25px; font-weight: bold;">
                    +60% بيانات أكثر في نفس المساحة
                </span>
            </div>
        </div>
    </div>
</body>
</html>
