<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>مسح البيانات</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; text-align: center; }
        .btn { background: #dc3545; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; margin: 10px; font-size: 16px; }
        .btn:hover { background: #c82333; }
        .success { background: #28a745; }
        .success:hover { background: #218838; }
    </style>
</head>
<body>
    <h1>🗑️ مسح البيانات</h1>
    
    <div>
        <button class="btn" onclick="clearNewInvoices()">مسح الفواتير الجديدة</button>
        <button class="btn" onclick="clearPreviousDebts()">مسح الديون السابقة</button>
        <button class="btn" onclick="clearAllData()">مسح جميع البيانات</button>
    </div>
    
    <br><br>
    
    <div>
        <button class="btn success" onclick="window.location.href='/'">العودة للموقع الرئيسي</button>
    </div>

    <script>
        function clearNewInvoices() {
            if (confirm('هل أنت متأكد من مسح جميع الفواتير الجديدة؟')) {
                localStorage.removeItem('customerDebts');
                alert('✅ تم مسح الفواتير الجديدة');
            }
        }

        function clearPreviousDebts() {
            if (confirm('هل أنت متأكد من مسح جميع الديون السابقة؟')) {
                localStorage.removeItem('previousDebts');
                alert('✅ تم مسح الديون السابقة');
            }
        }

        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟\n\nهذا الإجراء لا يمكن التراجع عنه!')) {
                localStorage.removeItem('customerDebts');
                localStorage.removeItem('previousDebts');
                alert('✅ تم مسح جميع البيانات');
            }
        }
    </script>
</body>
</html>
