// 🔥 الحل الجذري النهائي لمشكلة الترميز العربي في PDF
// استخدام HTML2Canvas + jsPDF بطريقة مختلفة تماماً

console.log('🔥 تحميل الحل الجذري النهائي للترميز العربي...');

// وظيفة إنشاء PDF من HTML مع دعم كامل للعربية
function createArabicPDFFromHTML(data, title, type = 'report') {
    console.log('📄 إنشاء PDF من HTML مع دعم كامل للعربية...');
    
    return new Promise((resolve, reject) => {
        try {
            // إنشاء HTML مع النصوص الأصلية بدون تعديل
            const htmlContent = createArabicHTMLTable(data, title);
            
            // إنشاء عنصر مؤقت
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = htmlContent;
            tempDiv.style.position = 'absolute';
            tempDiv.style.left = '-9999px';
            tempDiv.style.top = '0';
            tempDiv.style.width = '800px';
            tempDiv.style.backgroundColor = '#ffffff';
            tempDiv.style.color = '#000000';
            tempDiv.style.fontFamily = 'Arial, "Segoe UI", Tahoma, sans-serif';
            tempDiv.style.fontSize = '14px';
            tempDiv.style.lineHeight = '1.5';
            tempDiv.style.direction = 'rtl';
            tempDiv.style.textAlign = 'right';
            tempDiv.style.padding = '20px';
            
            document.body.appendChild(tempDiv);
            
            // انتظار قصير للتأكد من الرندر
            setTimeout(() => {
                // استخدام html2canvas مع إعدادات محسنة للعربية
                html2canvas(tempDiv, {
                    scale: 2,
                    useCORS: true,
                    allowTaint: false,
                    backgroundColor: '#ffffff',
                    width: 800,
                    height: tempDiv.scrollHeight,
                    scrollX: 0,
                    scrollY: 0,
                    windowWidth: 800,
                    windowHeight: tempDiv.scrollHeight,
                    removeContainer: false,
                    imageTimeout: 30000,
                    logging: false,
                    foreignObjectRendering: false,
                    letterRendering: true,
                    dpi: 300,
                    quality: 1.0,
                    onclone: function(clonedDoc) {
                        // إضافة CSS محسن للعربية
                        const style = clonedDoc.createElement('style');
                        style.textContent = `
                            * {
                                font-family: Arial, "Segoe UI", Tahoma, sans-serif !important;
                                direction: rtl !important;
                                text-align: right !important;
                                background: #ffffff !important;
                                color: #000000 !important;
                                -webkit-font-smoothing: antialiased;
                                -moz-osx-font-smoothing: grayscale;
                                text-rendering: optimizeLegibility;
                            }
                            body {
                                background: #ffffff !important;
                                color: #000000 !important;
                                margin: 0 !important;
                                padding: 20px !important;
                            }
                            table {
                                border-collapse: collapse !important;
                                width: 100% !important;
                                background: #ffffff !important;
                                color: #000000 !important;
                            }
                            th, td {
                                border: 2px solid #333333 !important;
                                padding: 12px !important;
                                text-align: center !important;
                                background: #ffffff !important;
                                color: #000000 !important;
                                font-size: 14px !important;
                                font-weight: normal !important;
                            }
                            th {
                                background: #2c3e50 !important;
                                color: #ffffff !important;
                                font-weight: bold !important;
                                font-size: 16px !important;
                            }
                            h1 {
                                color: #2c3e50 !important;
                                text-align: center !important;
                                font-size: 24px !important;
                                font-weight: bold !important;
                                margin-bottom: 20px !important;
                                background: transparent !important;
                            }
                            .date {
                                color: #666666 !important;
                                text-align: center !important;
                                font-size: 14px !important;
                                margin-bottom: 20px !important;
                            }
                            .total {
                                background: #2c3e50 !important;
                                color: #ffffff !important;
                                font-weight: bold !important;
                                font-size: 16px !important;
                                text-align: center !important;
                                padding: 15px !important;
                            }
                        `;
                        clonedDoc.head.appendChild(style);
                        
                        // التأكد من الخلفية البيضاء
                        clonedDoc.body.style.backgroundColor = '#ffffff';
                        clonedDoc.body.style.color = '#000000';
                    }
                }).then(canvas => {
                    // إزالة العنصر المؤقت
                    document.body.removeChild(tempDiv);
                    
                    try {
                        const { jsPDF } = window.jspdf;
                        
                        // حساب أبعاد PDF
                        const imgWidth = 190; // عرض الصورة في PDF
                        const pageHeight = 277; // ارتفاع صفحة A4
                        const imgHeight = (canvas.height * imgWidth) / canvas.width;
                        const heightLeft = imgHeight;
                        
                        // إنشاء PDF جديد
                        const pdf = new jsPDF('p', 'mm', 'a4');
                        let position = 10;
                        
                        // تحويل Canvas إلى صورة
                        const imgData = canvas.toDataURL('image/png', 1.0);
                        
                        // إضافة الصفحة الأولى
                        pdf.addImage(imgData, 'PNG', 10, position, imgWidth, imgHeight);
                        let remainingHeight = heightLeft - pageHeight;
                        
                        // إضافة صفحات إضافية إذا لزم الأمر
                        while (remainingHeight >= 0) {
                            position = remainingHeight - imgHeight + 10;
                            pdf.addPage();
                            pdf.addImage(imgData, 'PNG', 10, position, imgWidth, imgHeight);
                            remainingHeight -= pageHeight;
                        }
                        
                        // حفظ الملف
                        const fileName = `${title.replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')}_Arabic_${new Date().toISOString().split('T')[0]}.pdf`;
                        pdf.save(fileName);
                        
                        console.log(`✅ تم إنشاء PDF عربي: ${fileName}`);
                        
                        resolve({
                            success: true,
                            fileName: fileName,
                            method: 'HTML2Canvas',
                            records: data.length
                        });
                        
                    } catch (pdfError) {
                        console.error('❌ خطأ في إنشاء PDF:', pdfError);
                        reject({
                            success: false,
                            error: `خطأ في إنشاء PDF: ${pdfError.message}`
                        });
                    }
                    
                }).catch(canvasError => {
                    // إزالة العنصر المؤقت في حالة الخطأ
                    if (document.body.contains(tempDiv)) {
                        document.body.removeChild(tempDiv);
                    }
                    
                    console.error('❌ خطأ في html2canvas:', canvasError);
                    reject({
                        success: false,
                        error: `خطأ في تحويل HTML: ${canvasError.message}`
                    });
                });
                
            }, 1000); // انتظار ثانية واحدة للرندر
            
        } catch (error) {
            console.error('❌ خطأ عام:', error);
            reject({
                success: false,
                error: `خطأ عام: ${error.message}`
            });
        }
    });
}

// وظيفة إنشاء HTML مع النصوص الأصلية
function createArabicHTMLTable(data, title) {
    const currentDate = new Date().toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
    });
    
    let totalAmount = 0;
    data.forEach(item => {
        totalAmount += parseFloat(item.amount) || 0;
    });
    
    let html = `
        <div style="direction: rtl; text-align: right; font-family: Arial, 'Segoe UI', Tahoma, sans-serif; background: #ffffff; color: #000000; padding: 20px;">
            <h1 style="color: #2c3e50; text-align: center; font-size: 28px; margin-bottom: 10px; font-weight: bold;">${title}</h1>
            <div class="date" style="color: #666666; text-align: center; font-size: 16px; margin-bottom: 30px;">${currentDate}</div>
            
            <table style="width: 100%; border-collapse: collapse; margin: 20px 0; background: #ffffff; color: #000000;">
                <thead>
                    <tr style="background: #2c3e50; color: #ffffff;">
                        <th style="border: 2px solid #333; padding: 15px; text-align: center; font-size: 18px; font-weight: bold;">رقم الفاتورة</th>
                        <th style="border: 2px solid #333; padding: 15px; text-align: center; font-size: 18px; font-weight: bold;">اسم العميل</th>
                        <th style="border: 2px solid #333; padding: 15px; text-align: center; font-size: 18px; font-weight: bold;">المدينة</th>
                        <th style="border: 2px solid #333; padding: 15px; text-align: center; font-size: 18px; font-weight: bold;">المبلغ (ر.س)</th>
                        <th style="border: 2px solid #333; padding: 15px; text-align: center; font-size: 18px; font-weight: bold;">الملاحظات</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    data.forEach((item, index) => {
        const rowColor = index % 2 === 0 ? '#ffffff' : '#f8f9fa';
        const amount = parseFloat(item.amount) || 0;
        
        html += `
            <tr style="background: ${rowColor};">
                <td style="border: 2px solid #ddd; padding: 12px; text-align: center; font-size: 16px; color: #000000;">${item.invoiceNumber || ''}</td>
                <td style="border: 2px solid #ddd; padding: 12px; text-align: center; font-size: 16px; color: #000000;">${item.customerName || ''}</td>
                <td style="border: 2px solid #ddd; padding: 12px; text-align: center; font-size: 16px; color: #000000;">${item.city || ''}</td>
                <td style="border: 2px solid #ddd; padding: 12px; text-align: center; font-size: 16px; color: #000000;">${amount.toLocaleString('ar-SA')}</td>
                <td style="border: 2px solid #ddd; padding: 12px; text-align: center; font-size: 16px; color: #000000;">${item.notes || ''}</td>
            </tr>
        `;
    });
    
    html += `
                </tbody>
            </table>
            
            <div class="total" style="background: #2c3e50; color: #ffffff; padding: 20px; text-align: center; font-size: 20px; font-weight: bold; margin-top: 20px; border-radius: 5px;">
                إجمالي المبلغ: ${totalAmount.toLocaleString('ar-SA')} ر.س
            </div>
        </div>
    `;
    
    return html;
}

// وظيفة اختبار سريع
async function testArabicPDFSolution() {
    console.log('🧪 اختبار الحل الجذري للترميز العربي...');
    
    const testData = [
        {
            invoiceNumber: '001',
            customerName: 'أحمد محمد العلي',
            city: 'الرياض',
            amount: 1500.75,
            notes: 'ملاحظة تجريبية عربية'
        },
        {
            invoiceNumber: '002',
            customerName: 'فاطمة عبدالله',
            city: 'جدة',
            amount: 2500.50,
            notes: 'معلومات إضافية مهمة'
        },
        {
            invoiceNumber: '003',
            customerName: 'محمد سالم',
            city: 'الدمام',
            amount: 3750.25,
            notes: 'تفاصيل شاملة للفاتورة'
        }
    ];
    
    try {
        const result = await createArabicPDFFromHTML(testData, 'تقرير اختبار الترميز العربي النهائي', 'test');
        
        if (result.success) {
            alert(`✅ نجح الاختبار بالحل الجذري!\n\nالملف: ${result.fileName}\nالطريقة: ${result.method}\nالسجلات: ${result.records}\n\n🔍 افتح الملف وتحقق من أن النصوص العربية تظهر بوضوح تام`);
            return result;
        } else {
            alert(`❌ فشل الاختبار: ${result.error}`);
            return result;
        }
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error);
        alert(`❌ خطأ في الاختبار: ${error.error || error.message}`);
        return { success: false, error: error.error || error.message };
    }
}

// تصدير الوظائف
if (typeof window !== 'undefined') {
    window.createArabicPDFFromHTML = createArabicPDFFromHTML;
    window.testArabicPDFSolution = testArabicPDFSolution;
    console.log('✅ الحل الجذري للترميز العربي متوفر عالمياً');
}

console.log('🔥 تم تحميل الحل الجذري النهائي للترميز العربي بنجاح');
