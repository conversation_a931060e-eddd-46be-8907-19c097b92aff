{"name": "customer-debt-manager", "productName": "نظام إدارة ديون العملاء", "version": "1.0.0", "description": "نظام شامل لإدارة ديون العملاء مع إحصائيات متقدمة - يعمل بدون إنترنت", "main": "electron/main.js", "homepage": "./", "author": {"name": "مطور النظام", "email": "<EMAIL>"}, "license": "MIT", "private": true, "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never", "postinstall": "electron-builder install-app-deps"}, "build": {"appId": "com.debtmanager.app", "productName": "نظام إدارة ديون العملاء", "directories": {"output": "dist", "buildResources": "build"}, "files": ["electron/**/*", "html/**/*", "css/**/*", "javascript/**/*", "libs/**/*", "icons/**/*", "manifest.json", "offline.html", "package.json"], "extraResources": [{"from": "data", "to": "data", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}], "icon": "build/icon.ico", "requestedExecutionLevel": "asInvoker", "artifactName": "${productName}-${version}-${arch}.${ext}"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "icon": "build/icon.icns", "category": "public.app-category.business", "artifactName": "${productName}-${version}-${arch}.${ext}"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "build/icon.png", "category": "Office", "artifactName": "${productName}-${version}-${arch}.${ext}"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "إدارة ديون العملاء", "include": "electron/installer.nsh", "artifactName": "${productName}-Setup-${version}.${ext}"}, "dmg": {"title": "${productName} ${version}", "icon": "build/icon.icns", "background": "build/dmg-background.png", "contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}]}, "publish": {"provider": "generic", "url": "https://example.com/updates/"}}, "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"electron-updater": "^6.1.4", "electron-store": "^8.1.0"}, "keywords": ["debt-management", "customer-management", "accounting", "business", "arabic", "offline"]}