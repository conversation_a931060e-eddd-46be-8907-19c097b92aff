// 🔥 الحل البسيط والفعال للترميز العربي في PDF
// استخدام jsPDF مع إعدادات محسنة خصيصاً للعربية

console.log('🔥 تحميل الحل البسيط والفعال للترميز العربي...');

// وظيفة تنظيف النص العربي
function cleanArabicText(text) {
    if (!text) return '';
    
    // تحويل إلى نص
    let cleanText = String(text).trim();
    
    // إزالة الرموز التي تسبب مشاكل
    cleanText = cleanText.replace(/[\u200E\u200F\u202A-\u202E]/g, ''); // إزالة رموز الاتجاه
    cleanText = cleanText.replace(/[\uFEFF]/g, ''); // إزالة BOM
    
    // تحويل الأرقام العربية إلى إنجليزية
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    
    for (let i = 0; i < arabicNumbers.length; i++) {
        cleanText = cleanText.replace(new RegExp(arabicNumbers[i], 'g'), englishNumbers[i]);
    }
    
    return cleanText;
}

// وظيفة إنشاء PDF بسيط مع دعم العربية
function createSimpleArabicPDF(data, title, type = 'report') {
    console.log('📄 إنشاء PDF بسيط مع دعم العربية...');
    
    try {
        const { jsPDF } = window.jspdf;
        
        // إنشاء PDF جديد مع إعدادات محسنة
        const pdf = new jsPDF({
            orientation: 'portrait',
            unit: 'mm',
            format: 'a4',
            compress: false, // تعطيل الضغط لتجنب مشاكل الترميز
            precision: 16
        });
        
        // إعدادات الصفحة
        const pageWidth = pdf.internal.pageSize.getWidth();
        const pageHeight = pdf.internal.pageSize.getHeight();
        const margin = 15;
        const usableWidth = pageWidth - (margin * 2);
        
        let yPosition = 30;
        
        // العنوان
        pdf.setFontSize(20);
        pdf.setFont('helvetica', 'bold');
        const cleanTitle = cleanArabicText(title);
        pdf.text(cleanTitle, pageWidth / 2, yPosition, { align: 'center' });
        
        yPosition += 15;
        
        // التاريخ
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'normal');
        const currentDate = new Date().toLocaleDateString('en-US');
        pdf.text(`Date: ${currentDate}`, pageWidth / 2, yPosition, { align: 'center' });
        
        yPosition += 20;
        
        // رؤوس الجدول
        pdf.setFontSize(14);
        pdf.setFont('helvetica', 'bold');
        
        const colWidths = [25, 45, 35, 30, 45];
        const headers = ['Invoice No', 'Customer Name', 'City', 'Amount (SAR)', 'Notes'];
        
        let xPosition = margin;
        
        // رسم رؤوس الجدول
        pdf.setFillColor(44, 62, 80); // لون خلفية الرأس
        pdf.setTextColor(255, 255, 255); // نص أبيض
        
        for (let i = 0; i < headers.length; i++) {
            pdf.rect(xPosition, yPosition - 8, colWidths[i], 12, 'F');
            pdf.text(headers[i], xPosition + colWidths[i] / 2, yPosition - 2, { align: 'center' });
            xPosition += colWidths[i];
        }
        
        yPosition += 10;
        
        // إعادة تعيين لون النص للبيانات
        pdf.setTextColor(0, 0, 0);
        pdf.setFont('helvetica', 'normal');
        pdf.setFontSize(11);
        
        let totalAmount = 0;
        
        // رسم البيانات
        data.forEach((item, index) => {
            // التحقق من الحاجة لصفحة جديدة
            if (yPosition > pageHeight - 30) {
                pdf.addPage();
                yPosition = 30;
            }
            
            xPosition = margin;
            
            // لون الخلفية للصفوف
            if (index % 2 === 0) {
                pdf.setFillColor(248, 249, 250);
                pdf.rect(margin, yPosition - 8, usableWidth, 12, 'F');
            }
            
            // البيانات
            const rowData = [
                cleanArabicText(item.invoiceNumber || ''),
                cleanArabicText(item.customerName || ''),
                cleanArabicText(item.city || ''),
                (parseFloat(item.amount) || 0).toLocaleString('en-US'),
                cleanArabicText(item.notes || '')
            ];
            
            // رسم البيانات
            for (let i = 0; i < rowData.length; i++) {
                // تقسيم النص الطويل
                const lines = pdf.splitTextToSize(rowData[i], colWidths[i] - 4);
                pdf.text(lines, xPosition + colWidths[i] / 2, yPosition - 2, { 
                    align: 'center',
                    maxWidth: colWidths[i] - 4
                });
                xPosition += colWidths[i];
            }
            
            totalAmount += parseFloat(item.amount) || 0;
            yPosition += 12;
        });
        
        // الإجمالي
        yPosition += 10;
        pdf.setFontSize(14);
        pdf.setFont('helvetica', 'bold');
        pdf.setFillColor(44, 62, 80);
        pdf.setTextColor(255, 255, 255);
        
        const totalText = `Total Amount: ${totalAmount.toLocaleString('en-US')} SAR`;
        pdf.rect(margin, yPosition - 8, usableWidth, 15, 'F');
        pdf.text(totalText, pageWidth / 2, yPosition, { align: 'center' });
        
        // حفظ الملف
        const fileName = `${cleanTitle.replace(/[^a-zA-Z0-9]/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;
        pdf.save(fileName);
        
        console.log(`✅ تم إنشاء PDF بسيط: ${fileName}`);
        
        return {
            success: true,
            fileName: fileName,
            method: 'Simple Arabic PDF',
            records: data.length
        };
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء PDF البسيط:', error);
        return {
            success: false,
            error: `خطأ في إنشاء PDF: ${error.message}`
        };
    }
}

// وظيفة إنشاء PDF مع تحويل البيانات إلى إنجليزية
function createEnglishOnlyPDF(data, title, type = 'report') {
    console.log('📄 إنشاء PDF بالإنجليزية فقط...');
    
    try {
        const { jsPDF } = window.jspdf;
        
        // تحويل البيانات إلى إنجليزية
        const englishData = data.map(item => ({
            invoiceNumber: item.invoiceNumber || '',
            customerName: transliterateArabic(item.customerName || ''),
            city: transliterateArabic(item.city || ''),
            amount: item.amount || 0,
            notes: transliterateArabic(item.notes || '')
        }));
        
        const pdf = new jsPDF('p', 'mm', 'a4');
        
        // إعدادات الصفحة
        const pageWidth = pdf.internal.pageSize.getWidth();
        const pageHeight = pdf.internal.pageSize.getHeight();
        const margin = 15;
        const usableWidth = pageWidth - (margin * 2);
        
        let yPosition = 30;
        
        // العنوان
        pdf.setFontSize(20);
        pdf.setFont('helvetica', 'bold');
        const englishTitle = transliterateArabic(title);
        pdf.text(englishTitle, pageWidth / 2, yPosition, { align: 'center' });
        
        yPosition += 15;
        
        // التاريخ
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'normal');
        const currentDate = new Date().toLocaleDateString('en-US');
        pdf.text(`Date: ${currentDate}`, pageWidth / 2, yPosition, { align: 'center' });
        
        yPosition += 20;
        
        // رؤوس الجدول
        pdf.setFontSize(14);
        pdf.setFont('helvetica', 'bold');
        
        const colWidths = [25, 45, 35, 30, 45];
        const headers = ['Invoice No', 'Customer Name', 'City', 'Amount (SAR)', 'Notes'];
        
        let xPosition = margin;
        
        // رسم رؤوس الجدول
        pdf.setFillColor(44, 62, 80);
        pdf.setTextColor(255, 255, 255);
        
        for (let i = 0; i < headers.length; i++) {
            pdf.rect(xPosition, yPosition - 8, colWidths[i], 12, 'F');
            pdf.text(headers[i], xPosition + colWidths[i] / 2, yPosition - 2, { align: 'center' });
            xPosition += colWidths[i];
        }
        
        yPosition += 10;
        
        // البيانات
        pdf.setTextColor(0, 0, 0);
        pdf.setFont('helvetica', 'normal');
        pdf.setFontSize(11);
        
        let totalAmount = 0;
        
        englishData.forEach((item, index) => {
            if (yPosition > pageHeight - 30) {
                pdf.addPage();
                yPosition = 30;
            }
            
            xPosition = margin;
            
            if (index % 2 === 0) {
                pdf.setFillColor(248, 249, 250);
                pdf.rect(margin, yPosition - 8, usableWidth, 12, 'F');
            }
            
            const rowData = [
                item.invoiceNumber,
                item.customerName,
                item.city,
                (parseFloat(item.amount) || 0).toLocaleString('en-US'),
                item.notes
            ];
            
            for (let i = 0; i < rowData.length; i++) {
                const lines = pdf.splitTextToSize(rowData[i], colWidths[i] - 4);
                pdf.text(lines, xPosition + colWidths[i] / 2, yPosition - 2, { 
                    align: 'center',
                    maxWidth: colWidths[i] - 4
                });
                xPosition += colWidths[i];
            }
            
            totalAmount += parseFloat(item.amount) || 0;
            yPosition += 12;
        });
        
        // الإجمالي
        yPosition += 10;
        pdf.setFontSize(14);
        pdf.setFont('helvetica', 'bold');
        pdf.setFillColor(44, 62, 80);
        pdf.setTextColor(255, 255, 255);
        
        const totalText = `Total Amount: ${totalAmount.toLocaleString('en-US')} SAR`;
        pdf.rect(margin, yPosition - 8, usableWidth, 15, 'F');
        pdf.text(totalText, pageWidth / 2, yPosition, { align: 'center' });
        
        const fileName = `${englishTitle.replace(/[^a-zA-Z0-9]/g, '_')}_English_${new Date().toISOString().split('T')[0]}.pdf`;
        pdf.save(fileName);
        
        console.log(`✅ تم إنشاء PDF إنجليزي: ${fileName}`);
        
        return {
            success: true,
            fileName: fileName,
            method: 'English Only PDF',
            records: data.length
        };
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء PDF الإنجليزي:', error);
        return {
            success: false,
            error: `خطأ في إنشاء PDF: ${error.message}`
        };
    }
}

// وظيفة تحويل النص العربي إلى حروف إنجليزية
function transliterateArabic(text) {
    if (!text) return '';
    
    const arabicToEnglish = {
        'أ': 'A', 'ا': 'A', 'إ': 'I', 'آ': 'A',
        'ب': 'B', 'ت': 'T', 'ث': 'Th', 'ج': 'J',
        'ح': 'H', 'خ': 'Kh', 'د': 'D', 'ذ': 'Th',
        'ر': 'R', 'ز': 'Z', 'س': 'S', 'ش': 'Sh',
        'ص': 'S', 'ض': 'D', 'ط': 'T', 'ظ': 'Z',
        'ع': 'A', 'غ': 'Gh', 'ف': 'F', 'ق': 'Q',
        'ك': 'K', 'ل': 'L', 'م': 'M', 'ن': 'N',
        'ه': 'H', 'و': 'W', 'ي': 'Y', 'ى': 'A',
        'ة': 'H', 'ء': 'A'
    };
    
    let result = '';
    for (let char of text) {
        result += arabicToEnglish[char] || char;
    }
    
    return result;
}

// وظيفة اختبار سريع
function testSimpleArabicPDF() {
    console.log('🧪 اختبار PDF البسيط مع دعم العربية...');
    
    const testData = [
        {
            invoiceNumber: '001',
            customerName: 'أحمد محمد',
            city: 'الرياض',
            amount: 1500,
            notes: 'ملاحظة تجريبية'
        },
        {
            invoiceNumber: '002',
            customerName: 'فاطمة علي',
            city: 'جدة',
            amount: 2500,
            notes: 'معلومات إضافية'
        }
    ];
    
    // جرب الطريقة البسيطة
    const result1 = createSimpleArabicPDF(testData, 'تقرير اختبار بسيط', 'test');
    
    if (result1.success) {
        alert(`✅ نجح PDF البسيط: ${result1.fileName}`);
    }
    
    // جرب الطريقة الإنجليزية
    const result2 = createEnglishOnlyPDF(testData, 'تقرير اختبار إنجليزي', 'test');
    
    if (result2.success) {
        alert(`✅ نجح PDF الإنجليزي: ${result2.fileName}`);
    }
}

// تصدير الوظائف
if (typeof window !== 'undefined') {
    window.createSimpleArabicPDF = createSimpleArabicPDF;
    window.createEnglishOnlyPDF = createEnglishOnlyPDF;
    window.testSimpleArabicPDF = testSimpleArabicPDF;
    window.cleanArabicText = cleanArabicText;
    window.transliterateArabic = transliterateArabic;
    console.log('✅ الحل البسيط للترميز العربي متوفر عالمياً');
}

console.log('🔥 تم تحميل الحل البسيط والفعال للترميز العربي بنجاح');
