<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>اختبار سريع للاستيراد</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 10px; }
        .log { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار سريع للاستيراد</h1>
        
        <div>
            <h3>📁 اختر ملف CSV:</h3>
            <input type="file" id="csvFile" accept=".csv">
            <br><br>
            <button class="btn" onclick="testImport()">اختبار الاستيراد</button>
            <button class="btn" onclick="clearLog()">مسح السجل</button>
        </div>
        
        <div>
            <h3>📝 سجل الأحداث:</h3>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function testImport() {
            const fileInput = document.getElementById('csvFile');
            const file = fileInput.files[0];
            
            if (!file) {
                log('❌ يرجى اختيار ملف CSV أولاً');
                return;
            }
            
            log('🚀 بدء اختبار الاستيراد...');
            log('📁 اسم الملف: ' + file.name);
            log('📊 حجم الملف: ' + file.size + ' بايت');
            
            const reader = new FileReader();
            
            reader.onload = function(e) {
                try {
                    log('📖 تم قراءة الملف بنجاح');
                    
                    let csvData = e.target.result;
                    log('📄 محتوى الملف:');
                    log(csvData);
                    
                    // Remove BOM if present
                    if (csvData.charCodeAt(0) === 0xFEFF) {
                        csvData = csvData.slice(1);
                        log('🔧 تم إزالة BOM');
                    }
                    
                    // Split into lines
                    const lines = csvData.split('\n').filter(line => line.trim());
                    log('📋 عدد الأسطر: ' + lines.length);
                    
                    if (lines.length < 2) {
                        log('❌ الملف فارغ أو لا يحتوي على بيانات');
                        return;
                    }
                    
                    // Detect separator
                    const headerLine = lines[0].trim();
                    const separator = headerLine.includes(';') ? ';' : ',';
                    log('📋 سطر الرؤوس: ' + headerLine);
                    log('🔍 الفاصل المكتشف: ' + separator);
                    
                    // Process data
                    const processedData = [];
                    
                    for (let i = 1; i < lines.length; i++) {
                        const line = lines[i].trim();
                        if (!line) continue;
                        
                        log('📝 معالجة السطر ' + i + ': ' + line);
                        
                        const parts = line.split(separator).map(field => field.trim());
                        log('🔧 أجزاء السطر: ' + JSON.stringify(parts));
                        
                        if (parts.length >= 4) {
                            const newItem = {
                                invoiceNumber: parts[0] || '',
                                customerName: parts[1] || '',
                                city: parts[2] || '',
                                amount: parseFloat(parts[3]) || 0,
                                notes: parts[4] || ''
                            };
                            
                            processedData.push(newItem);
                            log('✅ تم معالجة السطر: ' + JSON.stringify(newItem));
                        } else {
                            log('⚠️ تم تجاهل السطر (أجزاء غير كافية): ' + line);
                        }
                    }
                    
                    log('📊 عدد السجلات المعالجة: ' + processedData.length);
                    log('🎯 البيانات النهائية:');
                    processedData.forEach((item, index) => {
                        log(`${index + 1}. ${item.invoiceNumber} | ${item.customerName} | ${item.city} | ${item.amount} | ${item.notes}`);
                    });
                    
                    if (processedData.length > 0) {
                        log('✅ نجح الاختبار! البيانات جاهزة للاستيراد');
                    } else {
                        log('❌ فشل الاختبار! لم يتم العثور على بيانات صالحة');
                    }
                    
                } catch (error) {
                    log('❌ خطأ في معالجة الملف: ' + error.message);
                    console.error('Error:', error);
                }
            };
            
            reader.onerror = function() {
                log('❌ خطأ في قراءة الملف');
            };
            
            reader.readAsText(file, 'UTF-8');
        }
    </script>
</body>
</html>
