<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار العمل النهائي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <style>
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container-fluid p-4">
        <h1 class="text-center mb-4">🧪 اختبار العمل النهائي للنظام</h1>
        
        <!-- Status Section -->
        <div class="test-container">
            <h3>📊 حالة النظام</h3>
            <div id="systemStatus" class="row"></div>
        </div>
        
        <!-- Test Buttons -->
        <div class="test-container">
            <h3>🎯 اختبار الوظائف</h3>
            <div class="row">
                <div class="col-md-4">
                    <button class="btn btn-primary w-100 mb-2" onclick="addSampleData()">
                        <i class="fas fa-plus"></i> إضافة بيانات تجريبية
                    </button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-danger w-100 mb-2" onclick="testPDFExport()">
                        <i class="fas fa-file-pdf"></i> اختبار تصدير PDF
                    </button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-success w-100 mb-2" onclick="testExcelExport()">
                        <i class="fas fa-file-excel"></i> اختبار تصدير Excel
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Dropdown Test -->
        <div class="test-container">
            <h3>📋 اختبار القائمة المنسدلة</h3>
            <div class="debt-card new-invoices-card">
                <div class="debt-header">
                    <div class="debt-title">
                        <span class="debt-badge new-invoices-badge">اختبار القائمة</span>
                        <div class="debt-actions-menu">
                            <div class="dropdown">
                                <button class="dropdown-btn" onclick="toggleDropdown('testDropdown')">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <div class="dropdown-content" id="testDropdown">
                                    <a href="#" onclick="testPDFFromMenu()">
                                        <i class="fas fa-file-pdf"></i> طباعة PDF
                                    </a>
                                    <a href="#" onclick="testExcelFromMenu()">
                                        <i class="fas fa-file-excel"></i> تصدير إلى Excel
                                    </a>
                                    <a href="#" onclick="testImportFromMenu()">
                                        <i class="fas fa-file-import"></i> استيراد من Excel
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Log Section -->
        <div class="test-container">
            <h3>📝 سجل الأحداث</h3>
            <button class="btn btn-secondary btn-sm mb-2" onclick="clearLog()">مسح السجل</button>
            <div id="logArea" class="log-area"></div>
        </div>
    </div>

    <!-- Hidden file input for import test -->
    <input type="file" id="testImportFile" accept=".xlsx,.xls" style="display: none;" onchange="handleTestImport(event)">

    <!-- Scripts - Same order as original -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdn.sheetjs.com/xlsx-0.20.1/package/dist/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="javascript/arabic-font.js"></script>
    <script src="javascript/app.js"></script>
    <script src="javascript/export-import.js"></script>

    <script>
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            logArea.textContent += `[${timestamp}] ${icon} ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(`${icon} ${message}`);
        }

        function clearLog() {
            document.getElementById('logArea').textContent = '';
        }

        function updateStatus(id, status, message) {
            const statusDiv = document.getElementById(id);
            if (statusDiv) {
                const statusClass = status === 'success' ? 'status-success' : status === 'error' ? 'status-error' : 'status-warning';
                statusDiv.innerHTML = `<span class="status-indicator ${statusClass}"></span>${message}`;
            }
        }

        function checkSystemStatus() {
            const statusContainer = document.getElementById('systemStatus');
            statusContainer.innerHTML = `
                <div class="col-md-3"><div id="jspdf-status">جاري الفحص...</div></div>
                <div class="col-md-3"><div id="xlsx-status">جاري الفحص...</div></div>
                <div class="col-md-3"><div id="debtmanager-status">جاري الفحص...</div></div>
                <div class="col-md-3"><div id="functions-status">جاري الفحص...</div></div>
            `;

            // Check libraries
            setTimeout(() => {
                updateStatus('jspdf-status', 
                    typeof window.jspdf !== 'undefined' ? 'success' : 'error',
                    'مكتبة jsPDF'
                );
                
                updateStatus('xlsx-status',
                    typeof window.XLSX !== 'undefined' ? 'success' : 'error',
                    'مكتبة XLSX'
                );
                
                updateStatus('debtmanager-status',
                    typeof window.debtManager !== 'undefined' ? 'success' : 'error',
                    'نظام إدارة الديون'
                );
                
                const functionsOk = typeof window.printPDF !== 'undefined' && 
                                  typeof window.exportToExcel !== 'undefined' && 
                                  typeof window.importFromExcel !== 'undefined';
                
                updateStatus('functions-status',
                    functionsOk ? 'success' : 'error',
                    'وظائف التصدير/الاستيراد'
                );
            }, 1000);
        }

        function addSampleData() {
            log('🔄 إضافة بيانات تجريبية...');
            
            if (window.debtManager) {
                window.debtManager.debts = [
                    {
                        id: Date.now(),
                        invoiceNumber: 'TEST-001',
                        customerName: 'أحمد محمد',
                        city: 'الرياض',
                        amount: 1500,
                        notes: 'فاتورة تجريبية',
                        date: new Date().toLocaleDateString('en-GB')
                    },
                    {
                        id: Date.now() + 1,
                        invoiceNumber: 'TEST-002',
                        customerName: 'فاطمة علي',
                        city: 'جدة',
                        amount: 2000,
                        notes: 'فاتورة تجريبية أخرى',
                        date: new Date().toLocaleDateString('en-GB')
                    }
                ];
                
                window.debtManager.previousDebts = [
                    {
                        id: Date.now() + 2,
                        invoiceNumber: 'OLD-001',
                        customerName: 'محمد أحمد',
                        city: 'الدمام',
                        amount: 800,
                        notes: 'دين سابق',
                        date: new Date().toLocaleDateString('en-GB')
                    }
                ];
                
                // Update display if functions exist
                if (window.debtManager.updateStatistics) {
                    window.debtManager.updateStatistics();
                }
                if (window.debtManager.displayRecords) {
                    window.debtManager.displayRecords();
                }
                if (window.debtManager.displayPreviousDebt) {
                    window.debtManager.displayPreviousDebt(window.debtManager.previousDebts);
                }
                
                log('✅ تم إضافة البيانات التجريبية بنجاح', 'success');
                log(`📊 الفواتير الجديدة: ${window.debtManager.debts.length}`);
                log(`📊 الديون السابقة: ${window.debtManager.previousDebts.length}`);
            } else {
                log('❌ debtManager غير متوفر', 'error');
            }
        }

        function testPDFExport() {
            log('🔄 اختبار تصدير PDF...');
            
            if (typeof window.printPDF === 'function') {
                try {
                    window.printPDF('new');
                    log('✅ تم استدعاء وظيفة PDF بنجاح', 'success');
                } catch (error) {
                    log('❌ خطأ في تصدير PDF: ' + error.message, 'error');
                }
            } else {
                log('❌ وظيفة printPDF غير متوفرة', 'error');
            }
        }

        function testExcelExport() {
            log('🔄 اختبار تصدير Excel...');
            
            if (typeof window.exportToExcel === 'function') {
                try {
                    window.exportToExcel('new');
                    log('✅ تم استدعاء وظيفة Excel بنجاح', 'success');
                } catch (error) {
                    log('❌ خطأ في تصدير Excel: ' + error.message, 'error');
                }
            } else {
                log('❌ وظيفة exportToExcel غير متوفرة', 'error');
            }
        }

        function testPDFFromMenu() {
            log('🔄 اختبار PDF من القائمة المنسدلة...');
            testPDFExport();
        }

        function testExcelFromMenu() {
            log('🔄 اختبار Excel من القائمة المنسدلة...');
            testExcelExport();
        }

        function testImportFromMenu() {
            log('🔄 اختبار الاستيراد من القائمة المنسدلة...');
            document.getElementById('testImportFile').click();
        }

        function handleTestImport(event) {
            log('🔄 اختبار استيراد ملف...');
            
            if (typeof window.importFromExcel === 'function') {
                try {
                    window.importFromExcel(event, 'new');
                    log('✅ تم استدعاء وظيفة الاستيراد بنجاح', 'success');
                } catch (error) {
                    log('❌ خطأ في الاستيراد: ' + error.message, 'error');
                }
            } else {
                log('❌ وظيفة importFromExcel غير متوفرة', 'error');
            }
        }

        // Initialize when page loads
        window.addEventListener('load', function() {
            log('🎯 بدء اختبار النظام...');
            
            // Wait for all scripts to load
            setTimeout(() => {
                checkSystemStatus();
                log('✅ تم تحميل صفحة الاختبار', 'success');
                
                // Add sample data automatically
                setTimeout(addSampleData, 1000);
            }, 2000);
        });
    </script>
</body>
</html>
