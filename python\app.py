#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Customer Debt Management System - Flask Backend
نظام إدارة ديون العملاء - الخادم الخلفي
"""

from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import os
from datetime import datetime
from database import DatabaseManager

app = Flask(__name__)
CORS(app)

# Initialize database manager
db_manager = DatabaseManager()

@app.route('/')
def index():
    """Serve the main HTML page"""
    html_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'html')
    return send_from_directory(html_dir, 'index.html')

@app.route('/css/<path:filename>')
def css_files(filename):
    """Serve CSS files"""
    css_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'css')
    return send_from_directory(css_dir, filename)

@app.route('/javascript/<path:filename>')
def js_files(filename):
    """Serve JavaScript files"""
    js_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'javascript')
    return send_from_directory(js_dir, filename)

@app.route('/api/debts', methods=['GET'])
def get_debts():
    """Get all debt records"""
    try:
        debts = db_manager.get_all_debts()
        return jsonify({
            'success': True,
            'data': debts,
            'total_records': len(debts)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/debts', methods=['POST'])
def add_debt():
    """Add a new debt record"""
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['invoiceNumber', 'customerName', 'city', 'amount']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({
                    'success': False,
                    'error': f'Missing required field: {field}'
                }), 400

        # Check for duplicate invoice number
        if db_manager.invoice_exists(data['invoiceNumber']):
            return jsonify({
                'success': False,
                'error': 'Invoice number already exists'
            }), 400

        # Prepare debt record
        debt_record = {
            'invoice_number': data['invoiceNumber'],
            'customer_name': data['customerName'].strip(),
            'city': data['city'].strip(),
            'amount': float(data['amount']),
            'notes': data.get('notes', '').strip(),
            'date': datetime.now().strftime('%Y-%m-%d'),
            'timestamp': datetime.now().isoformat()
        }

        # Add to database
        debt_id = db_manager.add_debt(debt_record)
        debt_record['id'] = debt_id

        return jsonify({
            'success': True,
            'message': 'Debt record added successfully',
            'data': debt_record
        })

    except ValueError as e:
        return jsonify({
            'success': False,
            'error': 'Invalid amount value'
        }), 400
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/debts/<int:debt_id>', methods=['DELETE'])
def delete_debt(debt_id):
    """Delete a debt record"""
    try:
        if db_manager.delete_debt(debt_id):
            return jsonify({
                'success': True,
                'message': 'Debt record deleted successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Debt record not found'
            }), 404
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/debts/customer/<customer_name>/<city>', methods=['GET'])
def get_customer_debts(customer_name, city):
    """Get all debts for a specific customer"""
    try:
        debts = db_manager.get_customer_debts(customer_name, city)
        total_amount = sum(debt['amount'] for debt in debts)

        return jsonify({
            'success': True,
            'data': debts,
            'total_amount': total_amount,
            'total_invoices': len(debts)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/debts/search', methods=['GET'])
def search_debts():
    """Search debt records"""
    try:
        search_term = request.args.get('q', '').strip()
        if not search_term:
            return jsonify({
                'success': False,
                'error': 'Search term is required'
            }), 400

        debts = db_manager.search_debts(search_term)
        return jsonify({
            'success': True,
            'data': debts,
            'total_results': len(debts)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/statistics', methods=['GET'])
def get_statistics():
    """Get system statistics"""
    try:
        stats = db_manager.get_statistics()
        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/export', methods=['GET'])
def export_data():
    """Export all data as JSON"""
    try:
        debts = db_manager.get_all_debts()
        export_data = {
            'export_date': datetime.now().isoformat(),
            'total_records': len(debts),
            'debts': debts
        }

        return jsonify(export_data)
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/import', methods=['POST'])
def import_data():
    """Import data from JSON"""
    try:
        data = request.get_json()

        if 'debts' not in data or not isinstance(data['debts'], list):
            return jsonify({
                'success': False,
                'error': 'Invalid data format'
            }), 400

        imported_count = 0
        errors = []

        for debt_data in data['debts']:
            try:
                # Validate and add each debt record
                if db_manager.invoice_exists(debt_data.get('invoice_number', '')):
                    errors.append(f"Invoice {debt_data.get('invoice_number')} already exists")
                    continue

                debt_record = {
                    'invoice_number': debt_data['invoice_number'],
                    'customer_name': debt_data['customer_name'],
                    'city': debt_data['city'],
                    'amount': float(debt_data['amount']),
                    'notes': debt_data.get('notes', ''),
                    'date': debt_data.get('date', datetime.now().strftime('%Y-%m-%d')),
                    'timestamp': debt_data.get('timestamp', datetime.now().isoformat())
                }

                db_manager.add_debt(debt_record)
                imported_count += 1

            except Exception as e:
                errors.append(f"Error importing record: {str(e)}")

        return jsonify({
            'success': True,
            'message': f'Imported {imported_count} records',
            'imported_count': imported_count,
            'errors': errors
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    return jsonify({
        'success': False,
        'error': 'Endpoint not found'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    return jsonify({
        'success': False,
        'error': 'Internal server error'
    }), 500

if __name__ == '__main__':
    # Create data directory if it doesn't exist
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_dir = os.path.dirname(current_dir)
    data_dir = os.path.join(project_dir, 'data')
    os.makedirs(data_dir, exist_ok=True)

    # Initialize database
    db_manager.init_database()

    print("🚀 Customer Debt Management System Starting...")
    print("📊 نظام إدارة ديون العملاء")
    print("🌐 Server running at: http://localhost:5000")
    print(f"📁 Data directory: {data_dir}")

    app.run(debug=True, host='0.0.0.0', port=5000)
