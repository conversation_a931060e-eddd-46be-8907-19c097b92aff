# نموذج استيراد Excel - دليل المستخدم

## 🎯 الأعمدة المطلوبة

### الحقول الإجبارية:
1. **رقم الفاتورة** - يجب أن يكون فريد
2. **اسم العميل** - اسم العميل بالعربية أو الإنجليزية
3. **المدينة** - مدينة العميل
4. **المبلغ** - المبلغ بالأرقام فقط

### الحقول الاختيارية:
5. **الملاحظات** - أي ملاحظات إضافية

## 📊 نموذج ملف Excel

### الهيكل الصحيح:
```
A1: رقم الفاتورة    B1: اسم العميل    C1: المدينة    D1: المبلغ    E1: الملاحظات
A2: INV-001        B2: أحمد محمد     C2: الرياض     D2: 1500     E2: فاتورة تجريبية
A3: INV-002        B3: فاطمة علي     C3: جدة       D3: 2000     E3: فاتورة عادية
A4: INV-003        B4: محمد سالم     C4: الدمام     D4: 1200     E4: فاتورة مستعجلة
A5: INV-004        B5: سارة أحمد     C5: مكة       D5: 800      E5: فاتورة صغيرة
A6: INV-005        B6: خالد محمود    C6: المدينة    D6: 3000     E6: فاتورة كبيرة
```

## 🔤 أسماء الأعمدة المقبولة

### رقم الفاتورة:
- `رقم الفاتورة`
- `invoice`
- `Invoice Number`
- `فاتورة`

### اسم العميل:
- `اسم العميل`
- `customer`
- `Customer Name`
- `عميل`

### المدينة:
- `المدينة`
- `city`
- `City`
- `مدينة`

### المبلغ:
- `المبلغ`
- `amount`
- `Amount`
- `مبلغ`

### الملاحظات:
- `الملاحظات`
- `notes`
- `Notes`
- `ملاحظات`

## ✅ أمثلة صحيحة

### مثال 1 - بالعربية:
```
رقم الفاتورة | اسم العميل | المدينة | المبلغ | الملاحظات
INV-001      | أحمد محمد  | الرياض   | 1500  | فاتورة تجريبية
INV-002      | فاطمة علي  | جدة     | 2000  | فاتورة عادية
```

### مثال 2 - بالإنجليزية:
```
Invoice Number | Customer Name | City    | Amount | Notes
INV-001       | Ahmed Mohamed | Riyadh  | 1500   | Test invoice
INV-002       | Fatima Ali    | Jeddah  | 2000   | Regular invoice
```

### مثال 3 - مختلط:
```
invoice | customer | city | amount | notes
INV-001 | أحمد محمد | الرياض | 1500 | فاتورة تجريبية
INV-002 | فاطمة علي | جدة | 2000 | فاتورة عادية
```

## ❌ أخطاء شائعة

### خطأ في أسماء الأعمدة:
```
❌ خطأ:
رقم | اسم | مكان | قيمة | تعليق

✅ صحيح:
رقم الفاتورة | اسم العميل | المدينة | المبلغ | الملاحظات
```

### خطأ في البيانات:
```
❌ خطأ:
INV-001 | | الرياض | abc | فاتورة
        ↑ اسم فارغ  ↑ مبلغ غير صحيح

✅ صحيح:
INV-001 | أحمد محمد | الرياض | 1500 | فاتورة
```

### خطأ في أرقام الفواتير:
```
❌ خطأ - أرقام مكررة:
INV-001 | أحمد محمد | الرياض | 1500 | فاتورة
INV-001 | فاطمة علي | جدة | 2000 | فاتورة أخرى

✅ صحيح - أرقام فريدة:
INV-001 | أحمد محمد | الرياض | 1500 | فاتورة
INV-002 | فاطمة علي | جدة | 2000 | فاتورة أخرى
```

## 🛠️ خطوات الاستيراد

### 1. تحضير الملف:
```
1. افتح Excel أو Google Sheets
2. أنشئ الأعمدة المطلوبة في الصف الأول
3. أدخل البيانات في الصفوف التالية
4. احفظ الملف بصيغة .xlsx
```

### 2. الاستيراد:
```
1. افتح النظام
2. انقر على القائمة (⋮) في "الفواتير الجديدة"
3. اختر "استيراد من Excel"
4. اختر الملف المحضر
5. انتظر رسالة النجاح
```

### 3. التحقق:
```
1. تحقق من عدد السجلات المستوردة
2. راجع البيانات في الجدول
3. تأكد من صحة الإحصائيات
```

## 🔍 استكشاف الأخطاء

### رسالة: "الأعمدة المفقودة"
```
المشكلة: أسماء الأعمدة غير صحيحة
الحل: استخدم الأسماء المقبولة المذكورة أعلاه
```

### رسالة: "رقم الفاتورة مفقود"
```
المشكلة: خلية رقم الفاتورة فارغة
الحل: تأكد من وجود رقم فاتورة في كل صف
```

### رسالة: "المبلغ غير صحيح"
```
المشكلة: المبلغ يحتوي على نص أو فارغ
الحل: استخدم أرقام فقط (مثال: 1500)
```

### رسالة: "رقم الفاتورة مكرر"
```
المشكلة: نفس رقم الفاتورة موجود مرتين
الحل: استخدم أرقام فواتير فريدة
```

## 📋 قائمة التحقق

### قبل الاستيراد:
- [ ] الأعمدة المطلوبة موجودة
- [ ] أسماء الأعمدة صحيحة
- [ ] جميع أرقام الفواتير فريدة
- [ ] جميع أسماء العملاء مكتوبة
- [ ] جميع المدن مكتوبة
- [ ] جميع المبالغ أرقام صحيحة
- [ ] الملف محفوظ بصيغة .xlsx

### بعد الاستيراد:
- [ ] رسالة النجاح ظهرت
- [ ] عدد السجلات صحيح
- [ ] البيانات ظاهرة في الجدول
- [ ] الإحصائيات محدثة

## 💡 نصائح مفيدة

### لتجنب الأخطاء:
1. **استخدم نموذج التصدير** - صدّر ملف فارغ أولاً كنموذج
2. **تحقق من البيانات** - راجع الملف قبل الاستيراد
3. **ابدأ بعينة صغيرة** - جرب 2-3 سجلات أولاً
4. **احتفظ بنسخة احتياطية** - احفظ نسخة من البيانات الأصلية

### لتحسين الأداء:
1. **استيراد دفعات صغيرة** - لا تستورد أكثر من 100 سجل مرة واحدة
2. **تنظيف البيانات** - احذف الصفوف الفارغة
3. **استخدام UTF-8** - للنصوص العربية

## 🎯 مثال كامل

### ملف Excel نموذجي:
```
رقم الفاتورة | اسم العميل      | المدينة    | المبلغ | الملاحظات
INV-2025-001 | أحمد محمد علي   | الرياض     | 1500  | فاتورة خدمات استشارية
INV-2025-002 | فاطمة سالم أحمد | جدة       | 2000  | فاتورة صيانة أجهزة
INV-2025-003 | محمد عبدالله   | الدمام     | 1200  | فاتورة تطوير موقع
INV-2025-004 | سارة أحمد محمد  | مكة المكرمة| 800   | فاتورة تصميم شعار
INV-2025-005 | خالد محمود سعد  | المدينة المنورة| 3000 | فاتورة نظام إدارة
```

### النتيجة المتوقعة:
```
✅ تم استيراد 5 سجل بنجاح
✅ إجمالي المبلغ: 8,500 ريال
✅ عدد العملاء: 5
✅ تم تحديث الإحصائيات
```

**اتبع هذا الدليل وستتمكن من استيراد البيانات بنجاح!** 🚀
