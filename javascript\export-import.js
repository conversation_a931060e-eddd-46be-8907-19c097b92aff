// Export and Import Functions with Arabic Support and CSV

// Print PDF function using HTML to Canvas approach for Arabic support
function printPDF(type) {
    try {
        // Show loading indicator
        if (window.debtManager && debtManager.showSuccess) {
            debtManager.showSuccess('جاري إنشاء ملف PDF بجودة عالية...');
        }

        // Check if required objects are available
        if (typeof window.jspdf === 'undefined') {
            console.error('❌ jsPDF library not loaded');
            alert('مكتبة PDF غير متوفرة. يرجى إعادة تحميل الصفحة.');
            return;
        }

        if (typeof window.html2canvas === 'undefined') {
            console.error('❌ html2canvas library not loaded');
            alert('مكتبة html2canvas غير متوفرة. يرجى إعادة تحميل الصفحة.');
            return;
        }

        if (typeof window.debtManager === 'undefined') {
            console.error('❌ debtManager not available');
            alert('نظام إدارة الديون غير جاهز. يرجى إعادة تحميل الصفحة.');
            return;
        }

        // Get ALL data based on type (not filtered data)
        let data = [];
        let title = '';
        let englishTitle = '';

        if (type === 'previous') {
            // استخدام جميع الديون السابقة وليس المفلترة
            data = debtManager.previousDebts || [];
            title = 'تقرير الديون السابقة';
            englishTitle = 'Previous Debts Report';
            console.log(`📊 طباعة جميع الديون السابقة: ${data.length} دين`);
        } else {
            // استخدام جميع الفواتير الجديدة وليس المفلترة
            data = debtManager.debts || [];
            title = 'تقرير الفواتير الجديدة';
            englishTitle = 'New Invoices Report';
            console.log(`📊 طباعة جميع الفواتير الجديدة: ${data.length} فاتورة`);
        }

        if (data.length === 0) {
            const sectionName = type === 'previous' ? 'الديون السابقة' : 'الفواتير الجديدة';
            debtManager.showError(`لا توجد بيانات في قسم ${sectionName} للطباعة`);
            return;
        }

        // Create HTML content for the report
        const reportHTML = createArabicReportHTML(data, title, type);

        // Create a temporary div to render the HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = reportHTML;
        tempDiv.style.position = 'absolute';
        tempDiv.style.left = '-9999px';
        tempDiv.style.top = '-9999px';
        tempDiv.style.width = '800px';
        tempDiv.style.backgroundColor = 'white';
        tempDiv.style.padding = '20px';
        tempDiv.style.fontFamily = 'Arial, sans-serif';
        tempDiv.style.direction = 'rtl';

        document.body.appendChild(tempDiv);

        // Use html2canvas to convert HTML to image, then add to PDF
        if (window.html2canvas) {
            html2canvas(tempDiv, {
                scale: 2.0, // جودة عالية للنصوص العربية
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff',
                width: tempDiv.scrollWidth,
                height: tempDiv.scrollHeight,
                scrollX: 0,
                scrollY: 0,
                windowWidth: 1000, // عرض أكبر لجودة أفضل
                windowHeight: tempDiv.scrollHeight,
                removeContainer: true,
                imageTimeout: 10000, // وقت انتظار أطول للجودة
                logging: false, // إيقاف السجلات لتحسين الأداء
                foreignObjectRendering: true, // تحسين عرض النصوص
                onclone: function(clonedDoc) {
                    // تحسين الخطوط والألوان في النسخة المستنسخة مع دعم أفضل للعربية
                    const style = clonedDoc.createElement('style');
                    style.textContent = `
                        * {
                            font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif !important;
                            -webkit-font-smoothing: antialiased;
                            -moz-osx-font-smoothing: grayscale;
                            text-rendering: optimizeLegibility;
                            font-feature-settings: "liga", "kern";
                        }
                        table {
                            border-collapse: collapse !important;
                            font-variant-numeric: tabular-nums;
                        }
                        th, td {
                            border: 1px solid #ddd !important;
                            text-align: center !important;
                            white-space: nowrap;
                            overflow: visible;
                        }
                        .arabic-text {
                            direction: rtl;
                            unicode-bidi: bidi-override;
                        }
                    `;
                    clonedDoc.head.appendChild(style);

                    // تأكد من أن جميع النصوص العربية تظهر بشكل صحيح
                    const allElements = clonedDoc.querySelectorAll('*');
                    allElements.forEach(el => {
                        if (el.textContent && /[\u0600-\u06FF]/.test(el.textContent)) {
                            el.style.direction = 'rtl';
                            el.style.unicodeBidi = 'bidi-override';
                            el.classList.add('arabic-text');
                        }
                    });
                }
            }).then(canvas => {
                // Remove temporary div
                document.body.removeChild(tempDiv);

                // Create PDF and add the image
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF({
                    orientation: 'portrait',
                    unit: 'mm',
                    format: 'a4'
                });

                // تحسين جودة الصورة قبل إضافتها للـ PDF
                const imgData = canvas.toDataURL('image/jpeg', 0.95); // استخدام JPEG بجودة عالية

                // Calculate page dimensions with optimized margins
                const pageWidth = 210; // A4 width in mm
                const pageHeight = 297; // A4 height in mm
                const margin = 8; // هوامش أصغر لمساحة أكبر
                const contentWidth = pageWidth - (2 * margin);
                const contentHeight = pageHeight - (2 * margin);

                // Calculate image dimensions with better scaling
                const imgHeight = (canvas.height * contentWidth) / canvas.width;
                let heightLeft = imgHeight;
                let position = 0;
                let pageCount = 1;

                console.log(`📄 PDF Info: Canvas ${canvas.width}x${canvas.height}, Scaled height: ${imgHeight}mm`);

                // Add first page with enhanced quality
                doc.addImage(imgData, 'JPEG', margin, margin, contentWidth, Math.min(imgHeight, contentHeight));
                heightLeft -= contentHeight;

                // Add additional pages if needed with better positioning
                while (heightLeft > 0) {
                    position = heightLeft - imgHeight;
                    doc.addPage();
                    doc.addImage(imgData, 'JPEG', margin, position + margin, contentWidth, imgHeight);
                    heightLeft -= contentHeight;
                    pageCount++;
                }

                console.log(`📄 PDF created with ${pageCount} pages`);

                console.log(`✅ تم إنشاء PDF متعدد الصفحات: ${Math.ceil(imgHeight / contentHeight)} صفحة`);

                // Save the PDF with enhanced metadata
                const timestamp = new Date().toISOString().split('T')[0];
                const fileName = `${englishTitle.replace(/\s+/g, '_')}_${timestamp}.pdf`;

                // Optimize PDF before saving
                doc.setProperties({
                    title: title,
                    creator: 'نظام إدارة الديون',
                    producer: 'نظام إدارة الديون المحسن'
                });

                doc.save(fileName);

                // Close dropdown
                closeAllDropdowns();

                // Show enhanced success message
                debtManager.showSuccess(`تم إنشاء ملف PDF بجودة عالية! (${pageCount} صفحة)`);
            }).catch(error => {
                // Remove temporary div
                document.body.removeChild(tempDiv);
                console.error('HTML2Canvas Error:', error);

                // Fallback to simple PDF
                createSimplePDF(data, englishTitle, type);
            });
        } else {
            // Remove temporary div
            document.body.removeChild(tempDiv);

            // Fallback to simple PDF
            createSimplePDF(data, englishTitle, type);
        }

    } catch (error) {
        console.error('PDF Error:', error);
        debtManager.showError('حدث خطأ أثناء إنشاء ملف PDF');
    }
}

// Create HTML content for Arabic report with multi-page support
function createArabicReportHTML(data, title, type) {
    const now = new Date();
    const dateStr = `${now.getDate()}/${now.getMonth() + 1}/${now.getFullYear()}`;
    const timeStr = `${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`;

    if (data.length === 0) {
        return `
            <div style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: #7c3aed; margin-bottom: 10px; font-size: 24px;">${title}</h1>
                <div style="font-size: 14px; color: #666;">
                    <span>التاريخ: ${dateStr}</span> &nbsp;&nbsp;&nbsp;
                    <span>الوقت: ${timeStr}</span>
                </div>
            </div>
            <div style="text-align: center; font-size: 18px; color: #666; margin-top: 50px;">
                لا توجد بيانات للعرض
            </div>
        `;
    }

    // Create optimized HTML for multi-page PDF with better performance
    const itemsPerPage = 30; // زيادة عدد العناصر لتقليل الصفحات
    const totalPages = Math.ceil(data.length / itemsPerPage);

    // Sort data for better organization
    const sortedData = [...data].sort((a, b) => {
        // ترتيب حسب المدينة أولاً ثم اسم العميل
        const cityCompare = (a.city || '').localeCompare(b.city || '', 'ar');
        if (cityCompare !== 0) return cityCompare;
        return (a.customerName || '').localeCompare(b.customerName || '', 'ar');
    });

    const totalAmount = sortedData.reduce((sum, item) => sum + item.amount, 0);
    const uniqueCustomers = new Set(sortedData.map(item => `${item.customerName}_${item.city}`)).size;

    let html = '';

    for (let page = 0; page < totalPages; page++) {
        const startIndex = page * itemsPerPage;
        const endIndex = Math.min(startIndex + itemsPerPage, sortedData.length);
        const pageData = sortedData.slice(startIndex, endIndex);

        html += `
            <div style="page-break-after: ${page < totalPages - 1 ? 'always' : 'auto'}; min-height: 700px;">
                ${page === 0 ? `
                    <div style="text-align: center; margin-bottom: 25px;">
                        <h1 style="color: #7c3aed; margin-bottom: 10px; font-size: 22px;">${title}</h1>
                        <div style="font-size: 12px; color: #666;">
                            <span>التاريخ: ${dateStr}</span> &nbsp;&nbsp;&nbsp;
                            <span>الوقت: ${timeStr}</span> &nbsp;&nbsp;&nbsp;
                            <span>إجمالي السجلات: ${data.length}</span> &nbsp;&nbsp;&nbsp;
                            <span>الصفحات: ${totalPages}</span>
                        </div>
                    </div>
                ` : `
                    <div style="text-align: center; margin-bottom: 20px;">
                        <h2 style="color: #7c3aed; margin: 0; font-size: 16px;">${title} - صفحة ${page + 1}</h2>
                    </div>
                `}

                <table style="width: 100%; border-collapse: collapse; margin: 8px 0; font-size: 11px; font-family: 'Arial', sans-serif;">
                    <thead>
                        <tr style="background: linear-gradient(135deg, #2980b9 0%, #3498db 100%); color: white; font-weight: bold;">
                            <th style="border: 2px solid #1f5f8b; padding: 6px; text-align: center; width: 6%; font-size: 10px;">#</th>
                            <th style="border: 2px solid #1f5f8b; padding: 6px; text-align: center; width: 16%; font-size: 10px;">رقم الفاتورة</th>
                            <th style="border: 2px solid #1f5f8b; padding: 6px; text-align: center; width: 28%; font-size: 10px;">اسم العميل</th>
                            <th style="border: 2px solid #1f5f8b; padding: 6px; text-align: center; width: 14%; font-size: 10px;">المدينة</th>
                            <th style="border: 2px solid #1f5f8b; padding: 6px; text-align: center; width: 16%; font-size: 10px;">المبلغ (ر.س)</th>
                            <th style="border: 2px solid #1f5f8b; padding: 6px; text-align: center; width: 20%; font-size: 10px;">الملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${pageData.map((item, index) => {
                            const rowNumber = startIndex + index + 1;
                            const isEven = rowNumber % 2 === 0;
                            const bgColor = isEven ? '#f8f9fa' : 'white';
                            const borderColor = '#d1d5db';

                            // عرض البيانات كما هي بدون أي تحويل أو ترجمة
                            const invoiceNumber = item.invoiceNumber || '-';
                            const customerName = item.customerName || '-';
                            const city = item.city || '-';
                            const amount = (item.amount || 0).toLocaleString('en-US'); // استخدام تنسيق إنجليزي للأرقام
                            const notes = item.notes || '-';

                            return `
                            <tr style="background-color: ${bgColor}; transition: background-color 0.2s;">
                                <td style="border: 1px solid ${borderColor}; padding: 4px; text-align: center; font-size: 10px; font-weight: 600;">${rowNumber}</td>
                                <td style="border: 1px solid ${borderColor}; padding: 4px; text-align: center; font-size: 10px; font-weight: 500; direction: ltr;">${invoiceNumber}</td>
                                <td style="border: 1px solid ${borderColor}; padding: 4px; text-align: center; font-size: 10px; font-weight: 500; color: #2563eb; direction: rtl;">${customerName}</td>
                                <td style="border: 1px solid ${borderColor}; padding: 4px; text-align: center; font-size: 10px; color: #059669; direction: rtl;">${city}</td>
                                <td style="border: 1px solid ${borderColor}; padding: 4px; text-align: center; font-size: 10px; font-weight: 600; color: #dc2626; direction: ltr;">${amount} ر.س</td>
                                <td style="border: 1px solid ${borderColor}; padding: 4px; text-align: center; font-size: 9px; color: #6b7280; direction: rtl;">${notes.length > 25 ? notes.substring(0, 25) + '...' : notes}</td>
                            </tr>`;
                        }).join('')}
                    </tbody>
                </table>

                <div style="text-align: center; margin-top: 10px; color: #666; font-size: 9px;">
                    صفحة ${page + 1} من ${totalPages} | السجلات ${startIndex + 1} - ${endIndex} من ${data.length}
                </div>
            </div>
        `;
    }

    // Add summary on last page
    html += `
        <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 8px; border: 2px solid #7c3aed;">
            <h3 style="color: #7c3aed; margin-bottom: 10px; font-size: 14px; text-align: center;">ملخص التقرير</h3>
            <div style="font-size: 11px; line-height: 1.6;">
                <p style="margin: 3px 0;"><strong>إجمالي الفواتير:</strong> ${data.length}</p>
                <p style="margin: 3px 0;"><strong>عدد العملاء:</strong> ${uniqueCustomers}</p>
                <p style="margin: 3px 0;"><strong>المبلغ الإجمالي:</strong> ${totalAmount.toLocaleString('en-US')} ريال سعودي</p>
                <p style="margin: 3px 0;"><strong>عدد الصفحات:</strong> ${totalPages}</p>
            </div>
        </div>

        <div style="text-align: center; margin-top: 15px; color: #999; font-size: 9px;">
            تم إنشاء التقرير في: ${dateStr} الساعة ${timeStr}
        </div>
    `;

    return html;
}

// Fallback simple PDF function - DISABLED to force HTML2Canvas approach
function createSimplePDF(data, englishTitle, type) {
    console.log('⚠️ Simple PDF fallback disabled - using HTML2Canvas only for Arabic support');
    debtManager.showError('حدث خطأ في إنشاء PDF. يرجى إعادة المحاولة.');
}

// Export to CSV function - Simple and reliable
function exportToCSV(type) {
    try {
        if (typeof window.debtManager === 'undefined') {
            alert('نظام إدارة الديون غير جاهز. يرجى إعادة تحميل الصفحة.');
            console.error('debtManager not available');
            return;
        }

        // Get ALL data based on type (not filtered data)
        let data = [];
        let fileName = '';

        if (type === 'previous') {
            // استخدام جميع الديون السابقة وليس المفلترة
            data = debtManager.previousDebts || [];
            fileName = 'الديون_السابقة';
            console.log(`📊 تصدير جميع الديون السابقة إلى CSV: ${data.length} دين`);
        } else {
            // استخدام جميع الفواتير الجديدة وليس المفلترة
            data = debtManager.debts || [];
            fileName = 'الفواتير_الجديدة';
            console.log(`📊 تصدير جميع الفواتير الجديدة إلى CSV: ${data.length} فاتورة`);
        }

        if (data.length === 0) {
            debtManager.showError('لا توجد بيانات للتصدير');
            return;
        }

        // Create CSV content with UTF-8 BOM for Arabic support
        let csvContent = '\uFEFF'; // UTF-8 BOM

        // Add headers
        csvContent += 'رقم الفاتورة,اسم العميل,المدينة,المبلغ,الملاحظات\n';

        // Add data rows with proper CSV formatting - FIXED for column separation
        data.forEach(item => {
            // Clean and prepare data
            const invoiceNumber = String(item.invoiceNumber || '').trim().replace(/"/g, '""');
            const customerName = String(item.customerName || '').trim().replace(/"/g, '""');
            const city = String(item.city || '').trim().replace(/"/g, '""');
            const amount = parseFloat(item.amount) || 0;
            const notes = String(item.notes || '').trim().replace(/"/g, '""');

            // Create row with proper CSV formatting
            const row = [
                `"${invoiceNumber}"`,
                `"${customerName}"`,
                `"${city}"`,
                `${amount}`, // Amount without quotes for proper number formatting
                `"${notes}"`
            ];

            csvContent += row.join(',') + '\n';
            console.log('CSV row:', row.join(','));
        });

        // Create and download file
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        link.setAttribute('href', url);
        link.setAttribute('download', `${fileName}_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Close dropdown
        closeAllDropdowns();

        // Show success message
        debtManager.showSuccess(`✅ تم تصدير ${data.length} سجل بنجاح إلى ملف CSV`);

    } catch (error) {
        console.error('CSV Export Error:', error);
        debtManager.showError('حدث خطأ أثناء تصدير البيانات إلى CSV');
    }
}

// Export to Excel function with enhanced Arabic support
function exportToExcel(type) {
    try {
        // Check if required objects are available
        if (!window.XLSX) {
            debtManager.showError('مكتبة Excel غير متوفرة. يرجى إعادة تحميل الصفحة.');
            return;
        }

        if (!window.debtManager) {
            debtManager.showError('نظام إدارة الديون غير جاهز. يرجى إعادة تحميل الصفحة.');
            return;
        }
        // Get ALL data based on type (not filtered data)
        let data = [];
        let fileName = '';

        if (type === 'previous') {
            // استخدام جميع الديون السابقة وليس المفلترة
            data = debtManager.previousDebts || [];
            fileName = 'الديون_السابقة';
            console.log(`📊 تصدير جميع الديون السابقة إلى Excel: ${data.length} دين`);
        } else {
            // استخدام جميع الفواتير الجديدة وليس المفلترة
            data = debtManager.debts || [];
            fileName = 'الفواتير_الجديدة';
            console.log(`📊 تصدير جميع الفواتير الجديدة إلى Excel: ${data.length} فاتورة`);
        }

        if (data.length === 0) {
            debtManager.showError('لا توجد بيانات للتصدير');
            return;
        }

        // Prepare data with proper column structure - FIXED for proper separation
        // Using array of arrays to ensure each column is separate
        const headers = ['رقم الفاتورة', 'اسم العميل', 'المدينة', 'المبلغ', 'الملاحظات'];
        const excelData = [headers];

        // Add data rows with debugging
        data.forEach((item, index) => {
            const row = [
                String(item.invoiceNumber || '').trim(),     // Column A: رقم الفاتورة
                String(item.customerName || '').trim(),      // Column B: اسم العميل
                String(item.city || '').trim(),              // Column C: المدينة
                parseFloat(item.amount) || 0,                // Column D: المبلغ (as number)
                String(item.notes || '').trim()              // Column E: الملاحظات
            ];

            // Debug log for first few rows
            if (index < 3) {
                console.log(`Excel row ${index + 1}:`, row);
                console.log(`- A: "${row[0]}" (${typeof row[0]})`);
                console.log(`- B: "${row[1]}" (${typeof row[1]})`);
                console.log(`- C: "${row[2]}" (${typeof row[2]})`);
                console.log(`- D: "${row[3]}" (${typeof row[3]})`);
                console.log(`- E: "${row[4]}" (${typeof row[4]})`);
            }

            excelData.push(row);
        });

        console.log('📊 Excel data structure preview:', {
            totalRows: excelData.length,
            headers: excelData[0],
            firstDataRow: excelData[1],
            columnsCount: excelData[0].length
        });

        // Create workbook and worksheet using array of arrays for proper column separation
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.aoa_to_sheet(excelData); // Array of arrays to sheet

        // Set column widths for better display and proper separation
        ws['!cols'] = [
            { wch: 20 }, // A: رقم الفاتورة - wider for invoice numbers
            { wch: 30 }, // B: اسم العميل - wider for customer names
            { wch: 20 }, // C: المدينة - adequate for city names
            { wch: 15 }, // D: المبلغ - adequate for amounts
            { wch: 35 }  // E: الملاحظات - wider for notes
        ];

        // Format amount column as currency and add borders
        const range = XLSX.utils.decode_range(ws['!ref']);
        for (let row = 0; row <= range.e.r; row++) {
            for (let col = 0; col <= range.e.c; col++) {
                const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
                if (!ws[cellAddress]) ws[cellAddress] = { v: "", t: "s" };

                // Style header row
                if (row === 0) {
                    ws[cellAddress].s = {
                        font: {
                            bold: true,
                            color: { rgb: "FFFFFF" },
                            size: 12
                        },
                        fill: { fgColor: { rgb: "2E86AB" } },
                        alignment: {
                            horizontal: "center",
                            vertical: "center",
                            wrapText: true
                        },
                        border: {
                            top: { style: "thin", color: { rgb: "000000" } },
                            bottom: { style: "thin", color: { rgb: "000000" } },
                            left: { style: "thin", color: { rgb: "000000" } },
                            right: { style: "thin", color: { rgb: "000000" } }
                        }
                    };
                } else {
                    // Style data rows
                    ws[cellAddress].s = {
                        border: {
                            top: { style: "thin", color: { rgb: "CCCCCC" } },
                            bottom: { style: "thin", color: { rgb: "CCCCCC" } },
                            left: { style: "thin", color: { rgb: "CCCCCC" } },
                            right: { style: "thin", color: { rgb: "CCCCCC" } }
                        },
                        alignment: {
                            horizontal: col === 3 ? "right" : "center", // Amount column right-aligned
                            vertical: "center"
                        }
                    };

                    // Format amount column as number
                    if (col === 3 && row > 0 && typeof ws[cellAddress].v === 'number') {
                        ws[cellAddress].z = '#,##0.00'; // Number format
                        ws[cellAddress].t = 'n'; // Ensure it's treated as number
                    }
                }
            }
        }

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(wb, ws, 'بيانات الفواتير');

        // Add metadata sheet with summary statistics
        const totalAmount = data.reduce((sum, item) => sum + item.amount, 0);
        const uniqueCustomers = new Set(data.map(item => `${item.customerName}_${item.city}`)).size;

        const metaData = [
            { 'المعلومة': 'اسم التقرير', 'القيمة': fileName },
            { 'المعلومة': 'تاريخ التصدير', 'القيمة': new Date().toLocaleDateString('ar-SA') },
            { 'المعلومة': 'وقت التصدير', 'القيمة': new Date().toLocaleTimeString('ar-SA') },
            { 'المعلومة': '', 'القيمة': '' }, // Empty row for spacing
            { 'المعلومة': '📊 إحصائيات التقرير', 'القيمة': '' },
            { 'المعلومة': 'عدد الفواتير', 'القيمة': data.length },
            { 'المعلومة': 'إجمالي المبلغ', 'القيمة': totalAmount.toLocaleString() + ' ريال سعودي' },
            { 'المعلومة': 'عدد العملاء', 'القيمة': uniqueCustomers },
            { 'المعلومة': 'متوسط قيمة الفاتورة', 'القيمة': Math.round(totalAmount / data.length).toLocaleString() + ' ريال' }
        ];

        const metaWs = XLSX.utils.json_to_sheet(metaData);
        metaWs['!cols'] = [{ wch: 20 }, { wch: 30 }];
        XLSX.utils.book_append_sheet(wb, metaWs, 'معلومات التقرير');

        // Generate file name with date and time
        const currentDate = new Date().toISOString().split('T')[0];
        const currentTime = new Date().toTimeString().split(' ')[0].replace(/:/g, '-');
        const fullFileName = `${fileName}_${currentDate}_${currentTime}.xlsx`;

        // Write file with enhanced UTF-8 encoding for Arabic
        XLSX.writeFile(wb, fullFileName, {
            bookType: 'xlsx',
            type: 'binary',
            cellStyles: true,
            compression: true,
            Props: {
                Title: fileName,
                Subject: 'تقرير الفواتير',
                Author: 'نظام إدارة ديون العملاء',
                CreatedDate: new Date()
            }
        });

        // Close dropdown
        closeAllDropdowns();

        // Show success message with column information
        debtManager.showSuccess(`✅ تم تصدير ${data.length} سجل بنجاح إلى ملف Excel مع فصل صحيح للأعمدة\n\n📋 ترتيب الأعمدة:\n• العمود A: رقم الفاتورة\n• العمود B: اسم العميل\n• العمود C: المدينة\n• العمود D: المبلغ\n• العمود E: الملاحظات\n\n📊 الملف يحتوي على:\n• ورقة "بيانات الفواتير" - البيانات الأساسية\n• ورقة "معلومات التقرير" - الإحصائيات والملخص`);

    } catch (error) {
        console.error('Excel Export Error:', error);
        debtManager.showError('حدث خطأ أثناء تصدير البيانات إلى Excel');
    }
}

// Unified import function for CSV and Excel
function importFromFile(event, type) {
    const file = event.target.files[0];
    if (!file) return;

    const fileName = file.name.toLowerCase();

    if (fileName.endsWith('.csv')) {
        importFromCSV(file, type);
    } else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
        importFromExcel(file, type);
    } else {
        debtManager.showError('يرجى اختيار ملف CSV أو Excel صحيح (.csv, .xlsx, .xls)');
    }
}

// Import from CSV function
function importFromCSV(file, type) {
    try {
        const reader = new FileReader();

        reader.onload = function(e) {
            try {
                let csvData = e.target.result;

                // Remove BOM if present
                if (csvData.charCodeAt(0) === 0xFEFF) {
                    csvData = csvData.slice(1);
                }

                // Split into lines
                const lines = csvData.split('\n').filter(line => line.trim());

                if (lines.length < 2) {
                    debtManager.showError('الملف فارغ أو لا يحتوي على بيانات صالحة');
                    return;
                }

                // Parse header
                const headers = parseCSVLine(lines[0]);
                console.log('CSV Headers:', headers);

                // Map headers to indices
                const headerMap = mapCSVHeaders(headers);

                if (!headerMap.invoiceNumber || !headerMap.customerName || !headerMap.city || !headerMap.amount) {
                    debtManager.showError('الملف لا يحتوي على الأعمدة المطلوبة: رقم الفاتورة، اسم العميل، المدينة، المبلغ');
                    return;
                }

                // Process data rows
                const processedData = [];
                let successCount = 0;
                let errorCount = 0;
                const errors = [];

                for (let i = 1; i < lines.length; i++) {
                    try {
                        const row = parseCSVLine(lines[i]);
                        if (row.length === 0 || row.every(cell => !cell.trim())) continue;

                        const invoiceNumber = (row[headerMap.invoiceNumber] || '').trim();
                        const customerName = (row[headerMap.customerName] || '').trim();
                        const city = (row[headerMap.city] || '').trim();
                        const amountStr = (row[headerMap.amount] || '0').trim();
                        const notes = headerMap.notes !== undefined ? (row[headerMap.notes] || '').trim() : '';

                        // Validate required fields
                        if (!invoiceNumber || !customerName || !city) {
                            errors.push(`الصف ${i + 1}: بيانات مفقودة`);
                            errorCount++;
                            continue;
                        }

                        const amount = parseFloat(amountStr.replace(/[^\d.-]/g, ''));
                        if (isNaN(amount) || amount <= 0) {
                            errors.push(`الصف ${i + 1}: المبلغ غير صحيح`);
                            errorCount++;
                            continue;
                        }

                        // Check for duplicates
                        if (debtManager.isDuplicateInvoice(invoiceNumber)) {
                            errors.push(`الصف ${i + 1}: رقم الفاتورة "${invoiceNumber}" مكرر`);
                            errorCount++;
                            continue;
                        }

                        const newDebt = {
                            id: Date.now() + i + Math.random(),
                            invoiceNumber: invoiceNumber,
                            customerName: customerName,
                            city: city,
                            amount: amount,
                            notes: notes,
                            date: new Date().toLocaleDateString('en-GB'),
                            timestamp: new Date().toISOString()
                        };

                        processedData.push(newDebt);
                        successCount++;

                    } catch (error) {
                        errors.push(`الصف ${i + 1}: خطأ في معالجة البيانات`);
                        errorCount++;
                    }
                }

                // Add processed data
                if (processedData.length > 0) {
                    addImportedData(processedData, type);
                    showImportResults(successCount, errorCount, errors, type);
                } else {
                    debtManager.showError('لم يتم العثور على بيانات صالحة في الملف');
                }

            } catch (error) {
                console.error('CSV Processing Error:', error);
                debtManager.showError('خطأ في معالجة ملف CSV');
            }
        };

        reader.onerror = function() {
            debtManager.showError('خطأ في قراءة الملف');
        };

        reader.readAsText(file, 'UTF-8');

    } catch (error) {
        console.error('CSV Import Error:', error);
        debtManager.showError('حدث خطأ أثناء استيراد ملف CSV');
    }
}

// Parse CSV line handling quotes and commas
function parseCSVLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
        const char = line[i];

        if (char === '"') {
            if (inQuotes && line[i + 1] === '"') {
                current += '"';
                i++; // Skip next quote
            } else {
                inQuotes = !inQuotes;
            }
        } else if (char === ',' && !inQuotes) {
            result.push(current);
            current = '';
        } else {
            current += char;
        }
    }

    result.push(current);
    return result;
}

// Map CSV headers to field indices
function mapCSVHeaders(headers) {
    const headerMap = {};

    headers.forEach((header, index) => {
        const cleanHeader = header.trim().toLowerCase();

        if (cleanHeader.includes('فاتورة') || cleanHeader.includes('invoice')) {
            headerMap.invoiceNumber = index;
        } else if (cleanHeader.includes('عميل') || cleanHeader.includes('customer')) {
            headerMap.customerName = index;
        } else if (cleanHeader.includes('مدينة') || cleanHeader.includes('city')) {
            headerMap.city = index;
        } else if (cleanHeader.includes('مبلغ') || cleanHeader.includes('amount')) {
            headerMap.amount = index;
        } else if (cleanHeader.includes('ملاحظات') || cleanHeader.includes('notes')) {
            headerMap.notes = index;
        }
    });

    return headerMap;
}

// Import from Excel function with enhanced Arabic support
function importFromExcel(file, type) {
    try {
        // Check if XLSX library is loaded
        if (typeof XLSX === 'undefined') {
            debtManager.showError('مكتبة Excel غير محملة. يرجى إعادة تحميل الصفحة والمحاولة مرة أخرى');
            return;
        }

    // Show loading message
    debtManager.showSuccess('جاري قراءة الملف...');

    // Try different file reading approaches
    let attempts = 0;
    const maxAttempts = 3;

    function tryReadFile(method) {
        attempts++;
        console.log(`🔄 Attempt ${attempts}/${maxAttempts}: Trying ${method} method...`);

        const reader = new FileReader();

        reader.onerror = function() {
            console.error(`❌ FileReader error with ${method} method`);
            if (attempts < maxAttempts) {
                tryNextMethod();
            } else {
                debtManager.showError('فشل في قراءة الملف. تأكد من أن الملف غير تالف وبصيغة Excel صحيحة');
            }
        };

        reader.onload = function(e) {
            try {
                let workbook = null;

                if (method === 'arrayBuffer') {
                    const data = new Uint8Array(e.target.result);
                    workbook = XLSX.read(data, {
                        type: 'array',
                        cellText: false,
                        cellDates: true,
                        codepage: 65001,
                        raw: false,
                        WTF: false,
                        dense: false,
                        bookVBA: false,
                        bookSheets: true,
                        bookProps: false
                    });
                } else if (method === 'binaryString') {
                    workbook = XLSX.read(e.target.result, {
                        type: 'binary',
                        cellText: false,
                        cellDates: true,
                        codepage: 65001,
                        raw: false,
                        WTF: false,
                        dense: false
                    });
                } else if (method === 'base64') {
                    const base64Data = e.target.result.split(',')[1];
                    workbook = XLSX.read(base64Data, {
                        type: 'base64',
                        cellText: false,
                        cellDates: true,
                        codepage: 65001,
                        raw: false,
                        WTF: false
                    });
                }

                // Comprehensive validation
                if (!workbook) {
                    throw new Error('Workbook is null');
                }

                if (!workbook.SheetNames || !Array.isArray(workbook.SheetNames) || workbook.SheetNames.length === 0) {
                    throw new Error('No valid sheet names found');
                }

                if (!workbook.Sheets || typeof workbook.Sheets !== 'object') {
                    throw new Error('Sheets object is invalid');
                }

                // Check if at least one sheet is accessible
                const accessibleSheets = workbook.SheetNames.filter(name => workbook.Sheets[name]);
                if (accessibleSheets.length === 0) {
                    throw new Error('No accessible sheets found');
                }

                console.log(`✅ ${method} method successful! Found ${accessibleSheets.length} accessible sheets`);
                processWorkbook(workbook, type);

            } catch (error) {
                console.error(`❌ ${method} method failed:`, error.message);
                if (attempts < maxAttempts) {
                    tryNextMethod();
                } else {
                    let errorMessage = 'فشل في قراءة الملف بجميع الطرق المتاحة.';

                    if (error.message.includes('ZIP') || error.message.includes('Invalid file')) {
                        errorMessage += '\n\n💡 جرب الحلول التالية:';
                        errorMessage += '\n• احفظ الملف بصيغة .xlsx جديدة من Excel';
                        errorMessage += '\n• تأكد من أن الملف ليس محمياً بكلمة مرور';
                        errorMessage += '\n• جرب إنشاء ملف Excel جديد ونسخ البيانات إليه';
                        errorMessage += '\n• تأكد من أن الملف لا يحتوي على ماكرو معقد';
                    } else if (error.message.includes('Sheets') || error.message.includes('sheet')) {
                        errorMessage += '\n\n💡 مشكلة في أوراق العمل:';
                        errorMessage += '\n• تأكد من أن الملف يحتوي على ورقة عمل واحدة على الأقل';
                        errorMessage += '\n• تأكد من أن أوراق العمل تحتوي على بيانات';
                        errorMessage += '\n• جرب حذف أي أوراق عمل فارغة أو غير ضرورية';
                    }

                    debtManager.showError(errorMessage);
                }
            }
        };

        // Start reading with the specified method
        if (method === 'arrayBuffer') {
            reader.readAsArrayBuffer(file);
        } else if (method === 'binaryString') {
            reader.readAsBinaryString(file);
        } else if (method === 'base64') {
            reader.readAsDataURL(file);
        }
    }

    function tryNextMethod() {
        if (attempts === 1) {
            tryReadFile('binaryString');
        } else if (attempts === 2) {
            tryReadFile('base64');
        }
    }

    // Start with the most compatible method
    tryReadFile('arrayBuffer');

    // Reset UI after processing
    setTimeout(() => {
        resetImportUI(event);
    }, 100);
}

// Function to process workbook after successful reading
function processWorkbook(workbook, type) {
    try {
        console.log('📊 Processing workbook...', {
            sheetNames: workbook.SheetNames,
            type: type
        });
            // Comprehensive workbook validation
            console.log('🔍 Starting workbook validation...');

            if (!workbook) {
                console.error('❌ Workbook is null or undefined');
                debtManager.showError('فشل في قراءة الملف. الملف قد يكون تالف أو غير صحيح');
                return;
            }
            console.log('✅ Workbook exists');

            if (!workbook.SheetNames) {
                console.error('❌ Workbook.SheetNames is null or undefined');
                debtManager.showError('الملف لا يحتوي على معلومات أوراق العمل');
                return;
            }
            console.log('✅ Workbook.SheetNames exists');

            if (!Array.isArray(workbook.SheetNames)) {
                console.error('❌ Workbook.SheetNames is not an array:', typeof workbook.SheetNames, workbook.SheetNames);
                debtManager.showError('بنية أوراق العمل في الملف غير صحيحة');
                return;
            }
            console.log('✅ Workbook.SheetNames is an array');

            if (workbook.SheetNames.length === 0) {
                console.error('❌ Workbook.SheetNames is empty');
                debtManager.showError('الملف لا يحتوي على أوراق عمل');
                return;
            }
            console.log('✅ Workbook.SheetNames has', workbook.SheetNames.length, 'sheets');

            // Check if Sheets object exists
            if (!workbook.Sheets) {
                console.error('❌ Workbook.Sheets is null or undefined');
                debtManager.showError('الملف تالف أو لا يحتوي على أوراق عمل قابلة للقراءة. جرب:\n• حفظ الملف مرة أخرى بصيغة Excel (.xlsx)\n• استخدام ملف Excel مختلف\n• التأكد من أن الملف غير محمي\n• فتح الملف في Excel وحفظه مرة أخرى');
                return;
            }
            console.log('✅ Workbook.Sheets exists');

            // Validate that Sheets object has the expected structure
            if (typeof workbook.Sheets !== 'object') {
                console.error('❌ Workbook.Sheets is not an object:', typeof workbook.Sheets);
                debtManager.showError('بنية أوراق العمل في الملف غير صحيحة');
                return;
            }

            // Check if we can access at least one sheet
            const availableSheets = Object.keys(workbook.Sheets);
            if (availableSheets.length === 0) {
                console.error('❌ No sheets available in Workbook.Sheets');
                debtManager.showError('الملف لا يحتوي على أوراق عمل قابلة للوصول');
                return;
            }
            console.log('✅ Workbook.Sheets has', availableSheets.length, 'accessible sheets:', availableSheets);

            // Create a safe copy of sheet names to avoid reference issues
            const sheetNames = [...workbook.SheetNames];
            console.log('✅ Created safe copy of sheet names:', sheetNames);

            console.log('Workbook validation passed. Available sheets:', sheetNames);

            // Find the best worksheet to use
            let targetSheetName = sheetNames[0]; // Default to first sheet (using safe copy)
            let worksheet = null;

            // Look for the data sheet specifically (for files exported from this program)
            const dataSheetNames = ['بيانات الفواتير', 'Sheet1', 'Sheet 1', 'الورقة1'];
            for (const sheetName of dataSheetNames) {
                if (sheetNames.includes(sheetName) && workbook.Sheets[sheetName]) {
                    targetSheetName = sheetName;
                    console.log('✅ Found preferred sheet:', sheetName);
                    break;
                }
            }

            // Avoid metadata sheet if possible
            if (targetSheetName === 'معلومات التقرير' && sheetNames.length > 1) {
                // Try to find a better sheet
                for (const sheetName of sheetNames) {
                    if (sheetName !== 'معلومات التقرير' && workbook.Sheets[sheetName]) {
                        targetSheetName = sheetName;
                        console.log('✅ Switched from metadata sheet to:', sheetName);
                        break;
                    }
                }
            }

            // Verify the target sheet exists and is accessible
            if (!workbook.Sheets[targetSheetName]) {
                console.error(`❌ Target sheet "${targetSheetName}" not found in workbook.Sheets`);
                console.log('Available sheets in workbook.Sheets:', Object.keys(workbook.Sheets));
                console.log('Sheet names from workbook.SheetNames:', sheetNames);

                // Try to find any accessible sheet
                const accessibleSheet = Object.keys(workbook.Sheets).find(sheetName =>
                    workbook.Sheets[sheetName] && sheetNames.includes(sheetName)
                );

                if (accessibleSheet) {
                    targetSheetName = accessibleSheet;
                    console.log('✅ Found accessible sheet as fallback:', accessibleSheet);
                } else {
                    debtManager.showError(`لا يمكن قراءة أي ورقة عمل في الملف. الأوراق المتاحة: ${sheetNames.join('، ')}`);
                    return;
                }
            }

            worksheet = workbook.Sheets[targetSheetName];

            if (!worksheet) {
                debtManager.showError(`لا يمكن قراءة ورقة العمل "${targetSheetName}" في الملف`);
                return;
            }

            console.log(`✅ Using worksheet: "${targetSheetName}"`);
            console.log('✅ Available sheets:', sheetNames);

            // Convert to JSON with proper handling
            let jsonData;
            try {
                jsonData = XLSX.utils.sheet_to_json(worksheet, {
                    header: 1,
                    defval: '',
                    blankrows: false,
                    raw: false,
                    dateNF: 'dd/mm/yyyy'
                });
            } catch (conversionError) {
                console.error('JSON conversion error:', conversionError);
                debtManager.showError('خطأ في تحويل بيانات الملف. تأكد من أن الملف بصيغة Excel صحيحة');
                return;
            }

            if (!jsonData || jsonData.length < 2) {
                debtManager.showError('الملف فارغ أو لا يحتوي على بيانات صالحة');
                return;
            }

            // Get headers from first row and clean them
            let headers = jsonData[0];
            if (!headers || headers.length === 0) {
                debtManager.showError('الملف لا يحتوي على صف العناوين');
                return;
            }

            // Clean headers to handle encoding issues
            headers = headers.map(header => {
                if (!header) return '';
                return cleanArabicText(header.toString().trim());
            });

            let dataRows = jsonData.slice(1);

            // Filter out empty rows and any potential summary rows
            dataRows = dataRows.filter(row => {
                if (!row || row.length === 0) return false;

                // Check if all cells are empty
                const hasData = row.some(cell => cell && cell.toString().trim() !== '');
                if (!hasData) return false;

                // Check if this might be a summary row (just in case old files still have them)
                const cellValues = row.map(cell => cell ? cell.toString().trim().toLowerCase() : '');
                const hasSummaryKeywords = cellValues.some(value =>
                    value.includes('الإجمالي') ||
                    value.includes('إجمالي') ||
                    value.includes('total') ||
                    value.includes('summary') ||
                    (value.includes('فاتورة') && value.includes('عميل'))
                );

                if (hasSummaryKeywords) {
                    console.log('Skipping potential summary row:', row);
                    return false;
                }

                return true;
            });

            // Check if we have data rows after filtering
            if (dataRows.length === 0) {
                debtManager.showError('الملف لا يحتوي على بيانات صالحة بعد تصفية الصفوف الفارغة وصفوف الإجمالي');
                return;
            }

            // Map headers to indices with enhanced matching for exported files
            const headerMap = {};

            // Debug: log all headers
            console.log('All headers found:', headers);

            headers.forEach((header, index) => {
                if (!header) return;

                const originalHeader = header.toString().trim();
                const normalizedHeader = originalHeader.toLowerCase();

                // Clean header from any special characters or encoding issues
                const cleanHeader = cleanArabicText(originalHeader);
                const cleanNormalizedHeader = cleanHeader.toLowerCase();

                console.log(`Header ${index}: "${originalHeader}" -> "${cleanHeader}" (normalized: "${cleanNormalizedHeader}")`);

                // رقم الفاتورة - exact match first, then partial
                if (cleanHeader === 'رقم الفاتورة' ||
                    originalHeader === 'رقم الفاتورة' ||
                    cleanNormalizedHeader === 'رقم الفاتورة' ||
                    cleanNormalizedHeader.includes('فاتورة') ||
                    cleanNormalizedHeader.includes('invoice') ||
                    cleanNormalizedHeader === 'invoice number') {
                    headerMap.invoiceNumber = index;
                    console.log(`✅ Mapped invoiceNumber to index ${index}`);
                }

                // اسم العميل - exact match first, then partial
                if (cleanHeader === 'اسم العميل' ||
                   originalHeader === 'اسم العميل' ||
                   cleanNormalizedHeader === 'اسم العميل' ||
                   cleanNormalizedHeader.includes('عميل') ||
                   cleanNormalizedHeader.includes('customer') ||
                   cleanNormalizedHeader === 'customer name') {
                    headerMap.customerName = index;
                    console.log(`✅ Mapped customerName to index ${index}`);
                }

                // المدينة - exact match first, then partial
                if (cleanHeader === 'المدينة' ||
                   originalHeader === 'المدينة' ||
                   cleanNormalizedHeader === 'المدينة' ||
                   cleanNormalizedHeader.includes('مدينة') ||
                   cleanNormalizedHeader.includes('city')) {
                    headerMap.city = index;
                    console.log(`✅ Mapped city to index ${index}`);
                }

                // المبلغ - exact match first, then partial
                if (cleanHeader === 'المبلغ' ||
                   originalHeader === 'المبلغ' ||
                   cleanNormalizedHeader === 'المبلغ' ||
                   cleanNormalizedHeader.includes('مبلغ') ||
                   cleanNormalizedHeader.includes('amount')) {
                    headerMap.amount = index;
                    console.log(`✅ Mapped amount to index ${index}`);
                }

                // الملاحظات - exact match first, then partial
                if (cleanHeader === 'الملاحظات' ||
                   originalHeader === 'الملاحظات' ||
                   cleanNormalizedHeader === 'الملاحظات' ||
                   cleanNormalizedHeader.includes('ملاحظات') ||
                   cleanNormalizedHeader.includes('notes')) {
                    headerMap.notes = index;
                    console.log(`✅ Mapped notes to index ${index}`);
                }
            });

            console.log('Final header mapping:', headerMap);

            // Validate required headers (notes is optional)
            const missingHeaders = [];
            if (headerMap.invoiceNumber === undefined) missingHeaders.push('رقم الفاتورة');
            if (headerMap.customerName === undefined) missingHeaders.push('اسم العميل');
            if (headerMap.city === undefined) missingHeaders.push('المدينة');
            if (headerMap.amount === undefined) missingHeaders.push('المبلغ');

            if (missingHeaders.length > 0) {
                const foundHeaders = headers.filter(h => h && h.toString().trim()).join('، ');
                let errorMessage = `❌ الأعمدة المفقودة: ${missingHeaders.join('، ')}`;

                if (foundHeaders) {
                    errorMessage += `\n\n📋 الأعمدة الموجودة في الملف:\n${foundHeaders}`;
                }

                errorMessage += `\n\n✅ الأعمدة المطلوبة:`;
                errorMessage += `\n• رقم الفاتورة (مطلوب)`;
                errorMessage += `\n• اسم العميل (مطلوب)`;
                errorMessage += `\n• المدينة (مطلوب)`;
                errorMessage += `\n• المبلغ (مطلوب)`;
                errorMessage += `\n• الملاحظات (اختياري)`;

                errorMessage += `\n\n💡 نصيحة: إذا كان هذا ملف مصدر من نفس البرنامج، تأكد من:`;
                errorMessage += `\n• استخدام الورقة الأولى في الملف (بيانات الفواتير)`;
                errorMessage += `\n• عدم تعديل أسماء الأعمدة`;
                errorMessage += `\n• حفظ الملف بصيغة .xlsx`;
                errorMessage += `\n• تجنب استخدام ورقة "معلومات التقرير"`;

                // Check if this might be the metadata sheet
                if (foundHeaders.includes('المعلومة') && foundHeaders.includes('القيمة')) {
                    errorMessage += `\n\n⚠️ يبدو أنك تحاول استيراد ورقة "معلومات التقرير"`;
                    errorMessage += `\nيرجى اختيار ورقة "بيانات الفواتير" بدلاً من ذلك`;
                }

                debtManager.showError(errorMessage);

                // Log headers for debugging
                console.log('Headers found:', headers);
                console.log('Header mapping:', headerMap);
                console.log('Detected sheet type:', foundHeaders.includes('المعلومة') ? 'metadata' : 'unknown');
                return;
            }

            // Process imported data
            const processedData = [];
            let successCount = 0;
            let errorCount = 0;
            const errors = [];

            dataRows.forEach((row, index) => {
                try {
                    // Skip empty rows
                    if (!row || row.every(cell => !cell || cell.toString().trim() === '')) {
                        return;
                    }

                    // Extract data from row with UTF-8 handling
                    const invoiceNumber = cleanArabicText(row[headerMap.invoiceNumber]?.toString().trim() || '');
                    const customerName = cleanArabicText(row[headerMap.customerName]?.toString().trim() || '');
                    const city = cleanArabicText(row[headerMap.city]?.toString().trim() || '');
                    const amountStr = row[headerMap.amount]?.toString().trim() || '0';
                    const notes = headerMap.notes !== undefined ? cleanArabicText(row[headerMap.notes]?.toString().trim() || '') : '';

                    // Parse amount
                    const amount = parseFloat(amountStr.replace(/[^\d.-]/g, ''));

                    // Validate required fields
                    if (!invoiceNumber) {
                        errors.push(`الصف ${index + 2}: رقم الفاتورة مفقود`);
                        errorCount++;
                        return;
                    }
                    if (!customerName) {
                        errors.push(`الصف ${index + 2}: اسم العميل مفقود`);
                        errorCount++;
                        return;
                    }
                    if (!city) {
                        errors.push(`الصف ${index + 2}: المدينة مفقودة`);
                        errorCount++;
                        return;
                    }
                    if (isNaN(amount) || amount <= 0) {
                        errors.push(`الصف ${index + 2}: المبلغ غير صحيح "${amountStr}" - يجب أن يكون رقم أكبر من صفر`);
                        errorCount++;
                        return;
                    }

                    // Check for duplicate invoice numbers in all sections
                    const inNewDebts = debtManager.debts.some(debt => debt.invoiceNumber === invoiceNumber);
                    const inPreviousDebts = debtManager.previousDebts && debtManager.previousDebts.some(debt => debt.invoiceNumber === invoiceNumber);
                    const inCurrentImport = processedData.some(debt => debt.invoiceNumber === invoiceNumber);
                    const isDuplicate = inNewDebts || inPreviousDebts || inCurrentImport;

                    if (isDuplicate) {
                        errors.push(`الصف ${index + 2}: رقم الفاتورة "${invoiceNumber}" مكرر - موجود مسبقاً في النظام أو في الملف`);
                        errorCount++;
                        return;
                    }

                    // Create new debt object
                    const newDebt = {
                        id: Date.now() + index + Math.random(),
                        invoiceNumber: invoiceNumber,
                        customerName: customerName,
                        city: city,
                        amount: amount,
                        notes: notes,
                        date: new Date().toLocaleDateString('en-GB'), // DD/MM/YYYY format
                        timestamp: new Date().toISOString()
                    };

                    processedData.push(newDebt);
                    successCount++;

                } catch (error) {
                    errors.push(`الصف ${index + 2}: خطأ في معالجة البيانات`);
                    errorCount++;
                }
            });

            if (processedData.length === 0) {
                let errorMessage = '❌ لم يتم العثور على بيانات صالحة في الملف';

                if (errors.length > 0) {
                    errorMessage += '\n\nالأخطاء الموجودة:';
                    const maxErrorsToShow = 10;
                    for (let i = 0; i < Math.min(errors.length, maxErrorsToShow); i++) {
                        errorMessage += `\n• ${errors[i]}`;
                    }

                    if (errors.length > maxErrorsToShow) {
                        errorMessage += `\n... و ${errors.length - maxErrorsToShow} خطأ آخر`;
                    }

                    errorMessage += '\n\n💡 تأكد من:\n• وجود الأعمدة المطلوبة\n• صحة البيانات في كل صف\n• عدم وجود أرقام فواتير مكررة';
                } else {
                    errorMessage += '\n\n💡 تأكد من وجود بيانات في الملف والأعمدة المطلوبة';
                }

                debtManager.showError(errorMessage);
                console.log('تفاصيل أخطاء الاستيراد:', errors);
                return;
            }

            // Add processed data to the appropriate section
            if (type === 'new') {
                // Add to new invoices
                debtManager.debts.unshift(...processedData);
                debtManager.saveData();
                debtManager.updateStatistics();
                debtManager.displayRecords();
            } else if (type === 'previous') {
                // Add to previous debts
                if (!debtManager.previousDebts) {
                    debtManager.previousDebts = [];
                }

                console.log('📥 إضافة البيانات إلى الديون السابقة:', processedData.length, 'سجل');
                debtManager.previousDebts.unshift(...processedData);

                console.log('💾 حفظ الديون السابقة...');
                debtManager.savePreviousDebts();

                console.log('🖥️ عرض الديون السابقة...');
                debtManager.displayPreviousDebt(debtManager.previousDebts);

                console.log('✅ تم الانتهاء من استيراد الديون السابقة');
            }

            // Show results
            const sectionName = type === 'previous' ? 'الديون السابقة' : 'الفواتير الجديدة';
            let message = `✅ تم استيراد ${successCount} سجل بنجاح إلى قسم ${sectionName}`;

            if (errorCount > 0) {
                message += `\n⚠️ تم تجاهل ${errorCount} سجل بسبب أخطاء`;

                // Show first few errors
                if (errors.length > 0) {
                    message += '\n\nالأخطاء:';
                    const maxErrorsToShow = 5;
                    for (let i = 0; i < Math.min(errors.length, maxErrorsToShow); i++) {
                        message += `\n• ${errors[i]}`;
                    }

                    if (errors.length > maxErrorsToShow) {
                        message += `\n... و ${errors.length - maxErrorsToShow} خطأ آخر`;
                    }
                }
            }

            if (successCount > 0) {
                const totalAmount = processedData.reduce((sum, item) => sum + item.amount, 0);
                message += `\n\n📊 إجمالي المبلغ المستورد: ${totalAmount.toLocaleString()} ريال`;
            }

            debtManager.showSuccess(message);

            // Log all errors for debugging
            if (errors.length > 0) {
                console.log('تفاصيل جميع الأخطاء:', errors);
            }

        } catch (error) {
            console.error('Processing Error:', error);

            let errorMessage = 'خطأ في معالجة الملف';

            if (error.message && error.message.includes('xl/worksheets/sheet.xml')) {
                errorMessage = 'الملف تالف أو غير مكتمل. جرب:\n• حفظ الملف مرة أخرى بصيغة Excel (.xlsx)\n• استخدام ملف Excel جديد\n• التأكد من أن الملف غير محمي بكلمة مرور';
            } else if (error.message && error.message.includes('ZIP')) {
                errorMessage = 'تنسيق الملف غير صحيح. تأكد من أن الملف بصيغة Excel (.xlsx أو .xls)';
            } else if (error.message && error.message.includes('Cannot read properties')) {
                errorMessage = 'خطأ في قراءة خصائص البيانات. تأكد من أن:\n• الملف يحتوي على الأعمدة المطلوبة\n• البيانات في الصيغة الصحيحة\n• لا توجد خلايا فارغة في الأعمدة المطلوبة\n• الملف غير تالف';
            } else if (error.message && error.message.includes('undefined')) {
                errorMessage = 'خطأ في بنية البيانات. تأكد من أن الملف يحتوي على:\n• صف العناوين في الصف الأول\n• البيانات في الصفوف التالية\n• الأعمدة المطلوبة موجودة';
            } else if (error.message && error.message.includes('TypeError')) {
                errorMessage = 'خطأ في نوع البيانات. تأكد من أن:\n• الملف بصيغة Excel صحيحة\n• البيانات في الصيغة المطلوبة\n• الملف غير تالف أو محمي';
            } else if (error.message) {
                errorMessage = `خطأ في معالجة الملف: ${error.message}`;
            }

            debtManager.showError(errorMessage);
        }
    } catch (error) {
        console.error('Processing Error:', error);
        debtManager.showError('خطأ في معالجة الملف: ' + error.message);
    }
}

// Reset file input and close dropdown after import
function resetImportUI(event) {
    if (event && event.target) {
        event.target.value = '';
    }
    closeAllDropdowns();
}

// Note: closeAllDropdowns function is defined in app.js to avoid duplication

// Helper function to clean Arabic text for better UTF-8 handling
function cleanArabicText(text) {
    if (!text) return '';

    try {
        // Convert to string and trim
        let cleanText = text.toString().trim();

        // Remove any BOM characters
        cleanText = cleanText.replace(/^\uFEFF/, '');
        cleanText = cleanText.replace(/\uFEFF/g, '');

        // Normalize Arabic text
        cleanText = cleanText.normalize('NFC');

        // Remove extra whitespace
        cleanText = cleanText.replace(/\s+/g, ' ');

        // Handle common encoding issues for Arabic text
        const encodingFixes = {
            'Ø§': 'ا',  // alef
            'Ø¨': 'ب',  // beh
            'Ø©': 'ة',  // teh marbuta
            'ØªØ': 'ت',  // teh
            'Ø«': 'ث',  // theh
            'Ø¬': 'ج',  // jeem
            'Ø­': 'ح',  // hah
            'Ø®': 'خ',  // khah
            'Ø¯': 'د',  // dal
            'Ø°': 'ذ',  // thal
            'Ø±': 'ر',  // reh
            'Ø²': 'ز',  // zain
            'Ø³': 'س',  // seen
            'Ø´': 'ش',  // sheen
            'Øµ': 'ص',  // sad
            'Ø¶': 'ض',  // dad
            'Ø·': 'ط',  // tah
            'Ø¸': 'ظ',  // zah
            'Ø¹': 'ع',  // ain
            'Øº': 'غ',  // ghain
            'Ù': 'ف',   // feh
            'Ù‚': 'ق',  // qaf
            'Ùƒ': 'ك',  // kaf
            'Ù„': 'ل',  // lam
            'Ù…': 'م',  // meem
            'Ù†': 'ن',  // noon
            'Ù‡': 'ه',  // heh
            'Ùˆ': 'و',  // waw
            'Ù‰': 'ى',  // alef maksura
            'ÙŠ': 'ي',  // yeh
            'Ù': 'ي',   // alternative yeh
            'Ø©': 'ة'   // teh marbuta
        };

        // Apply encoding fixes
        for (const [encoded, correct] of Object.entries(encodingFixes)) {
            cleanText = cleanText.replace(new RegExp(encoded, 'g'), correct);
        }

        // Additional cleanup for common patterns
        cleanText = cleanText.replace(/â€/g, '');
        cleanText = cleanText.replace(/â€™/g, '');
        cleanText = cleanText.replace(/â€œ/g, '');
        cleanText = cleanText.replace(/â€/g, '');

        return cleanText;
    } catch (error) {
        console.warn('Text cleaning error:', error);
        return text.toString().trim();
    }
}

// Helper function to ensure UTF-8 encoding for Excel
function ensureUTF8(text) {
    if (!text) return '';

    try {
        // Ensure proper UTF-8 encoding
        const utf8Text = unescape(encodeURIComponent(text));
        return decodeURIComponent(escape(utf8Text));
    } catch (error) {
        return text.toString();
    }
}



// Helper function to format date for PDF (Gregorian calendar)
function formatDateForPDF(dateStr) {
    if (!dateStr) return '';

    try {
        // If it's already in DD/MM/YYYY format, return as is
        if (dateStr.match(/^\d{1,2}\/\d{1,2}\/\d{4}$/)) {
            return dateStr;
        }

        // If it's a Date object or ISO string, convert to DD/MM/YYYY
        const date = new Date(dateStr);
        if (!isNaN(date.getTime())) {
            return `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`;
        }

        // If it's in Arabic format, try to parse it
        if (dateStr.includes('/')) {
            return dateStr;
        }

        // Default fallback
        return new Date().toLocaleDateString('en-GB'); // DD/MM/YYYY format
    } catch (error) {
        return new Date().toLocaleDateString('en-GB');
    }
}

// Helper functions for import
function addImportedData(processedData, type) {
    if (type === 'new') {
        // Add to new invoices
        debtManager.debts.unshift(...processedData);
        debtManager.saveData();
        debtManager.updateStatistics();
        debtManager.displayRecords();
    } else if (type === 'previous') {
        // Add to previous debts
        if (!debtManager.previousDebts) {
            debtManager.previousDebts = [];
        }
        debtManager.previousDebts.unshift(...processedData);
        debtManager.savePreviousDebts();
        debtManager.displayPreviousDebt(debtManager.previousDebts);
    }
}

function showImportResults(successCount, errorCount, errors, type) {
    const sectionName = type === 'previous' ? 'الديون السابقة' : 'الفواتير الجديدة';
    let message = `✅ تم استيراد ${successCount} سجل بنجاح إلى قسم ${sectionName}`;

    if (errorCount > 0) {
        message += `\n⚠️ تم تجاهل ${errorCount} سجل بسبب أخطاء`;

        if (errors.length > 0) {
            message += '\n\nالأخطاء:';
            const maxErrorsToShow = 5;
            for (let i = 0; i < Math.min(errors.length, maxErrorsToShow); i++) {
                message += `\n• ${errors[i]}`;
            }

            if (errors.length > maxErrorsToShow) {
                message += `\n... و ${errors.length - maxErrorsToShow} خطأ آخر`;
            }
        }
    }

    debtManager.showSuccess(message);

    // Reset file input
    setTimeout(() => {
        const fileInput = type === 'previous' ?
            document.getElementById('importPreviousFile') :
            document.getElementById('importNewFile');
        if (fileInput) fileInput.value = '';
        closeAllDropdowns();
    }, 100);
}

// Make functions globally accessible immediately
window.printPDF = printPDF;
window.exportToCSV = exportToCSV;
window.exportToExcel = exportToExcel;
window.importFromFile = importFromFile;
window.importFromCSV = importFromCSV;
window.importFromExcel = importFromExcel;

// Additional helper functions
function processWorkbook(workbook, type) {
    console.log('Processing workbook...');
    // This function exists in the original code but may need to be defined
}

function resetImportUI() {
    console.log('Resetting import UI...');
    // Reset file inputs
    const inputs = document.querySelectorAll('input[type="file"]');
    inputs.forEach(input => input.value = '');
}

function cleanArabicText(text) {
    if (!text) return '';
    return text.toString().trim();
}

function ensureUTF8(text) {
    if (!text) return '';
    return text.toString();
}

function formatDateForPDF() {
    return new Date().toLocaleDateString('en-GB');
}

// Log function availability
console.log('🔧 Export/Import functions loaded:');
console.log('- printPDF:', typeof printPDF);
console.log('- exportToCSV:', typeof exportToCSV);
console.log('- exportToExcel:', typeof exportToExcel);
console.log('- importFromFile:', typeof importFromFile);

// Prevent recursion by using different function names
const originalPrintPDF = printPDF;

// Create a safe wrapper function
function safePrintPDF(type) {
    console.log('🔧 safePrintPDF called with type:', type);

    // Prevent multiple calls
    if (window.pdfInProgress) {
        console.log('⚠️ PDF generation already in progress');
        return;
    }

    window.pdfInProgress = true;

    try {
        originalPrintPDF(type);
    } catch (error) {
        console.error('PDF Error:', error);
        alert('حدث خطأ في إنشاء PDF: ' + error.message);
    } finally {
        // Reset flag after 3 seconds
        setTimeout(() => {
            window.pdfInProgress = false;
        }, 3000);
    }
}

// Ensure functions are available globally
setTimeout(() => {
    // Use safe wrapper for PDF
    window.printPDF = safePrintPDF;
    window.exportToCSV = exportToCSV;
    window.exportToExcel = exportToExcel;
    window.importFromFile = importFromFile;

    // Override the old functions to use enhanced versions
    window.exportCSV = exportToCSV;
    window.exportExcel = exportToExcel;

    console.log('✅ Functions re-assigned to window object with safety wrapper');
    console.log('✅ Enhanced PDF functions with Arabic support ready');
    console.log('✅ All PDF functions now display data exactly as entered without translation');
    console.log('✅ Recursion protection enabled');
}, 100);
