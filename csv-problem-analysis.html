<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحليل مشكلة CSV - لماذا عمود واحد؟</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #c0392b;
            text-align: center;
            margin-bottom: 30px;
            font-size: 32px;
        }
        .problem-section {
            background: #f8d7da;
            border: 3px solid #f5c6cb;
            border-radius: 10px;
            padding: 25px;
            margin: 20px 0;
        }
        .solution-section {
            background: #d4edda;
            border: 3px solid #c3e6cb;
            border-radius: 10px;
            padding: 25px;
            margin: 20px 0;
        }
        .info-section {
            background: #d1ecf1;
            border: 3px solid #bee5eb;
            border-radius: 10px;
            padding: 25px;
            margin: 20px 0;
        }
        .code-demo {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 15px 0;
            direction: ltr;
            text-align: left;
            overflow-x: auto;
        }
        .excel-demo {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 2px;
            margin: 15px 0;
            border: 2px solid #333;
            border-radius: 8px;
            overflow: hidden;
        }
        .excel-header {
            background: #34495e;
            color: white;
            padding: 10px;
            text-align: center;
            font-weight: bold;
            font-size: 12px;
        }
        .excel-cell {
            background: #ecf0f1;
            padding: 8px;
            text-align: center;
            font-size: 11px;
            border-right: 1px solid #bdc3c7;
        }
        .excel-cell.wrong {
            background: #ffebee;
            color: #c62828;
        }
        .excel-cell.correct {
            background: #e8f5e8;
            color: #2e7d32;
        }
        .test-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            width: calc(33% - 20px);
            display: inline-block;
        }
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
        }
        .separator-demo {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            direction: ltr;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 تحليل مشكلة CSV - لماذا عمود واحد؟</h1>
        
        <div class="problem-section">
            <h2>❌ المشكلة الحالية:</h2>
            <p><strong>البيانات تظهر هكذا في Excel:</strong></p>
            
            <div class="excel-demo">
                <div class="excel-header">A</div>
                <div class="excel-header">B</div>
                <div class="excel-header">C</div>
                <div class="excel-header">D</div>
                <div class="excel-header">E</div>
                <div class="excel-header">F</div>
                
                <div class="excel-cell wrong">رقم الفاتورة,اسم العميل,المدينة,المبلغ,الملاحظات</div>
                <div class="excel-cell"></div>
                <div class="excel-cell"></div>
                <div class="excel-cell"></div>
                <div class="excel-cell"></div>
                <div class="excel-cell"></div>
                
                <div class="excel-cell wrong">1,"أحمد","الرياض",1500,"ملاحظة"</div>
                <div class="excel-cell"></div>
                <div class="excel-cell"></div>
                <div class="excel-cell"></div>
                <div class="excel-cell"></div>
                <div class="excel-cell"></div>
            </div>
            
            <p><strong style="color: #c62828;">❌ كل البيانات في العمود A فقط!</strong></p>
        </div>

        <div class="info-section">
            <h2>🔍 الأسباب الجذرية:</h2>
            
            <h3>1️⃣ مشكلة فاصل الأعمدة:</h3>
            <div class="separator-demo">
<strong>❌ الفاصلة (,) لا تعمل في Excel العربي:</strong><br>
"001","أحمد محمد","الرياض",1500,"ملاحظة"<br>
<span style="color: red;">↑ Excel لا يتعرف على الفاصلة كفاصل أعمدة</span><br><br>

<strong>✅ الفاصل المنقوطة (;) يعمل:</strong><br>
"001";"أحمد محمد";"الرياض";1500;"ملاحظة"<br>
<span style="color: green;">↑ Excel يتعرف على الفاصل المنقوطة</span>
            </div>

            <h3>2️⃣ مشكلة إعدادات النظام:</h3>
            <ul>
                <li><strong>Windows العربي:</strong> يستخدم الفاصل المنقوطة (;) كفاصل افتراضي</li>
                <li><strong>Excel العربي:</strong> يتوقع الفاصل المنقوطة (;)</li>
                <li><strong>المشكلة:</strong> الكود يستخدم الفاصلة (,)</li>
            </ul>

            <h3>3️⃣ مشكلة ترميز النصوص:</h3>
            <ul>
                <li><strong>النصوص العربية:</strong> تحتاج UTF-8 BOM</li>
                <li><strong>بدون BOM:</strong> قد تظهر رموز غريبة</li>
                <li><strong>فاصلات داخل النص:</strong> تخرب تقسيم الأعمدة</li>
            </ul>
        </div>

        <div class="solution-section">
            <h2>✅ الحلول الشاملة:</h2>
            
            <h3>🔧 الحل الأول: تغيير الفاصل</h3>
            <div class="code-demo">
// ❌ الطريقة الخاطئة
const csvRow = data.join(','); // فاصلة

// ✅ الطريقة الصحيحة  
const csvRow = data.join(';'); // فاصل منقوطة
            </div>

            <h3>🔧 الحل الثاني: تنظيف البيانات</h3>
            <div class="code-demo">
// إزالة الفواصل المنقوطة من البيانات
const cleanData = text.replace(/;/g, ',');

// تغليف النصوص بعلامات التنصيص
const wrappedText = `"${cleanData}"`;
            </div>

            <h3>🔧 الحل الثالث: إضافة UTF-8 BOM</h3>
            <div class="code-demo">
// إضافة UTF-8 BOM للنصوص العربية
let csvContent = '\uFEFF'; // UTF-8 BOM
csvContent += 'رقم الفاتورة;اسم العميل;المدينة;المبلغ;الملاحظات\n';
            </div>
        </div>

        <div class="solution-section">
            <h2>🎯 النتيجة المطلوبة:</h2>
            
            <div class="excel-demo">
                <div class="excel-header">A</div>
                <div class="excel-header">B</div>
                <div class="excel-header">C</div>
                <div class="excel-header">D</div>
                <div class="excel-header">E</div>
                <div class="excel-header">F</div>
                
                <div class="excel-cell correct">رقم الفاتورة</div>
                <div class="excel-cell correct">اسم العميل</div>
                <div class="excel-cell correct">المدينة</div>
                <div class="excel-cell correct">المبلغ</div>
                <div class="excel-cell correct">الملاحظات</div>
                <div class="excel-cell"></div>
                
                <div class="excel-cell correct">001</div>
                <div class="excel-cell correct">أحمد محمد</div>
                <div class="excel-cell correct">الرياض</div>
                <div class="excel-cell correct">1500</div>
                <div class="excel-cell correct">ملاحظة تجريبية</div>
                <div class="excel-cell"></div>
            </div>
            
            <p><strong style="color: #2e7d32;">✅ كل بيان في عمود منفصل!</strong></p>
        </div>

        <button class="test-btn" onclick="testWrongCSV()">
            ❌ اختبار CSV خاطئ (فاصلة)
        </button>

        <button class="test-btn" onclick="testCorrectCSV()">
            ✅ اختبار CSV صحيح (فاصل منقوطة)
        </button>

        <button class="test-btn" onclick="testBothMethods()">
            🔍 اختبار الطريقتين معاً
        </button>

        <div id="results" style="margin-top: 30px;"></div>
    </div>

    <script>
        const testData = [
            { invoiceNumber: '001', customerName: 'أحمد محمد', city: 'الرياض', amount: 1500, notes: 'ملاحظة تجريبية' },
            { invoiceNumber: '002', customerName: 'فاطمة علي', city: 'جدة', amount: 2000, notes: 'ملاحظة أخرى' }
        ];

        function showResults(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const alertClass = type === 'error' ? 'problem-section' : type === 'success' ? 'solution-section' : 'info-section';
            resultsDiv.innerHTML += `<div class="${alertClass}"><p>${message}</p></div>`;
        }

        function testWrongCSV() {
            document.getElementById('results').innerHTML = '';
            showResults('🔍 إنشاء CSV بالطريقة الخاطئة (فاصلة)...', 'info');
            
            // Create CSV with comma separator (WRONG)
            let csvContent = '\uFEFF';
            csvContent += 'رقم الفاتورة,اسم العميل,المدينة,المبلغ,الملاحظات\n';
            
            testData.forEach(item => {
                const row = [
                    `"${item.invoiceNumber}"`,
                    `"${item.customerName}"`,
                    `"${item.city}"`,
                    `${item.amount}`,
                    `"${item.notes}"`
                ];
                csvContent += row.join(',') + '\n'; // Using comma
            });

            downloadCSV(csvContent, 'test-wrong-comma.csv');
            showResults('❌ تم إنشاء ملف CSV بالفاصلة - ستظهر البيانات في عمود واحد!', 'error');
            showResults('📄 افتح الملف: test-wrong-comma.csv', 'info');
        }

        function testCorrectCSV() {
            document.getElementById('results').innerHTML = '';
            showResults('🔍 إنشاء CSV بالطريقة الصحيحة (فاصل منقوطة)...', 'info');
            
            // Create CSV with semicolon separator (CORRECT)
            let csvContent = '\uFEFF';
            csvContent += 'رقم الفاتورة;اسم العميل;المدينة;المبلغ;الملاحظات\n';
            
            testData.forEach(item => {
                // Clean data from semicolons
                const cleanName = item.customerName.replace(/;/g, ',');
                const cleanCity = item.city.replace(/;/g, ',');
                const cleanNotes = item.notes.replace(/;/g, ',');
                
                const row = [
                    `"${item.invoiceNumber}"`,
                    `"${cleanName}"`,
                    `"${cleanCity}"`,
                    `${item.amount}`,
                    `"${cleanNotes}"`
                ];
                csvContent += row.join(';') + '\n'; // Using semicolon
            });

            downloadCSV(csvContent, 'test-correct-semicolon.csv');
            showResults('✅ تم إنشاء ملف CSV بالفاصل المنقوطة - ستظهر البيانات في أعمدة منفصلة!', 'success');
            showResults('📄 افتح الملف: test-correct-semicolon.csv', 'info');
        }

        function testBothMethods() {
            testWrongCSV();
            setTimeout(() => {
                testCorrectCSV();
                showResults('🔍 تم إنشاء ملفين للمقارنة - افتحهما في Excel وقارن النتائج!', 'info');
            }, 1000);
        }

        function downloadCSV(content, filename) {
            const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);

            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            showResults('🔍 صفحة تحليل مشكلة CSV جاهزة', 'info');
            showResults('📋 اختبر الطريقتين وقارن النتائج في Excel', 'info');
        });
    </script>
</body>
</html>
