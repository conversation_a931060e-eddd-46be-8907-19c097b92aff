{"app": {"name": "نظام إدارة ديون العملاء", "version": "1.0.0", "description": "نظام شامل لإدارة ديون العملاء مع واجهة عربية", "author": "تم التطوير بواسطة Augment Agent", "license": "Open Source"}, "server": {"host": "0.0.0.0", "port": 5000, "debug": true}, "database": {"type": "json", "file": "data/customers.json", "backup_enabled": true, "backup_interval": "daily"}, "features": {"customer_history": true, "search": true, "statistics": true, "export_import": true, "duplicate_check": true}, "ui": {"language": "ar", "direction": "rtl", "theme": "bootstrap", "responsive": true}, "validation": {"required_fields": ["invoiceNumber", "customerName", "city", "amount"], "amount_min": 0.01, "invoice_unique": true}}