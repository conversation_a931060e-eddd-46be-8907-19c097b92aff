#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for Customer Debt Management System
سكريبت اختبار نظام إدارة ديون العملاء
"""

import requests
import json
import time
from datetime import datetime

class SystemTester:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.test_data = [
            {
                "invoiceNumber": "INV-001",
                "customerName": "أحمد محمد",
                "city": "الرياض",
                "amount": 1500.50,
                "notes": "دفعة أولى"
            },
            {
                "invoiceNumber": "INV-002", 
                "customerName": "فاطمة علي",
                "city": "جدة",
                "amount": 2300.75,
                "notes": "مستحقات شهر يناير"
            },
            {
                "invoiceNumber": "INV-003",
                "customerName": "أحمد محمد",
                "city": "الرياض", 
                "amount": 800.00,
                "notes": "دفعة ثانية"
            }
        ]
    
    def test_server_connection(self):
        """Test server connection"""
        try:
            response = requests.get(f"{self.base_url}/api/statistics", timeout=5)
            if response.status_code == 200:
                print("✅ الخادم متصل ويعمل بشكل صحيح")
                return True
            else:
                print(f"❌ خطأ في الاتصال: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ فشل الاتصال بالخادم: {e}")
            return False
    
    def test_add_debts(self):
        """Test adding debt records"""
        print("\n🧪 اختبار إضافة السجلات...")
        success_count = 0
        
        for i, debt in enumerate(self.test_data, 1):
            try:
                response = requests.post(
                    f"{self.base_url}/api/debts",
                    json=debt,
                    headers={'Content-Type': 'application/json'},
                    timeout=5
                )
                
                if response.status_code == 200:
                    print(f"✅ تم إضافة السجل {i}: {debt['customerName']}")
                    success_count += 1
                else:
                    print(f"❌ فشل إضافة السجل {i}: {response.text}")
                    
            except requests.exceptions.RequestException as e:
                print(f"❌ خطأ في إضافة السجل {i}: {e}")
        
        print(f"📊 تم إضافة {success_count} من {len(self.test_data)} سجلات")
        return success_count == len(self.test_data)
    
    def test_get_all_debts(self):
        """Test retrieving all debts"""
        print("\n🧪 اختبار استرجاع جميع السجلات...")
        try:
            response = requests.get(f"{self.base_url}/api/debts", timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    records = data['data']
                    print(f"✅ تم استرجاع {len(records)} سجل")
                    return True
                else:
                    print(f"❌ خطأ في البيانات: {data.get('error', 'Unknown error')}")
                    return False
            else:
                print(f"❌ خطأ في الاستجابة: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ خطأ في الطلب: {e}")
            return False
    
    def test_customer_history(self):
        """Test customer history retrieval"""
        print("\n🧪 اختبار سجل العميل...")
        try:
            # Test for "أحمد محمد" in "الرياض"
            response = requests.get(
                f"{self.base_url}/api/debts/customer/أحمد محمد/الرياض",
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    records = data['data']
                    total_amount = data['total_amount']
                    print(f"✅ سجل العميل: {len(records)} فاتورة، إجمالي: {total_amount}")
                    return True
                else:
                    print(f"❌ خطأ في البيانات: {data.get('error', 'Unknown error')}")
                    return False
            else:
                print(f"❌ خطأ في الاستجابة: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ خطأ في الطلب: {e}")
            return False
    
    def test_search(self):
        """Test search functionality"""
        print("\n🧪 اختبار البحث...")
        search_terms = ["أحمد", "الرياض", "1500", "INV-001"]
        
        for term in search_terms:
            try:
                response = requests.get(
                    f"{self.base_url}/api/debts/search",
                    params={'q': term},
                    timeout=5
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data['success']:
                        results = data['data']
                        print(f"✅ البحث عن '{term}': {len(results)} نتيجة")
                    else:
                        print(f"❌ خطأ في البحث عن '{term}': {data.get('error', 'Unknown error')}")
                        return False
                else:
                    print(f"❌ خطأ في البحث عن '{term}': {response.status_code}")
                    return False
            except requests.exceptions.RequestException as e:
                print(f"❌ خطأ في البحث عن '{term}': {e}")
                return False
        
        return True
    
    def test_statistics(self):
        """Test statistics endpoint"""
        print("\n🧪 اختبار الإحصائيات...")
        try:
            response = requests.get(f"{self.base_url}/api/statistics", timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data['success']:
                    stats = data['data']
                    print(f"✅ الإحصائيات:")
                    print(f"   📊 إجمالي المبالغ: {stats['total_amount']}")
                    print(f"   👥 عدد العملاء: {stats['total_customers']}")
                    print(f"   📄 عدد الفواتير: {stats['total_invoices']}")
                    return True
                else:
                    print(f"❌ خطأ في الإحصائيات: {data.get('error', 'Unknown error')}")
                    return False
            else:
                print(f"❌ خطأ في الاستجابة: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ خطأ في الطلب: {e}")
            return False
    
    def run_all_tests(self):
        """Run all tests"""
        print("🚀 بدء اختبار النظام...")
        print("=" * 50)
        
        tests = [
            ("اختبار الاتصال", self.test_server_connection),
            ("اختبار إضافة السجلات", self.test_add_debts),
            ("اختبار استرجاع السجلات", self.test_get_all_debts),
            ("اختبار سجل العميل", self.test_customer_history),
            ("اختبار البحث", self.test_search),
            ("اختبار الإحصائيات", self.test_statistics)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n🔍 {test_name}...")
            if test_func():
                passed += 1
            time.sleep(1)  # Small delay between tests
        
        print("\n" + "=" * 50)
        print(f"📋 نتائج الاختبار: {passed}/{total} نجح")
        
        if passed == total:
            print("🎉 جميع الاختبارات نجحت! النظام يعمل بشكل مثالي")
        else:
            print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        
        return passed == total

if __name__ == "__main__":
    print("🧪 سكريبت اختبار نظام إدارة ديون العملاء")
    print("=" * 50)
    
    # Wait for server to be ready
    print("⏳ انتظار تشغيل الخادم...")
    time.sleep(2)
    
    tester = SystemTester()
    success = tester.run_all_tests()
    
    if success:
        exit(0)
    else:
        exit(1)
