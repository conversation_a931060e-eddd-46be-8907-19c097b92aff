@echo off
chcp 65001 >nul
title Customer Debt Management System
color 0B

echo Starting Customer Debt Management System...
echo.

cd /d "%~dp0python"

echo Installing requirements...
pip install -r requirements.txt --quiet

echo.
echo Starting Flask server...
echo Server will be available at: http://localhost:5000
echo Press Ctrl+C to stop the server
echo.

python app.py

echo.
echo Server stopped.
pause
