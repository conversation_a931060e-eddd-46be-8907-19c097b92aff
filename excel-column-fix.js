// حل جذري لمشكلة فصل الأعمدة في Excel
// هذا الملف يحتوي على وظيفة محسنة تضمن فصل البيانات في أعمدة منفصلة

function createExcelWithSeparateColumns(data, fileName) {
    console.log('🔧 بدء إنشاء Excel مع فصل الأعمدة الجذري...');
    
    try {
        // التحقق من توفر مكتبة XLSX
        if (typeof XLSX === 'undefined') {
            throw new Error('مكتبة XLSX غير متوفرة');
        }

        if (!data || data.length === 0) {
            throw new Error('لا توجد بيانات للتصدير');
        }

        console.log(`📊 معالجة ${data.length} سجل...`);

        // إنشاء workbook جديد
        const workbook = XLSX.utils.book_new();
        
        // إنشاء worksheet فارغ تماماً
        const worksheet = {};
        
        // تحديد نطاق البيانات
        const maxRow = data.length; // عدد صفوف البيانات
        const maxCol = 4; // 5 أعمدة (0-4)
        
        // إنشاء headers يدوياً - صف 0
        console.log('📋 إنشاء headers...');
        worksheet['A1'] = { v: 'رقم الفاتورة', t: 's' };
        worksheet['B1'] = { v: 'اسم العميل', t: 's' };
        worksheet['C1'] = { v: 'المدينة', t: 's' };
        worksheet['D1'] = { v: 'المبلغ', t: 's' };
        worksheet['E1'] = { v: 'الملاحظات', t: 's' };
        
        console.log('✅ Headers تم إنشاؤها:', {
            A1: worksheet['A1'].v,
            B1: worksheet['B1'].v,
            C1: worksheet['C1'].v,
            D1: worksheet['D1'].v,
            E1: worksheet['E1'].v
        });

        // إضافة البيانات صف بصف وعمود بعمود
        console.log('📊 إضافة البيانات...');
        data.forEach((item, index) => {
            const rowNum = index + 2; // +2 لأن الصف الأول headers والفهرسة تبدأ من 1
            
            // العمود A: رقم الفاتورة
            const cellA = `A${rowNum}`;
            worksheet[cellA] = {
                v: String(item.invoiceNumber || '').trim(),
                t: 's'
            };
            
            // العمود B: اسم العميل
            const cellB = `B${rowNum}`;
            worksheet[cellB] = {
                v: String(item.customerName || '').trim(),
                t: 's'
            };
            
            // العمود C: المدينة
            const cellC = `C${rowNum}`;
            worksheet[cellC] = {
                v: String(item.city || '').trim(),
                t: 's'
            };
            
            // العمود D: المبلغ
            const cellD = `D${rowNum}`;
            const amount = parseFloat(item.amount) || 0;
            worksheet[cellD] = {
                v: amount,
                t: 'n',
                z: '#,##0.00'
            };
            
            // العمود E: الملاحظات
            const cellE = `E${rowNum}`;
            worksheet[cellE] = {
                v: String(item.notes || '').trim(),
                t: 's'
            };
            
            // طباعة أول 3 صفوف للتحقق
            if (index < 3) {
                console.log(`صف ${rowNum}:`, {
                    [cellA]: worksheet[cellA].v,
                    [cellB]: worksheet[cellB].v,
                    [cellC]: worksheet[cellC].v,
                    [cellD]: worksheet[cellD].v,
                    [cellE]: worksheet[cellE].v
                });
            }
        });

        // تحديد نطاق الورقة
        const range = `A1:E${data.length + 1}`;
        worksheet['!ref'] = range;
        
        console.log(`📐 نطاق الورقة: ${range}`);

        // تحديد عرض الأعمدة
        worksheet['!cols'] = [
            { wch: 15 }, // A: رقم الفاتورة
            { wch: 25 }, // B: اسم العميل
            { wch: 15 }, // C: المدينة
            { wch: 12 }, // D: المبلغ
            { wch: 30 }  // E: الملاحظات
        ];

        // إضافة تنسيق للـ headers
        ['A1', 'B1', 'C1', 'D1', 'E1'].forEach(cell => {
            if (worksheet[cell]) {
                worksheet[cell].s = {
                    font: { bold: true, color: { rgb: "000000" } },
                    fill: { fgColor: { rgb: "CCCCCC" } },
                    alignment: { horizontal: "center", vertical: "center" },
                    border: {
                        top: { style: "thin", color: { rgb: "000000" } },
                        bottom: { style: "thin", color: { rgb: "000000" } },
                        left: { style: "thin", color: { rgb: "000000" } },
                        right: { style: "thin", color: { rgb: "000000" } }
                    }
                };
            }
        });

        // إضافة الورقة إلى المصنف
        XLSX.utils.book_append_sheet(workbook, worksheet, 'بيانات الفواتير');
        
        console.log('✅ تم إنشاء الورقة وإضافتها للمصنف');

        // إنشاء اسم الملف
        const currentDate = new Date().toISOString().split('T')[0];
        const fullFileName = `${fileName}_${currentDate}.xlsx`;

        // حفظ الملف
        XLSX.writeFile(workbook, fullFileName, {
            bookType: 'xlsx',
            type: 'binary',
            cellStyles: true
        });

        console.log(`✅ تم حفظ الملف: ${fullFileName}`);
        
        // إرجاع معلومات النجاح
        return {
            success: true,
            fileName: fullFileName,
            recordsCount: data.length,
            message: `تم تصدير ${data.length} سجل بنجاح مع فصل كامل للأعمدة`
        };

    } catch (error) {
        console.error('❌ خطأ في إنشاء Excel:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// وظيفة مساعدة للتحقق من بنية البيانات
function validateDataStructure(data) {
    if (!Array.isArray(data)) {
        throw new Error('البيانات يجب أن تكون مصفوفة');
    }
    
    if (data.length === 0) {
        throw new Error('المصفوفة فارغة');
    }
    
    // التحقق من وجود الحقول المطلوبة
    const requiredFields = ['invoiceNumber', 'customerName', 'city', 'amount'];
    const firstItem = data[0];
    
    for (const field of requiredFields) {
        if (!(field in firstItem)) {
            console.warn(`⚠️ الحقل ${field} غير موجود في البيانات`);
        }
    }
    
    console.log('✅ بنية البيانات صحيحة');
    return true;
}

// تصدير الوظائف للاستخدام العام
if (typeof window !== 'undefined') {
    window.createExcelWithSeparateColumns = createExcelWithSeparateColumns;
    window.validateDataStructure = validateDataStructure;
    console.log('✅ وظائف Excel المحسنة متوفرة عالمياً');
}

// وظيفة اختبار سريعة
function testExcelColumnSeparation() {
    const testData = [
        {
            invoiceNumber: '001',
            customerName: 'أحمد محمد علي',
            city: 'الرياض',
            amount: 1500.50,
            notes: 'ملاحظة تجريبية طويلة'
        },
        {
            invoiceNumber: '002',
            customerName: 'فاطمة سالم',
            city: 'جدة',
            amount: 2000,
            notes: 'ملاحظة أخرى'
        },
        {
            invoiceNumber: '003',
            customerName: 'محمد عبدالله',
            city: 'الدمام',
            amount: 1750.75,
            notes: 'ملاحظة ثالثة'
        }
    ];
    
    console.log('🧪 بدء اختبار فصل الأعمدة...');
    const result = createExcelWithSeparateColumns(testData, 'اختبار_فصل_الأعمدة');
    
    if (result.success) {
        console.log('✅ نجح الاختبار!', result);
        alert(`✅ نجح الاختبار!\n\nتم إنشاء: ${result.fileName}\nعدد السجلات: ${result.recordsCount}\n\n🔍 تحقق من الملف - يجب أن تكون البيانات في أعمدة منفصلة تماماً`);
    } else {
        console.error('❌ فشل الاختبار:', result.error);
        alert(`❌ فشل الاختبار: ${result.error}`);
    }
}

if (typeof window !== 'undefined') {
    window.testExcelColumnSeparation = testExcelColumnSeparation;
}
