@echo off
chcp 65001 >nul
title تثبيت نظام إدارة ديون العملاء - Windows

echo.
echo ========================================
echo    تثبيت نظام إدارة ديون العملاء
echo    Windows Desktop Application
echo ========================================
echo.

:: التحقق من صلاحيات المدير
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ تحذير: يُنصح بتشغيل هذا الملف كمدير للحصول على أفضل النتائج
    echo.
    timeout /t 3 >nul
)

:: التحقق من وجود Node.js
echo 🔍 فحص Node.js...
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo.
    echo 📥 تحميل وتثبيت Node.js...
    echo يرجى تحميل Node.js من: https://nodejs.org/
    echo.
    echo بعد التثبيت، أعد تشغيل هذا الملف
    pause
    start https://nodejs.org/
    exit /b 1
) else (
    echo ✅ Node.js مثبت - الإصدار: 
    node --version
    echo.
)

:: التحقق من وجود ملفات المشروع
if not exist "package.json" (
    echo ❌ خطأ: ملفات المشروع غير موجودة
    echo تأكد من وجود ملف package.json في نفس المجلد
    pause
    exit /b 1
)

echo ✅ ملفات المشروع موجودة
echo.

:: عرض خيارات التثبيت
echo 🚀 اختر طريقة التثبيت:
echo.
echo 1. تشغيل مباشر (بدون تثبيت)
echo 2. بناء مثبت Windows (.exe)
echo 3. بناء نسخة محمولة
echo 4. تثبيت المتطلبات فقط
echo 0. خروج
echo.

set /p choice="اختر رقم (0-4): "

if "%choice%"=="1" goto run_direct
if "%choice%"=="2" goto build_installer
if "%choice%"=="3" goto build_portable
if "%choice%"=="4" goto install_deps
if "%choice%"=="0" goto exit
goto invalid_choice

:install_deps
echo.
echo 📦 تثبيت المتطلبات...
echo هذا قد يستغرق بضع دقائق...
echo.

npm install
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت المتطلبات
    echo.
    echo 🔧 محاولة حل المشكلة...
    echo حذف node_modules...
    if exist "node_modules" rmdir /s /q node_modules
    if exist "package-lock.json" del package-lock.json
    echo.
    echo إعادة المحاولة...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل مرة أخرى. تحقق من اتصال الإنترنت
        pause
        exit /b 1
    )
)

echo ✅ تم تثبيت المتطلبات بنجاح
echo.
goto menu

:run_direct
echo.
echo 🚀 تشغيل التطبيق مباشرة...
echo.

:: تثبيت المتطلبات إذا لم تكن مثبتة
if not exist "node_modules" (
    echo 📦 تثبيت المتطلبات أولاً...
    call :install_deps
)

echo تشغيل التطبيق...
npm start
goto end

:build_installer
echo.
echo 🏗️ بناء مثبت Windows...
echo هذا قد يستغرق عدة دقائق...
echo.

:: تثبيت المتطلبات إذا لم تكن مثبتة
if not exist "node_modules" (
    echo 📦 تثبيت المتطلبات أولاً...
    call :install_deps
)

:: التحقق من وجود الأيقونات
if not exist "build\icon.ico" (
    echo ⚠️ تحذير: أيقونة Windows غير موجودة
    echo إنشاء أيقونة افتراضية...
    mkdir build 2>nul
    echo. > build\icon.ico
)

echo بناء المثبت...
npm run build-win
if %errorlevel% equ 0 (
    echo.
    echo ✅ تم بناء المثبت بنجاح!
    echo.
    if exist "dist" (
        echo 📁 الملفات المُنتجة:
        dir /b dist\*.exe 2>nul
        echo.
        echo 📍 مكان الملفات: %CD%\dist\
        echo.
        set /p open="هل تريد فتح مجلد الملفات؟ (y/n): "
        if /i "!open!"=="y" explorer dist
    )
) else (
    echo ❌ فشل في بناء المثبت
    echo.
    echo 🔧 نصائح لحل المشكلة:
    echo 1. تأكد من اتصال الإنترنت
    echo 2. أعد تشغيل الكمبيوتر وحاول مرة أخرى
    echo 3. تأكد من وجود مساحة كافية على القرص الصلب
)
goto end

:build_portable
echo.
echo 📱 بناء نسخة محمولة...
echo.

:: تثبيت المتطلبات إذا لم تكن مثبتة
if not exist "node_modules" (
    echo 📦 تثبيت المتطلبات أولاً...
    call :install_deps
)

echo بناء النسخة المحمولة...
npm run pack
if %errorlevel% equ 0 (
    echo ✅ تم بناء النسخة المحمولة بنجاح!
    echo 📁 الملفات متوفرة في: dist\win-unpacked\
    echo.
    echo يمكنك نسخ مجلد win-unpacked إلى أي مكان وتشغيل التطبيق منه
) else (
    echo ❌ فشل في بناء النسخة المحمولة
)
goto end

:invalid_choice
echo.
echo ❌ اختيار غير صحيح
goto menu

:menu
echo.
echo اضغط أي زر للعودة للقائمة الرئيسية...
pause >nul
cls
goto start

:exit
echo.
echo 👋 شكراً لاستخدام نظام إدارة ديون العملاء
exit /b 0

:end
echo.
echo 🎯 انتهت العملية
echo.
pause

:start
goto install_deps
