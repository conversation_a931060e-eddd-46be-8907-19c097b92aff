<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار استيراد CSV</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
        }
        .btn {
            background: #7c3aed;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #6d28d9;
        }
        .log {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { color: #10b981; }
        .error { color: #ef4444; }
        .info { color: #3b82f6; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار استيراد CSV</h1>

        <div class="test-section">
            <h3>📁 اختبار الملف التجريبي</h3>
            <p>استخدم الملف: <strong>test-import.csv</strong></p>
            <input type="file" id="testFile" accept=".csv" style="margin: 10px 0;">
            <br>
            <button class="btn" onclick="testImport('new')">استيراد للفواتير الجديدة</button>
            <button class="btn" onclick="testImport('previous')">استيراد للديون السابقة</button>
        </div>

        <div class="test-section">
            <h3>📊 حالة النظام</h3>
            <button class="btn" onclick="checkSystem()">فحص النظام</button>
            <div id="systemStatus" class="log"></div>
        </div>

        <div class="test-section">
            <h3>📝 سجل الأحداث</h3>
            <button class="btn" onclick="clearLog()">مسح السجل</button>
            <div id="eventLog" class="log"></div>
        </div>
    </div>

    <script>
        // Mock debtManager for testing
        window.debtManager = {
            debts: [],
            previousDebts: [],
            isDuplicateInvoice: function(invoiceNumber) {
                return false; // No duplicates for testing
            },
            saveData: function() {
                log('💾 Data saved to localStorage', 'success');
            },
            savePreviousDebts: function() {
                log('💾 Previous debts saved to localStorage', 'success');
            },
            updateStatistics: function() {
                log('📊 Statistics updated', 'info');
            },
            displayRecords: function() {
                log('🖥️ Records displayed', 'info');
            },
            displayPreviousDebt: function() {
                log('🖥️ Previous debts displayed', 'info');
            },
            showSuccess: function(message) {
                log('✅ ' + message, 'success');
            },
            showError: function(message) {
                log('❌ ' + message, 'error');
            }
        };

        function log(message, type = 'info') {
            const logDiv = document.getElementById('eventLog');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('eventLog').innerHTML = '';
        }

        function checkSystem() {
            const status = document.getElementById('systemStatus');
            status.innerHTML = '';

            const checks = [
                { name: 'debtManager', obj: window.debtManager },
                { name: 'importFromFile', obj: window.importFromFile },
                { name: 'exportToCSV', obj: window.exportToCSV },
                { name: 'exportToExcel', obj: window.exportToExcel }
            ];

            checks.forEach(check => {
                const available = typeof check.obj !== 'undefined';
                const className = available ? 'success' : 'error';
                const icon = available ? '✅' : '❌';
                status.innerHTML += `<div class="${className}">${icon} ${check.name}: ${available ? 'متوفر' : 'غير متوفر'}</div>`;
            });
        }

        function testImport(type) {
            const fileInput = document.getElementById('testFile');
            const file = fileInput.files[0];

            if (!file) {
                log('❌ يرجى اختيار ملف أولاً', 'error');
                return;
            }

            log(`🚀 بدء اختبار الاستيراد للنوع: ${type}`, 'info');

            // Create mock event
            const mockEvent = {
                target: {
                    files: [file],
                    value: ''
                }
            };

            // Call import function
            try {
                if (typeof window.importFromFile === 'function') {
                    window.importFromFile(mockEvent, type);
                    log('✅ تم استدعاء دالة الاستيراد', 'success');
                } else {
                    log('❌ دالة importFromFile غير متوفرة', 'error');
                }
            } catch (error) {
                log('❌ خطأ في الاستيراد: ' + error.message, 'error');
            }
        }

        // Load the fix functions
        window.addEventListener('load', function() {
            log('🔄 تحميل الصفحة...', 'info');
            setTimeout(checkSystem, 1000);
        });
    </script>

    <!-- Load the actual functions -->
    <script src="/javascript/fix-functions.js"></script>
</body>
</html>
