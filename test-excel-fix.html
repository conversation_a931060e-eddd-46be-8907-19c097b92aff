<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح Excel</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 28px;
        }
        .test-btn {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            width: 100%;
        }
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            direction: ltr;
            text-align: left;
        }
        .success { background: #d4edda; color: #155724; border-color: #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border-color: #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border-color: #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار إصلاح Excel</h1>

        <div class="info log">
            <h3>📋 هدف الاختبار:</h3>
            <p>التأكد من أن البيانات تظهر في أعمدة منفصلة:</p>
            <ul>
                <li>العمود A: رقم الفاتورة</li>
                <li>العمود B: اسم العميل</li>
                <li>العمود C: المدينة</li>
                <li>العمود D: المبلغ</li>
                <li>العمود E: الملاحظات</li>
            </ul>
        </div>

        <button class="test-btn" onclick="testExcelExport()">
            📊 اختبار تصدير Excel
        </button>

        <button class="test-btn" onclick="testCSVExport()">
            📄 اختبار تصدير CSV
        </button>

        <button class="test-btn" onclick="openMainApp()">
            🚀 فتح التطبيق الرئيسي للاختبار
        </button>

        <div id="log" class="log" style="display: none;"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            logDiv.style.display = 'block';
            logDiv.className = `log ${type}`;
            logDiv.innerHTML += `<div>[${new Date().toLocaleTimeString()}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            const logDiv = document.getElementById('log');
            logDiv.innerHTML = '';
        }

        // Test data
        const testData = [
            {
                invoiceNumber: '1',
                customerName: 'أحمد محمد',
                city: 'الرياض',
                amount: 1500,
                notes: 'ملاحظة تجريبية'
            },
            {
                invoiceNumber: '2',
                customerName: 'فاطمة علي',
                city: 'جدة',
                amount: 2000,
                notes: 'ملاحظة أخرى'
            },
            {
                invoiceNumber: '3',
                customerName: 'محمد سالم',
                city: 'الدمام',
                amount: 1750,
                notes: 'ملاحظة ثالثة'
            }
        ];

        function testExcelExport() {
            clearLog();
            log('🚀 بدء اختبار تصدير Excel...', 'info');

            try {
                // Check XLSX library
                if (typeof XLSX === 'undefined') {
                    log('❌ مكتبة XLSX غير متوفرة', 'error');
                    return;
                }
                log('✅ مكتبة XLSX متوفرة', 'success');

                // MANUAL CELL APPROACH - Create worksheet cell by cell
                log('🔧 إنشاء Excel بطريقة الخلايا اليدوية...', 'info');

                const ws = {};
                const range = { s: { c: 0, r: 0 }, e: { c: 4, r: testData.length } };

                // Add headers manually
                const headers = ['رقم الفاتورة', 'اسم العميل', 'المدينة', 'المبلغ', 'الملاحظات'];
                headers.forEach((header, colIndex) => {
                    const cellRef = XLSX.utils.encode_cell({ r: 0, c: colIndex });
                    ws[cellRef] = {
                        v: header,
                        t: 's',
                        s: { font: { bold: true }, fill: { fgColor: { rgb: "CCCCCC" } } }
                    };
                    log(`Header ${colIndex}: ${cellRef} = "${header}"`, 'info');
                });

                // Add data rows manually
                testData.forEach((item, rowIndex) => {
                    const dataRow = rowIndex + 1;

                    // Column A: رقم الفاتورة
                    const invoiceCell = XLSX.utils.encode_cell({ r: dataRow, c: 0 });
                    ws[invoiceCell] = { v: String(item.invoiceNumber || '').trim(), t: 's' };

                    // Column B: اسم العميل
                    const nameCell = XLSX.utils.encode_cell({ r: dataRow, c: 1 });
                    ws[nameCell] = { v: String(item.customerName || '').trim(), t: 's' };

                    // Column C: المدينة
                    const cityCell = XLSX.utils.encode_cell({ r: dataRow, c: 2 });
                    ws[cityCell] = { v: String(item.city || '').trim(), t: 's' };

                    // Column D: المبلغ
                    const amountCell = XLSX.utils.encode_cell({ r: dataRow, c: 3 });
                    ws[amountCell] = { v: parseFloat(item.amount) || 0, t: 'n', z: '#,##0.00' };

                    // Column E: الملاحظات
                    const notesCell = XLSX.utils.encode_cell({ r: dataRow, c: 4 });
                    ws[notesCell] = { v: String(item.notes || '').trim(), t: 's' };

                    log(`Row ${dataRow}: A="${ws[invoiceCell].v}" B="${ws[nameCell].v}" C="${ws[cityCell].v}" D="${ws[amountCell].v}" E="${ws[notesCell].v}"`, 'info');
                });

                // Set worksheet range
                ws['!ref'] = XLSX.utils.encode_range(range);

                log(`📊 Worksheet range: ${ws['!ref']}`, 'info');

                // Create workbook
                const wb = XLSX.utils.book_new();

                log('✅ تم إنشاء worksheet بنجاح', 'success');

                // Set column widths
                ws['!cols'] = [
                    { wch: 15 }, // A: رقم الفاتورة
                    { wch: 25 }, // B: اسم العميل
                    { wch: 15 }, // C: المدينة
                    { wch: 12 }, // D: المبلغ
                    { wch: 30 }  // E: الملاحظات
                ];

                log('✅ تم تعيين عرض الأعمدة', 'success');

                // Add worksheet to workbook
                XLSX.utils.book_append_sheet(wb, ws, 'بيانات الاختبار');

                log('✅ تم إضافة الورقة إلى المصنف', 'success');

                // Generate filename
                const fileName = `test-excel-fix-${new Date().toISOString().split('T')[0]}.xlsx`;

                // Save file
                XLSX.writeFile(wb, fileName);

                log(`✅ تم حفظ الملف: ${fileName}`, 'success');
                log('🎉 اختبار Excel مكتمل بطريقة الخلايا اليدوية!', 'success');
                log('📋 تحقق من الملف المحفوظ - يجب أن تكون البيانات في أعمدة منفصلة', 'success');
                log('🔍 A=رقم الفاتورة، B=اسم العميل، C=المدينة، D=المبلغ، E=الملاحظات', 'success');

            } catch (error) {
                log(`❌ خطأ في اختبار Excel: ${error.message}`, 'error');
                console.error('Excel test error:', error);
            }
        }

        function testCSVExport() {
            clearLog();
            log('🚀 بدء اختبار تصدير CSV...', 'info');

            try {
                // Create CSV content
                let csvContent = '\uFEFF'; // UTF-8 BOM
                csvContent += 'رقم الفاتورة,اسم العميل,المدينة,المبلغ,الملاحظات\n';

                log('📋 إضافة headers إلى CSV', 'info');

                // Add data rows
                testData.forEach((item, index) => {
                    const invoiceNumber = String(item.invoiceNumber || '').trim().replace(/"/g, '""');
                    const customerName = String(item.customerName || '').trim().replace(/"/g, '""');
                    const city = String(item.city || '').trim().replace(/"/g, '""');
                    const amount = parseFloat(item.amount) || 0;
                    const notes = String(item.notes || '').trim().replace(/"/g, '""');

                    const row = [
                        `"${invoiceNumber}"`,
                        `"${customerName}"`,
                        `"${city}"`,
                        `${amount}`,
                        `"${notes}"`
                    ];

                    csvContent += row.join(',') + '\n';
                    log(`Row ${index + 1}: ${row.join(',')}`, 'info');
                });

                // Create and download file
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);

                link.setAttribute('href', url);
                link.setAttribute('download', `test-csv-fix-${new Date().toISOString().split('T')[0]}.csv`);
                link.style.visibility = 'hidden';

                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                log('✅ تم حفظ ملف CSV بنجاح', 'success');
                log('🎉 اختبار CSV مكتمل! تحقق من الملف المحفوظ', 'success');

            } catch (error) {
                log(`❌ خطأ في اختبار CSV: ${error.message}`, 'error');
                console.error('CSV test error:', error);
            }
        }

        function openMainApp() {
            window.open('http://localhost:5000', '_blank');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('🔧 صفحة اختبار Excel جاهزة', 'info');
            log('📋 تعليمات الاختبار:', 'info');
            log('1. اختبر تصدير Excel هنا أولاً', 'info');
            log('2. ثم افتح التطبيق الرئيسي', 'info');
            log('3. أضف بيانات عربية واختبر التصدير', 'info');
            log('4. تحقق من فصل الأعمدة في الملفات', 'info');
        });
    </script>
</body>
</html>
