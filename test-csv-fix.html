<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح CSV - فصل الأعمدة</title>
    <style>
        body {
            font-family: '<PERSON><PERSON>e UI', Tahoma, Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 28px;
        }
        .alert {
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            font-size: 16px;
        }
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 2px solid #bee5eb;
        }
        .test-btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            width: calc(50% - 20px);
            display: inline-block;
        }
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        .test-btn.primary {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            width: calc(100% - 20px);
        }
        .test-btn.primary:hover {
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            direction: ltr;
            text-align: left;
        }
        .success { background: #d4edda; color: #155724; border-color: #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border-color: #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border-color: #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border-color: #ffeaa7; }
        .csv-demo {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            direction: ltr;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار إصلاح CSV - فصل الأعمدة</h1>
        
        <div class="alert alert-danger">
            <h3>❌ المشكلة الحالية في CSV:</h3>
            <p><strong>البيانات تظهر في عمود واحد عند فتح CSV في Excel</strong></p>
            <p>السبب: استخدام الفاصلة (,) كفاصل أعمدة لا يعمل بشكل صحيح مع Excel العربي</p>
        </div>

        <div class="alert alert-success">
            <h3>✅ الحل المطبق:</h3>
            <p><strong>استخدام الفاصل المنقوطة (;) بدلاً من الفاصلة (,)</strong></p>
            <ul>
                <li>الفاصل المنقوطة (;) هو المعيار في Excel للمناطق العربية</li>
                <li>يضمن فصل صحيح للأعمدة</li>
                <li>متوافق مع النصوص العربية</li>
                <li>يعمل مع جميع إصدارات Excel</li>
            </ul>
        </div>

        <div class="alert alert-info">
            <h3>🔍 مقارنة الطرق:</h3>
            
            <h4>❌ الطريقة القديمة (فاصلة):</h4>
            <div class="csv-demo">
رقم الفاتورة,اسم العميل,المدينة,المبلغ,الملاحظات<br>
"001","أحمد محمد","الرياض",1500,"ملاحظة تجريبية"<br>
<span style="color: red;">النتيجة: كل البيانات في عمود واحد</span>
            </div>

            <h4>✅ الطريقة الجديدة (فاصل منقوطة):</h4>
            <div class="csv-demo">
رقم الفاتورة;اسم العميل;المدينة;المبلغ;الملاحظات<br>
"001";"أحمد محمد";"الرياض";1500;"ملاحظة تجريبية"<br>
<span style="color: green;">النتيجة: كل بيان في عمود منفصل</span>
            </div>
        </div>

        <button class="test-btn primary" onclick="testCSVWithSemicolon()">
            🚀 اختبار CSV مع الفاصل المنقوطة
        </button>

        <button class="test-btn" onclick="testCSVWithComma()">
            📄 اختبار CSV مع الفاصلة (للمقارنة)
        </button>

        <button class="test-btn" onclick="openMainApp()">
            🌐 فتح التطبيق الرئيسي
        </button>

        <div id="log" class="log" style="display: none;"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            logDiv.style.display = 'block';
            logDiv.className = `log ${type}`;
            logDiv.innerHTML += `<div>[${new Date().toLocaleTimeString()}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            const logDiv = document.getElementById('log');
            logDiv.innerHTML = '';
            logDiv.style.display = 'none';
        }

        // Test data
        const testData = [
            {
                invoiceNumber: '001',
                customerName: 'أحمد محمد علي',
                city: 'الرياض',
                amount: 1500.75,
                notes: 'ملاحظة تجريبية طويلة'
            },
            {
                invoiceNumber: '002',
                customerName: 'فاطمة سالم عبدالله',
                city: 'جدة',
                amount: 2000.50,
                notes: 'ملاحظة أخرى مع تفاصيل'
            },
            {
                invoiceNumber: '003',
                customerName: 'محمد عبدالله الزهراني',
                city: 'الدمام',
                amount: 1750.25,
                notes: 'ملاحظة ثالثة مع معلومات'
            }
        ];

        function testCSVWithSemicolon() {
            clearLog();
            log('🚀 بدء اختبار CSV مع الفاصل المنقوطة...', 'info');
            
            try {
                // Create CSV content with semicolon separator
                let csvContent = '\uFEFF'; // UTF-8 BOM
                csvContent += 'رقم الفاتورة;اسم العميل;المدينة;المبلغ;الملاحظات\n';
                
                log('📋 إضافة headers مع الفاصل المنقوطة (;)', 'info');

                // Add data rows
                testData.forEach((item, index) => {
                    const invoiceNumber = String(item.invoiceNumber || '').trim().replace(/;/g, ',').replace(/"/g, '""');
                    const customerName = String(item.customerName || '').trim().replace(/;/g, ',').replace(/"/g, '""');
                    const city = String(item.city || '').trim().replace(/;/g, ',').replace(/"/g, '""');
                    const amount = parseFloat(item.amount) || 0;
                    const notes = String(item.notes || '').trim().replace(/;/g, ',').replace(/"/g, '""');
                    
                    const row = [
                        `"${invoiceNumber}"`,
                        `"${customerName}"`,
                        `"${city}"`,
                        `${amount}`,
                        `"${notes}"`
                    ];
                    
                    const csvRow = row.join(';'); // Using semicolon
                    csvContent += csvRow + '\n';
                    log(`Row ${index + 1}: ${csvRow}`, 'info');
                });

                // Create and download file
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);

                link.setAttribute('href', url);
                link.setAttribute('download', `test-csv-semicolon-${new Date().toISOString().split('T')[0]}.csv`);
                link.style.visibility = 'hidden';

                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                log('✅ تم حفظ ملف CSV مع الفاصل المنقوطة', 'success');
                log('🔍 افتح الملف في Excel وتحقق من فصل الأعمدة', 'warning');
                log('📊 يجب أن تكون البيانات في أعمدة منفصلة', 'success');

            } catch (error) {
                log(`❌ خطأ في اختبار CSV: ${error.message}`, 'error');
                console.error('CSV test error:', error);
            }
        }

        function testCSVWithComma() {
            clearLog();
            log('📄 بدء اختبار CSV مع الفاصلة (للمقارنة)...', 'info');
            
            try {
                // Create CSV content with comma separator
                let csvContent = '\uFEFF'; // UTF-8 BOM
                csvContent += 'رقم الفاتورة,اسم العميل,المدينة,المبلغ,الملاحظات\n';
                
                log('📋 إضافة headers مع الفاصلة (,)', 'info');

                // Add data rows
                testData.forEach((item, index) => {
                    const invoiceNumber = String(item.invoiceNumber || '').trim().replace(/"/g, '""');
                    const customerName = String(item.customerName || '').trim().replace(/"/g, '""');
                    const city = String(item.city || '').trim().replace(/"/g, '""');
                    const amount = parseFloat(item.amount) || 0;
                    const notes = String(item.notes || '').trim().replace(/"/g, '""');
                    
                    const row = [
                        `"${invoiceNumber}"`,
                        `"${customerName}"`,
                        `"${city}"`,
                        `${amount}`,
                        `"${notes}"`
                    ];
                    
                    const csvRow = row.join(','); // Using comma
                    csvContent += csvRow + '\n';
                    log(`Row ${index + 1}: ${csvRow}`, 'info');
                });

                // Create and download file
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);

                link.setAttribute('href', url);
                link.setAttribute('download', `test-csv-comma-${new Date().toISOString().split('T')[0]}.csv`);
                link.style.visibility = 'hidden';

                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                log('✅ تم حفظ ملف CSV مع الفاصلة', 'success');
                log('⚠️ هذا الملف قد يظهر البيانات في عمود واحد', 'warning');
                log('🔍 قارن مع ملف الفاصل المنقوطة', 'info');

            } catch (error) {
                log(`❌ خطأ في اختبار CSV: ${error.message}`, 'error');
                console.error('CSV test error:', error);
            }
        }

        function openMainApp() {
            window.open('http://localhost:5000', '_blank');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('🔧 صفحة اختبار CSV جاهزة', 'info');
            log('📋 سيتم إنشاء ملفين للمقارنة:', 'info');
            log('1. ملف مع الفاصل المنقوطة (;) - يجب أن يعمل', 'info');
            log('2. ملف مع الفاصلة (,) - قد لا يعمل', 'info');
        });
    </script>
</body>
</html>
