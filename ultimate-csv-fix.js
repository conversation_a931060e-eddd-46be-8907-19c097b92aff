// الحل الشامل والنهائي لمشكلة CSV - فصل الأعمدة
// يحل جميع المشاكل: الفاصل، الترميز، تنظيف البيانات

console.log('🔧 تحميل الحل الشامل لمشكلة CSV...');

/**
 * إنشاء CSV مع ضمان فصل الأعمدة - الحل النهائي
 * @param {Array} data - البيانات للتصدير
 * @param {string} fileName - اسم الملف
 * @returns {Object} نتيجة العملية
 */
function createPerfectCSV(data, fileName) {
    console.log('🚀 بدء الحل الشامل لإنشاء CSV مع فصل مضمون للأعمدة...');

    try {
        if (!data || data.length === 0) {
            throw new Error('لا توجد بيانات للتصدير');
        }

        console.log(`📊 معالجة ${data.length} سجل...`);

        // الخطوة 1: إنشاء محتوى CSV مع UTF-8 BOM
        let csvContent = '\uFEFF'; // UTF-8 BOM للنصوص العربية
        console.log('✅ تم إضافة UTF-8 BOM');

        // الخطوة 2: إضافة headers مع الفاصل المنقوطة
        const headers = ['رقم الفاتورة', 'اسم العميل', 'المدينة', 'المبلغ', 'الملاحظات'];
        csvContent += headers.join(';') + '\n';
        console.log('✅ تم إضافة headers مع الفاصل المنقوطة (;)');

        // الخطوة 3: معالجة البيانات مع تنظيف شامل
        data.forEach((item, index) => {
            // تنظيف شامل للبيانات
            const cleanedData = {
                invoiceNumber: cleanCSVField(item.invoiceNumber || ''),
                customerName: cleanCSVField(item.customerName || ''),
                city: cleanCSVField(item.city || ''),
                amount: parseFloat(item.amount) || 0,
                notes: cleanCSVField(item.notes || '')
            };

            // إنشاء صف CSV مع ضمان الفصل
            const csvRow = [
                `"${cleanedData.invoiceNumber}"`,      // العمود A
                `"${cleanedData.customerName}"`,       // العمود B
                `"${cleanedData.city}"`,               // العمود C
                `${cleanedData.amount}`,               // العمود D (رقم بدون تنصيص)
                `"${cleanedData.notes}"`               // العمود E
            ];

            // ربط الأعمدة بالفاصل المنقوطة
            const finalRow = csvRow.join(';');
            csvContent += finalRow + '\n';

            // طباعة أول 3 صفوف للتحقق
            if (index < 3) {
                console.log(`✅ صف ${index + 1}: ${finalRow}`);
                console.log(`   العمود A: "${cleanedData.invoiceNumber}"`);
                console.log(`   العمود B: "${cleanedData.customerName}"`);
                console.log(`   العمود C: "${cleanedData.city}"`);
                console.log(`   العمود D: ${cleanedData.amount}`);
                console.log(`   العمود E: "${cleanedData.notes}"`);
            }
        });

        console.log(`📊 تم إنشاء ${data.length} صف مع فصل مضمون للأعمدة`);

        // الخطوة 4: حفظ الملف
        const currentDate = new Date().toISOString().split('T')[0];
        const fullFileName = `${fileName}_فصل_مضمون_${currentDate}.csv`;

        const blob = new Blob([csvContent], {
            type: 'text/csv;charset=utf-8;'
        });

        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        link.setAttribute('href', url);
        link.setAttribute('download', fullFileName);
        link.style.visibility = 'hidden';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        console.log(`✅ تم حفظ الملف: ${fullFileName}`);

        return {
            success: true,
            fileName: fullFileName,
            recordsCount: data.length,
            message: `تم إنشاء CSV مع فصل مضمون للأعمدة باستخدام الفاصل المنقوطة (;)`
        };

    } catch (error) {
        console.error('❌ خطأ في إنشاء CSV:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * تنظيف حقل CSV من الرموز المشكلة
 * @param {string} field - الحقل للتنظيف
 * @returns {string} الحقل المنظف
 */
function cleanCSVField(field) {
    if (!field) return '';

    return String(field)
        .trim()                    // إزالة المسافات الزائدة
        .replace(/;/g, ',')        // استبدال الفاصل المنقوطة بفاصلة
        .replace(/"/g, '""')       // مضاعفة علامات التنصيص
        .replace(/\r?\n/g, ' ')    // استبدال أسطر جديدة بمسافات
        .replace(/\t/g, ' ');      // استبدال التابات بمسافات
}

/**
 * اختبار الحل الشامل مع بيانات تجريبية
 */
function testPerfectCSV() {
    console.log('🧪 بدء اختبار الحل الشامل لـ CSV...');

    const testData = [
        {
            invoiceNumber: '001',
            customerName: 'أحمد محمد علي الأحمدي',
            city: 'الرياض',
            amount: 1500.75,
            notes: 'ملاحظة تجريبية مع نص طويل ومعلومات مفصلة'
        },
        {
            invoiceNumber: '002',
            customerName: 'فاطمة سالم عبدالله الزهراني',
            city: 'جدة',
            amount: 2000.50,
            notes: 'ملاحظة أخرى مع تفاصيل إضافية ومعلومات شاملة'
        },
        {
            invoiceNumber: '003',
            customerName: 'محمد عبدالله القحطاني',
            city: 'الدمام',
            amount: 1750.25,
            notes: 'ملاحظة ثالثة مع بيانات مفصلة وشاملة'
        },
        {
            invoiceNumber: '004',
            customerName: 'نورا أحمد السعيد',
            city: 'مكة المكرمة',
            amount: 3000.00,
            notes: 'ملاحظة رابعة مع معلومات تفصيلية كاملة'
        }
    ];

    const result = createPerfectCSV(testData, 'اختبار_CSV_شامل');

    if (result.success) {
        console.log('✅ نجح الاختبار الشامل!', result);
        alert(`✅ نجح الاختبار الشامل!\n\n📁 الملف: ${result.fileName}\n📊 عدد السجلات: ${result.recordsCount}\n\n🔍 افتح الملف في Excel وتحقق من:\n• كل بيان في عمود منفصل\n• النصوص العربية تظهر بشكل صحيح\n• الأرقام في العمود D كأرقام\n• لا توجد بيانات مختلطة\n\n🎯 إذا ظهرت البيانات في عمود واحد، فالمشكلة في إعدادات Excel وليس في الكود!`);
    } else {
        console.error('❌ فشل الاختبار:', result.error);
        alert(`❌ فشل الاختبار: ${result.error}`);
    }
}

/**
 * استبدال وظيفة CSV الحالية بالحل الشامل
 */
function replacePerfectCSVFunction(type) {
    console.log('🔄 استبدال وظيفة CSV بالحل الشامل...');

    try {
        if (typeof window.debtManager === 'undefined') {
            alert('نظام إدارة الديون غير جاهز. يرجى إعادة تحميل الصفحة.');
            return;
        }

        // الحصول على البيانات
        let data = [];
        let fileName = '';

        if (type === 'previous') {
            data = debtManager.previousDebts || [];
            fileName = 'الديون_السابقة';
        } else {
            data = debtManager.debts || [];
            fileName = 'الفواتير_الجديدة';
        }

        if (data.length === 0) {
            alert('لا توجد بيانات للتصدير');
            return;
        }

        console.log(`📊 تصدير ${data.length} سجل باستخدام الحل الشامل...`);

        // استخدام الحل الشامل
        const result = createPerfectCSV(data, fileName);

        if (result.success) {
            if (typeof debtManager.showSuccess === 'function') {
                debtManager.showSuccess(`تم تصدير ${result.recordsCount} سجل بنجاح إلى ملف CSV مع فصل الأعمدة`);
            }
        } else {
            if (typeof debtManager.showError === 'function') {
                debtManager.showError(`حدث خطأ: ${result.error}`);
            }
        }

    } catch (error) {
        console.error('❌ خطأ في استبدال وظيفة CSV:', error);
        alert(`حدث خطأ: ${error.message}`);
    }
}

// تصدير الوظائف للاستخدام العام
if (typeof window !== 'undefined') {
    window.createPerfectCSV = createPerfectCSV;
    window.cleanCSVField = cleanCSVField;
    window.testPerfectCSV = testPerfectCSV;
    window.replacePerfectCSVFunction = replacePerfectCSVFunction;
    console.log('✅ وظائف الحل الشامل لـ CSV متوفرة عالمياً');
}

console.log('🔧 تم تحميل الحل الشامل والنهائي لمشكلة CSV');
