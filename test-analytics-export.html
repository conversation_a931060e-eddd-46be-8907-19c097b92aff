<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تصدير التحليلات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-btn:hover {
            background: #0056b3;
        }
        .excel-btn {
            background: #28a745;
        }
        .pdf-btn {
            background: #dc3545;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #007bff; }
    </style>

    <!-- Libraries -->
    <script src="https://unpkg.com/jspdf@latest/dist/jspdf.umd.min.js"></script>
    <script src="https://unpkg.com/jspdf-autotable@latest/dist/jspdf.plugin.autotable.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار تصدير التحليلات</h1>

        <div class="test-section">
            <h3>📊 اختبار العملاء الجدد</h3>
            <button class="test-btn excel-btn" onclick="testExport('newCustomers')">📗 تصدير Excel</button>
            <button class="test-btn pdf-btn" onclick="testPDF('newCustomers')">📄 طباعة PDF</button>
        </div>

        <div class="test-section">
            <h3>📊 اختبار العملاء غير النشطين</h3>
            <button class="test-btn excel-btn" onclick="testExport('inactiveCustomers')">📗 تصدير Excel</button>
            <button class="test-btn pdf-btn" onclick="testPDF('inactiveCustomers')">📄 طباعة PDF</button>
        </div>

        <div class="test-section">
            <h3>📊 اختبار المقارنات</h3>
            <button class="test-btn excel-btn" onclick="testExport('comparisons')">📗 تصدير Excel</button>
            <button class="test-btn pdf-btn" onclick="testPDF('comparisons')">📄 طباعة PDF</button>
        </div>

        <div class="test-section">
            <h3>📊 اختبار الإحصائيات</h3>
            <button class="test-btn excel-btn" onclick="testExport('statistics')">📗 تصدير Excel</button>
            <button class="test-btn pdf-btn" onclick="testPDF('statistics')">📄 طباعة PDF</button>
        </div>

        <div class="test-section">
            <h3>📊 اختبار الفروقات</h3>
            <button class="test-btn excel-btn" onclick="testExport('differences')">📗 تصدير Excel</button>
            <button class="test-btn pdf-btn" onclick="testPDF('differences')">📄 طباعة PDF</button>
        </div>

        <div class="test-section">
            <h3>📋 سجل الاختبارات</h3>
            <div id="log" class="log"></div>
            <button class="test-btn" onclick="clearLog()">🗑️ مسح السجل</button>
        </div>
    </div>

    <script>
        // محاكاة debtManager مع بيانات تجريبية
        window.debtManager = {
            debts: [
                {
                    id: 1,
                    invoiceNumber: 'INV-001',
                    customerName: 'أحمد محمد',
                    city: 'الرياض',
                    amount: 1500,
                    notes: 'ملاحظة تجريبية',
                    date: '15/12/2024'
                },
                {
                    id: 2,
                    invoiceNumber: 'INV-002',
                    customerName: 'فاطمة علي',
                    city: 'جدة',
                    amount: 2000,
                    notes: 'ملاحظة أخرى',
                    date: '16/12/2024'
                },
                {
                    id: 3,
                    invoiceNumber: 'INV-003',
                    customerName: 'محمد أحمد',
                    city: 'الدمام',
                    amount: 1200,
                    notes: 'عميل جديد',
                    date: '17/12/2024'
                }
            ],
            previousDebts: [
                {
                    id: 4,
                    invoiceNumber: 'OLD-001',
                    customerName: 'أحمد محمد',
                    city: 'الرياض',
                    amount: 800,
                    notes: 'دين سابق',
                    date: '10/12/2024'
                },
                {
                    id: 5,
                    invoiceNumber: 'OLD-002',
                    customerName: 'سعد علي',
                    city: 'مكة',
                    amount: 1000,
                    notes: 'دين قديم',
                    date: '08/12/2024'
                }
            ],
            currentSearchTerm: '',
            filteredDebts: [],
            filteredPreviousDebts: [],
            showSuccess: function(message) {
                log('✅ نجح: ' + message, 'success');
            },
            showError: function(message) {
                log('❌ خطأ: ' + message, 'error');
            },

            // إضافة الوظائف المطلوبة
            getUniqueCustomers: function(debts) {
                const customers = {};
                debts.forEach(debt => {
                    const key = `${debt.customerName}_${debt.city}`;
                    if (!customers[key]) {
                        customers[key] = {
                            name: debt.customerName,
                            city: debt.city,
                            totalAmount: 0,
                            invoices: 0
                        };
                    }
                    customers[key].totalAmount += debt.amount;
                    customers[key].invoices += 1;
                });
                return Object.values(customers);
            },

            createExcelFile: function(data, headers, filename) {
                try {
                    log('📊 إنشاء ملف Excel...', 'info');

                    // Create workbook
                    const wb = XLSX.utils.book_new();

                    // Prepare data with headers
                    const wsData = [headers, ...data];

                    // Create worksheet
                    const ws = XLSX.utils.aoa_to_sheet(wsData);

                    // Set column widths
                    const colWidths = headers.map(() => ({ wch: 20 }));
                    ws['!cols'] = colWidths;

                    // Add worksheet to workbook
                    XLSX.utils.book_append_sheet(wb, ws, 'البيانات');

                    // Save file
                    const excelFilename = `${filename}_${new Date().toISOString().split('T')[0]}.xlsx`;
                    XLSX.writeFile(wb, excelFilename);

                    log(`✅ تم إنشاء ملف Excel: ${excelFilename}`, 'success');
                } catch (error) {
                    log('❌ خطأ في إنشاء ملف Excel: ' + error.message, 'error');
                }
            },

            createAnalyticsPDF: function(data, headers, title) {
                try {
                    log('🖨️ إنشاء PDF بالنص العربي الأصلي...', 'info');

                    // Create HTML table for conversion to PDF
                    const htmlContent = this.createHTMLTableForPDF(data, headers, title);

                    // Use html2canvas to convert HTML to image, then to PDF
                    this.convertHTMLToPDF(htmlContent, title);

                } catch (error) {
                    log('❌ خطأ في إنشاء ملف PDF: ' + error.message, 'error');
                }
            },

            // Create HTML table for PDF conversion
            createHTMLTableForPDF: function(data, headers, title) {
                const currentDate = new Date().toLocaleDateString('ar-SA-u-ca-gregory');

                let html = `
                    <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: rtl; text-align: right; padding: 20px; background: white;">
                        <div style="text-align: center; margin-bottom: 30px;">
                            <h1 style="color: #2c3e50; font-size: 24px; margin-bottom: 10px;">${title}</h1>
                            <p style="color: #7f8c8d; font-size: 14px;">تاريخ التقرير: ${currentDate}</p>
                        </div>

                        <table style="width: 100%; border-collapse: collapse; font-size: 12px; margin: 20px 0;">
                            <thead>
                                <tr style="background: linear-gradient(135deg, #3498db, #2980b9); color: white;">
                `;

                // Add headers
                headers.forEach(header => {
                    html += `<th style="padding: 12px 8px; border: 1px solid #bdc3c7; text-align: center; font-weight: bold;">${header}</th>`;
                });

                html += `
                                </tr>
                            </thead>
                            <tbody>
                `;

                // Add data rows
                data.forEach((row, index) => {
                    const bgColor = index % 2 === 0 ? '#f8f9fa' : '#ffffff';
                    html += `<tr style="background: ${bgColor};">`;

                    row.forEach(cell => {
                        html += `<td style="padding: 10px 8px; border: 1px solid #bdc3c7; text-align: center;">${cell}</td>`;
                    });

                    html += `</tr>`;
                });

                html += `
                            </tbody>
                        </table>

                        <div style="text-align: center; margin-top: 30px; color: #7f8c8d; font-size: 12px;">
                            <p>تم إنشاء هذا التقرير بواسطة نظام إدارة ديون العملاء</p>
                        </div>
                    </div>
                `;

                return html;
            },

            // Convert HTML to PDF using html2canvas
            convertHTMLToPDF: function(htmlContent, title) {
                // Create temporary div
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = htmlContent;
                tempDiv.style.position = 'absolute';
                tempDiv.style.left = '-9999px';
                tempDiv.style.top = '-9999px';
                tempDiv.style.width = '800px';
                tempDiv.style.background = 'white';
                document.body.appendChild(tempDiv);

                // Convert to canvas then PDF
                html2canvas(tempDiv, {
                    scale: 2,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff',
                    width: 800,
                    height: tempDiv.scrollHeight
                }).then(canvas => {
                    // Remove temporary div
                    document.body.removeChild(tempDiv);

                    // Create PDF
                    const { jsPDF } = window.jspdf;
                    const pdf = new jsPDF('p', 'mm', 'a4');

                    const imgWidth = 210; // A4 width in mm
                    const pageHeight = 295; // A4 height in mm
                    const imgHeight = (canvas.height * imgWidth) / canvas.width;
                    let heightLeft = imgHeight;
                    let position = 0;

                    // Add first page
                    pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, position, imgWidth, imgHeight);
                    heightLeft -= pageHeight;

                    // Add additional pages if needed
                    while (heightLeft >= 0) {
                        position = heightLeft - imgHeight;
                        pdf.addPage();
                        pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, position, imgWidth, imgHeight);
                        heightLeft -= pageHeight;
                    }

                    // Save PDF
                    const filename = `${title}_${new Date().toISOString().split('T')[0]}.pdf`;
                    pdf.save(filename);

                    log(`✅ تم إنشاء PDF بالعربية: ${filename}`, 'success');

                }).catch(error => {
                    // Remove temporary div if error occurs
                    if (document.body.contains(tempDiv)) {
                        document.body.removeChild(tempDiv);
                    }
                    log('❌ خطأ في تحويل HTML إلى PDF: ' + error.message, 'error');
                });
            }
        };

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function testExport(section) {
            log(`🔄 اختبار تصدير ${section} إلى Excel...`, 'info');

            try {
                // محاكاة استدعاء الوظيفة
                if (typeof window.debtManager.exportAnalyticsToExcel === 'function') {
                    window.debtManager.exportAnalyticsToExcel(section);
                } else {
                    // محاكاة الوظيفة
                    mockExportAnalyticsToExcel(section);
                }
            } catch (error) {
                log('❌ خطأ في التصدير: ' + error.message, 'error');
            }
        }

        function testPDF(section) {
            log(`🔄 اختبار طباعة ${section} إلى PDF...`, 'info');

            try {
                // محاكاة استدعاء الوظيفة
                if (typeof window.debtManager.printAnalyticsPDF === 'function') {
                    window.debtManager.printAnalyticsPDF(section);
                } else {
                    // محاكاة الوظيفة
                    mockPrintAnalyticsPDF(section);
                }
            } catch (error) {
                log('❌ خطأ في الطباعة: ' + error.message, 'error');
            }
        }

        function mockExportAnalyticsToExcel(section) {
            let data = [];
            let filename = '';
            let headers = [];

            switch (section) {
                case 'newCustomers':
                    headers = ['اسم العميل', 'المدينة', 'عدد الفواتير', 'إجمالي المبلغ'];
                    data = [
                        ['محمد أحمد', 'الدمام', 1, 1200],
                        ['فاطمة علي', 'جدة', 1, 2000]
                    ];
                    filename = 'العملاء_الجدد';
                    break;
                case 'inactiveCustomers':
                    headers = ['اسم العميل', 'المدينة', 'عدد الفواتير', 'إجمالي المبلغ'];
                    data = [
                        ['سعد علي', 'مكة', 1, 1000]
                    ];
                    filename = 'العملاء_غير_النشطين';
                    break;
                case 'comparisons':
                    headers = ['البيان', 'الفواتير الجديدة', 'الديون السابقة', 'الفرق'];
                    data = [
                        ['عدد الفواتير', 3, 2, 1],
                        ['إجمالي المبلغ', 4700, 1800, 2900]
                    ];
                    filename = 'مقارنة_البيانات';
                    break;
                case 'statistics':
                    headers = ['الترتيب', 'اسم العميل', 'المدينة', 'عدد الفواتير', 'إجمالي المبلغ'];
                    data = [
                        [1, 'أحمد محمد', 'الرياض', 2, 2300],
                        [2, 'فاطمة علي', 'جدة', 1, 2000],
                        [3, 'محمد أحمد', 'الدمام', 1, 1200]
                    ];
                    filename = 'الإحصائيات_المتقدمة';
                    break;
                case 'differences':
                    headers = ['اسم العميل', 'المدينة', 'المبلغ السابق', 'المبلغ الجديد', 'الفرق'];
                    data = [
                        ['أحمد محمد', 'الرياض', 800, 1500, 700]
                    ];
                    filename = 'الفروقات';
                    break;
            }

            if (data.length === 0) {
                log('❌ لا توجد بيانات للتصدير', 'error');
                return;
            }

            window.debtManager.createExcelFile(data, headers, filename);
            log(`✅ تم تصدير ${filename} إلى Excel بنجاح`, 'success');
        }

        function mockPrintAnalyticsPDF(section) {
            let title = '';
            let data = [];
            let headers = [];

            switch (section) {
                case 'newCustomers':
                    title = 'تقرير العملاء الجدد';
                    headers = ['اسم العميل', 'المدينة', 'عدد الفواتير', 'إجمالي المبلغ'];
                    data = [
                        ['محمد أحمد', 'الدمام', 1, '1,200 ر.س'],
                        ['فاطمة علي', 'جدة', 1, '2,000 ر.س']
                    ];
                    break;
                case 'inactiveCustomers':
                    title = 'تقرير العملاء غير النشطين';
                    headers = ['اسم العميل', 'المدينة', 'عدد الفواتير', 'إجمالي المبلغ'];
                    data = [
                        ['سعد علي', 'مكة', 1, '1,000 ر.س']
                    ];
                    break;
                case 'comparisons':
                    title = 'تقرير مقارنة البيانات';
                    headers = ['البيان', 'الفواتير الجديدة', 'الديون السابقة', 'الفرق'];
                    data = [
                        ['عدد الفواتير', 3, 2, '+1'],
                        ['إجمالي المبلغ', '4,700 ر.س', '1,800 ر.س', '+2,900 ر.س']
                    ];
                    break;
                case 'statistics':
                    title = 'تقرير الإحصائيات المتقدمة';
                    headers = ['الترتيب', 'اسم العميل', 'المدينة', 'عدد الفواتير', 'إجمالي المبلغ'];
                    data = [
                        [1, 'أحمد محمد', 'الرياض', 2, '2,300 ر.س'],
                        [2, 'فاطمة علي', 'جدة', 1, '2,000 ر.س'],
                        [3, 'محمد أحمد', 'الدمام', 1, '1,200 ر.س']
                    ];
                    break;
                case 'differences':
                    title = 'تقرير الفروقات';
                    headers = ['اسم العميل', 'المدينة', 'المبلغ السابق', 'المبلغ الجديد', 'الفرق'];
                    data = [
                        ['أحمد محمد', 'الرياض', '800 ر.س', '1,500 ر.س', '+700 ر.س']
                    ];
                    break;
            }

            if (data.length === 0) {
                log('❌ لا توجد بيانات للطباعة', 'error');
                return;
            }

            window.debtManager.createAnalyticsPDF(data, headers, title);
            log(`✅ تم إنشاء ${title} بنجاح`, 'success');
        }

        // تسجيل بداية الاختبار
        log('🚀 تم تحميل صفحة اختبار التصدير', 'success');
        log('📊 البيانات التجريبية جاهزة', 'info');
    </script>
</body>
</html>
