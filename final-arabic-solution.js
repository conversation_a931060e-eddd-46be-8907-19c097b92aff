// 🔥 الحل النهائي الجذري - تجاوز مشكلة jsPDF تماماً
// استخدام window.print() مع HTML محسن للطباعة

console.log('🔥 تحميل الحل النهائي الجذري للترميز العربي...');

// وظيفة إنشاء صفحة طباعة HTML مع النصوص الأصلية
function createPrintableHTML(data, title, type = 'report') {
    console.log('📄 إنشاء صفحة طباعة HTML مع النصوص الأصلية...');
    
    try {
        // حساب الإجمالي
        let totalAmount = 0;
        data.forEach(item => {
            totalAmount += parseFloat(item.amount) || 0;
        });
        
        // التاريخ الحالي
        const currentDate = new Date().toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
        });
        
        // إنشاء HTML للطباعة
        const printHTML = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        @media print {
            @page {
                size: A4;
                margin: 15mm;
            }
            body {
                margin: 0;
                padding: 0;
                font-family: Arial, "Segoe UI", Tahoma, sans-serif;
                font-size: 12pt;
                line-height: 1.4;
                color: #000;
                background: #fff;
                direction: rtl;
                text-align: right;
            }
            .no-print {
                display: none !important;
            }
        }
        
        body {
            font-family: Arial, "Segoe UI", Tahoma, sans-serif;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
            background: #fff;
            color: #000;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 20px;
        }
        
        .title {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .date {
            font-size: 16px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .report-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 14px;
        }
        
        .report-table th {
            background: #2c3e50;
            color: #fff;
            padding: 15px 10px;
            text-align: center;
            font-weight: bold;
            border: 2px solid #34495e;
            font-size: 16px;
        }
        
        .report-table td {
            padding: 12px 10px;
            text-align: center;
            border: 1px solid #ddd;
            vertical-align: middle;
        }
        
        .report-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .report-table tr:nth-child(odd) {
            background: #fff;
        }
        
        .total-row {
            background: #2c3e50 !important;
            color: #fff !important;
            font-weight: bold;
            font-size: 18px;
        }
        
        .total-row td {
            padding: 20px 10px;
            border: 2px solid #34495e;
        }
        
        .print-button {
            background: #27ae60;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 8px;
            cursor: pointer;
            margin: 20px;
            font-weight: bold;
        }
        
        .print-button:hover {
            background: #2ecc71;
        }
        
        .save-pdf-button {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 8px;
            cursor: pointer;
            margin: 20px;
            font-weight: bold;
        }
        
        .save-pdf-button:hover {
            background: #c0392b;
        }
        
        .buttons-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .instructions {
            background: #d4edda;
            border: 2px solid #c3e6cb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            color: #155724;
        }
        
        .instructions h4 {
            margin-top: 0;
            color: #155724;
        }
        
        @media print {
            .buttons-container,
            .instructions {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="instructions no-print">
        <h4>📋 تعليمات الطباعة وحفظ PDF:</h4>
        <p><strong>للطباعة:</strong> اضغط زر "طباعة" أدناه أو Ctrl+P</p>
        <p><strong>لحفظ PDF:</strong> اضغط زر "حفظ PDF" أو اختر "حفظ كـ PDF" من خيارات الطباعة</p>
        <p><strong>ملاحظة:</strong> النصوص العربية ستظهر بوضوح تام في الطباعة والـ PDF</p>
    </div>
    
    <div class="buttons-container no-print">
        <button class="print-button" onclick="window.print()">🖨️ طباعة</button>
        <button class="save-pdf-button" onclick="saveToPDF()">💾 حفظ PDF</button>
        <button class="print-button" onclick="window.close()" style="background: #95a5a6;">❌ إغلاق</button>
    </div>
    
    <div class="header">
        <div class="title">${title}</div>
        <div class="date">${currentDate}</div>
    </div>
    
    <table class="report-table">
        <thead>
            <tr>
                <th>رقم الفاتورة</th>
                <th>اسم العميل</th>
                <th>المدينة</th>
                <th>المبلغ (ر.س)</th>
                <th>الملاحظات</th>
            </tr>
        </thead>
        <tbody>
`;

        // إضافة البيانات
        data.forEach((item, index) => {
            const amount = parseFloat(item.amount) || 0;
            printHTML += `
            <tr>
                <td>${item.invoiceNumber || ''}</td>
                <td>${item.customerName || ''}</td>
                <td>${item.city || ''}</td>
                <td>${amount.toLocaleString('ar-SA')}</td>
                <td>${item.notes || ''}</td>
            </tr>`;
        });

        // إضافة صف الإجمالي
        printHTML += `
        </tbody>
        <tfoot>
            <tr class="total-row">
                <td colspan="3"><strong>إجمالي المبلغ</strong></td>
                <td><strong>${totalAmount.toLocaleString('ar-SA')}</strong></td>
                <td><strong>ر.س</strong></td>
            </tr>
        </tfoot>
    </table>
    
    <script>
        function saveToPDF() {
            // فتح نافذة الطباعة مع تعيين الوجهة كـ PDF
            window.print();
            
            // إظهار تعليمات للمستخدم
            setTimeout(() => {
                alert('📋 لحفظ الملف كـ PDF:\\n\\n1. في نافذة الطباعة، اختر "حفظ كـ PDF" أو "Microsoft Print to PDF"\\n2. اختر مكان الحفظ\\n3. اضغط "حفظ"\\n\\n✅ النصوص العربية ستظهر بوضوح تام في الـ PDF');
            }, 500);
        }
        
        // طباعة تلقائية عند التحميل (اختياري)
        // window.onload = function() {
        //     setTimeout(() => {
        //         window.print();
        //     }, 1000);
        // };
    </script>
</body>
</html>`;

        return printHTML;
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء HTML للطباعة:', error);
        return null;
    }
}

// وظيفة فتح صفحة الطباعة
function openPrintPage(data, title, type = 'report') {
    console.log('🖨️ فتح صفحة الطباعة...');
    
    try {
        const printHTML = createPrintableHTML(data, title, type);
        
        if (!printHTML) {
            throw new Error('فشل في إنشاء HTML للطباعة');
        }
        
        // فتح نافذة جديدة
        const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
        
        if (!printWindow) {
            throw new Error('فشل في فتح نافذة الطباعة - تأكد من السماح للنوافذ المنبثقة');
        }
        
        // كتابة HTML في النافذة الجديدة
        printWindow.document.write(printHTML);
        printWindow.document.close();
        
        // التركيز على النافذة الجديدة
        printWindow.focus();
        
        console.log('✅ تم فتح صفحة الطباعة بنجاح');
        
        return {
            success: true,
            method: 'HTML Print Page',
            records: data.length,
            message: 'تم فتح صفحة الطباعة - النصوص العربية ستظهر بوضوح تام'
        };
        
    } catch (error) {
        console.error('❌ خطأ في فتح صفحة الطباعة:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// وظيفة إنشاء ملف HTML قابل للتحميل
function downloadHTMLFile(data, title, type = 'report') {
    console.log('💾 إنشاء ملف HTML قابل للتحميل...');
    
    try {
        const htmlContent = createPrintableHTML(data, title, type);
        
        if (!htmlContent) {
            throw new Error('فشل في إنشاء محتوى HTML');
        }
        
        // إنشاء Blob
        const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
        
        // إنشاء رابط التحميل
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        
        // اسم الملف
        const fileName = `${title.replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')}_${new Date().toISOString().split('T')[0]}.html`;
        link.download = fileName;
        
        // تحميل الملف
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // تنظيف الذاكرة
        URL.revokeObjectURL(url);
        
        console.log(`✅ تم تحميل ملف HTML: ${fileName}`);
        
        return {
            success: true,
            fileName: fileName,
            method: 'HTML Download',
            records: data.length
        };
        
    } catch (error) {
        console.error('❌ خطأ في تحميل ملف HTML:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// وظيفة اختبار شاملة
function testFinalArabicSolution() {
    console.log('🧪 اختبار الحل النهائي للترميز العربي...');
    
    const testData = [
        {
            invoiceNumber: '001',
            customerName: 'أحمد محمد العلي',
            city: 'الرياض',
            amount: 1500.75,
            notes: 'ملاحظة تجريبية عربية'
        },
        {
            invoiceNumber: '002',
            customerName: 'فاطمة عبدالله',
            city: 'جدة',
            amount: 2500.50,
            notes: 'معلومات إضافية مهمة'
        },
        {
            invoiceNumber: '003',
            customerName: 'محمد سالم',
            city: 'الدمام',
            amount: 3750.25,
            notes: 'تفاصيل شاملة للفاتورة'
        }
    ];
    
    // فتح صفحة الطباعة
    const result = openPrintPage(testData, 'تقرير اختبار الحل النهائي', 'test');
    
    if (result.success) {
        alert(`✅ نجح الحل النهائي!\n\nالطريقة: ${result.method}\nالسجلات: ${result.records}\n\n${result.message}\n\n🔍 في النافذة الجديدة:\n• اضغط "طباعة" للطباعة المباشرة\n• اضغط "حفظ PDF" لحفظ ملف PDF\n• النصوص العربية ستظهر بوضوح تام`);
    } else {
        alert(`❌ فشل الاختبار: ${result.error}`);
    }
    
    return result;
}

// تصدير الوظائف
if (typeof window !== 'undefined') {
    window.createPrintableHTML = createPrintableHTML;
    window.openPrintPage = openPrintPage;
    window.downloadHTMLFile = downloadHTMLFile;
    window.testFinalArabicSolution = testFinalArabicSolution;
    console.log('✅ الحل النهائي الجذري متوفر عالمياً');
}

console.log('🔥 تم تحميل الحل النهائي الجذري للترميز العربي بنجاح');
