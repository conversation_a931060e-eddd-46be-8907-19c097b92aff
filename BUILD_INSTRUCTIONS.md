# 🏗️ تعليمات بناء وتثبيت نظام إدارة ديون العملاء كبرنامج سطح مكتب

## 🎯 الهدف
تحويل التطبيق إلى برنامج سطح مكتب مستقل يعمل بدون متصفح ويمكن تثبيته على Windows, Mac, Linux.

---

## 📋 المتطلبات الأساسية

### 🔧 البرامج المطلوبة:
```bash
1. Node.js (الإصدار 16 أو أحدث)
2. npm أو yarn
3. Git (اختياري)
```

### 📥 تحميل Node.js:
- **Windows/Mac:** https://nodejs.org/
- **Linux:** `sudo apt install nodejs npm` أو `sudo yum install nodejs npm`

---

## 🚀 خطوات البناء

### الخطوة 1: تثبيت المتطلبات
```bash
# في مجلد المشروع
npm install
```

### الخطوة 2: تشغيل التطبيق في وضع التطوير
```bash
# تشغيل مباشر
npm start

# أو تشغيل مع أدوات المطور
npm run dev
```

### الخطوة 3: بناء التطبيق للتوزيع

#### 🖥️ بناء لجميع المنصات:
```bash
npm run build
```

#### 🪟 بناء لـ Windows فقط:
```bash
npm run build-win
```

#### 🍎 بناء لـ Mac فقط:
```bash
npm run build-mac
```

#### 🐧 بناء لـ Linux فقط:
```bash
npm run build-linux
```

---

## 📁 هيكل الملفات بعد البناء

```
📁 المشروع/
├── 📁 dist/                    # ملفات التوزيع
│   ├── 📄 نظام إدارة ديون العملاء-Setup-1.0.0.exe  # مثبت Windows
│   ├── 📄 نظام إدارة ديون العملاء-1.0.0.dmg         # مثبت Mac
│   ├── 📄 نظام إدارة ديون العملاء-1.0.0.AppImage     # تطبيق Linux
│   └── 📁 win-unpacked/        # ملفات Windows غير مضغوطة
├── 📁 build/                   # ملفات البناء
│   ├── 📄 icon.ico            # أيقونة Windows
│   ├── 📄 icon.icns           # أيقونة Mac
│   └── 📄 icon.png            # أيقونة Linux
└── 📁 electron/               # ملفات Electron
    ├── 📄 main.js             # الملف الرئيسي
    ├── 📄 preload.js          # ملف الأمان
    └── 📄 installer.nsh       # إعدادات المثبت
```

---

## 🎨 إنشاء الأيقونات

### 📏 الأحجام المطلوبة:
- **Windows:** icon.ico (16x16, 32x32, 48x48, 64x64, 128x128, 256x256)
- **Mac:** icon.icns (16x16 إلى 1024x1024)
- **Linux:** icon.png (512x512)

### 🛠️ أدوات إنشاء الأيقونات:
```bash
# تثبيت أداة تحويل الأيقونات
npm install -g electron-icon-maker

# إنشاء جميع الأيقونات من صورة واحدة
electron-icon-maker --input=icon-source.png --output=build/
```

### 🌐 أدوات أونلاين:
- **Icon Converter:** https://convertio.co/
- **ICO Convert:** https://icoconvert.com/
- **App Icon Generator:** https://appicon.co/

---

## 📦 أنواع الملفات المُنتجة

### 🪟 Windows:
```
📄 نظام إدارة ديون العملاء-Setup-1.0.0.exe    # مثبت كامل
📄 نظام إدارة ديون العملاء-1.0.0-x64.exe       # نسخة محمولة 64-bit
📄 نظام إدارة ديون العملاء-1.0.0-ia32.exe      # نسخة محمولة 32-bit
```

### 🍎 Mac:
```
📄 نظام إدارة ديون العملاء-1.0.0.dmg           # مثبت Mac
📄 نظام إدارة ديون العملاء-1.0.0-mac.zip       # أرشيف مضغوط
```

### 🐧 Linux:
```
📄 نظام إدارة ديون العملاء-1.0.0.AppImage       # تطبيق محمول
📄 نظام إدارة ديون العملاء-1.0.0.deb           # حزمة Debian/Ubuntu
```

---

## 🔧 تخصيص البناء

### تعديل إعدادات البناء في package.json:
```json
{
  "build": {
    "appId": "com.yourcompany.debtmanager",
    "productName": "اسم التطبيق المخصص",
    "directories": {
      "output": "dist"
    },
    "win": {
      "target": "nsis",
      "icon": "build/icon.ico"
    }
  }
}
```

### إضافة ملفات إضافية:
```json
{
  "build": {
    "extraResources": [
      {
        "from": "data",
        "to": "data"
      },
      {
        "from": "docs",
        "to": "docs"
      }
    ]
  }
}
```

---

## 🚀 التوزيع والتثبيت

### 📤 رفع الملفات:
```bash
# رفع إلى GitHub Releases
gh release create v1.0.0 dist/*.exe dist/*.dmg dist/*.AppImage

# أو رفع إلى خادم ويب
scp dist/* user@server:/path/to/downloads/
```

### 📱 تثبيت التطبيق:

#### 🪟 Windows:
```
1. تحميل ملف .exe
2. تشغيل المثبت كمدير
3. اتباع خطوات التثبيت
4. تشغيل من سطح المكتب أو قائمة ابدأ
```

#### 🍎 Mac:
```
1. تحميل ملف .dmg
2. فتح الملف
3. سحب التطبيق إلى مجلد Applications
4. تشغيل من Launchpad
```

#### 🐧 Linux:
```
1. تحميل ملف .AppImage
2. جعل الملف قابل للتنفيذ: chmod +x *.AppImage
3. تشغيل مباشر: ./نظام*.AppImage
```

---

## 🛠️ حل المشاكل الشائعة

### ❌ مشكلة: فشل في البناء
```bash
# حذف node_modules وإعادة التثبيت
rm -rf node_modules package-lock.json
npm install

# تحديث electron-builder
npm update electron-builder
```

### ❌ مشكلة: الأيقونة لا تظهر
```bash
# التأكد من وجود الأيقونات في مجلد build/
ls build/icon.*

# إعادة إنشاء الأيقونات
electron-icon-maker --input=source.png --output=build/
```

### ❌ مشكلة: التطبيق لا يبدأ
```bash
# تشغيل في وضع التطوير للتشخيص
npm run dev

# فحص ملف main.js
node electron/main.js
```

---

## 📋 قائمة التحقق النهائية

### ✅ قبل البناء:
- [ ] تثبيت Node.js و npm
- [ ] تثبيت المتطلبات: `npm install`
- [ ] إنشاء الأيقونات في مجلد build/
- [ ] اختبار التطبيق: `npm start`
- [ ] تحديث معلومات package.json

### ✅ بعد البناء:
- [ ] التحقق من وجود ملفات التوزيع في dist/
- [ ] اختبار المثبت على نظام نظيف
- [ ] التأكد من عمل التطبيق بدون إنترنت
- [ ] اختبار جميع الوظائف الأساسية

---

## 🎉 النتيجة النهائية

بعد اتباع هذه التعليمات ستحصل على:

### 🖥️ برنامج سطح مكتب مستقل:
- ✅ يعمل بدون متصفح
- ✅ يعمل بدون إنترنت
- ✅ مثبت احترافي
- ✅ أيقونة مخصصة
- ✅ قوائم نظام كاملة

### 📱 تجربة مستخدم محسنة:
- ✅ تشغيل سريع
- ✅ واجهة أصلية
- ✅ اختصارات لوحة مفاتيح
- ✅ تكامل مع النظام

**🎯 التطبيق جاهز للتوزيع كبرنامج سطح مكتب احترافي!**
