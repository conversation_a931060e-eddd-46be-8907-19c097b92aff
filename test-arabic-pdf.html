<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار PDF العربي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Libraries for PDF -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
    <!-- Arabic font support -->
    <script src="https://cdn.jsdelivr.net/npm/jspdf-arabic@1.0.0/dist/jspdf-arabic.min.js"></script>

    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .test-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-btn {
            margin: 10px;
            padding: 15px 25px;
            border-radius: 10px;
            border: none;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .btn-primary { background: #667eea; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-info { background: #17a2b8; color: white; }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            background: #f8f9fa;
            border-left: 5px solid #667eea;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">🧪 اختبار PDF العربي المحسن</h1>

        <div class="row">
            <div class="col-md-6">
                <h3>🔧 اختبارات أساسية</h3>
                <button class="test-btn btn-primary" onclick="testLibraries()">
                    <i class="fas fa-check-circle"></i> اختبار المكتبات
                </button>
                <button class="test-btn btn-success" onclick="testSimplePDF()">
                    <i class="fas fa-file-pdf"></i> PDF بسيط
                </button>
                <button class="test-btn btn-warning" onclick="testArabicPDF()">
                    <i class="fas fa-language"></i> PDF عربي
                </button>
            </div>

            <div class="col-md-6">
                <h3>📊 اختبارات متقدمة</h3>
                <button class="test-btn btn-info" onclick="testTablePDF()">
                    <i class="fas fa-table"></i> جدول عربي
                </button>
                <button class="test-btn btn-primary" onclick="testFullReport()">
                    <i class="fas fa-chart-bar"></i> تقرير كامل
                </button>
                <button class="test-btn btn-success" onclick="createSampleData()">
                    <i class="fas fa-database"></i> بيانات تجريبية
                </button>
            </div>
        </div>

        <div id="result" class="result" style="display: none;">
            <h4>📋 نتائج الاختبار:</h4>
            <div id="resultContent"></div>
        </div>
    </div>

    <script>
        // Test functions
        function showResult(message, type = 'info') {
            const result = document.getElementById('result');
            const content = document.getElementById('resultContent');

            const colors = {
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8'
            };

            result.style.display = 'block';
            result.style.borderLeftColor = colors[type] || colors.info;
            content.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
        }

        function testLibraries() {
            let results = [];

            // Test jsPDF
            if (window.jspdf) {
                results.push('✅ jsPDF: متوفر');
            } else {
                results.push('❌ jsPDF: غير متوفر');
            }

            // Test autoTable
            if (window.jspdf && window.jspdf.jsPDF.prototype.autoTable) {
                results.push('✅ autoTable: متوفر');
            } else {
                results.push('❌ autoTable: غير متوفر');
            }

            // Test Arabic support
            if (window.arabicFontData) {
                results.push('✅ Arabic Font: متوفر');
            } else {
                results.push('⚠️ Arabic Font: غير متوفر (سيتم استخدام البديل)');
            }

            showResult(results.join('<br>'), 'info');
        }

        function testSimplePDF() {
            try {
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF();

                doc.setFontSize(16);
                doc.text('Test PDF - English', 20, 20);
                doc.text('Date: ' + new Date().toLocaleDateString(), 20, 40);

                doc.save('test-simple.pdf');
                showResult('✅ تم إنشاء PDF بسيط بنجاح!', 'success');
            } catch (error) {
                showResult('❌ خطأ في إنشاء PDF البسيط: ' + error.message, 'error');
            }
        }

        function testArabicPDF() {
            try {
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF();

                doc.setFontSize(16);
                doc.text('تقرير تجريبي', 20, 20);
                doc.text('التاريخ: ' + new Date().toLocaleDateString(), 20, 40);
                doc.text('اسم العميل: أحمد محمد', 20, 60);
                doc.text('المدينة: الرياض', 20, 80);
                doc.text('المبلغ: 1500 ريال', 20, 100);

                doc.save('test-arabic.pdf');
                showResult('✅ تم إنشاء PDF عربي بنجاح!', 'success');
            } catch (error) {
                showResult('❌ خطأ في إنشاء PDF العربي: ' + error.message, 'error');
            }
        }

        function testTablePDF() {
            try {
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF('portrait');

                doc.setFontSize(16);
                doc.text('جدول تجريبي', 105, 20, { align: 'center' });

                const tableData = [
                    ['1', 'INV-001', 'أحمد محمد', 'الرياض', '1500 ريال', 'فاتورة تجريبية'],
                    ['2', 'INV-002', 'فاطمة علي', 'جدة', '2000 ريال', 'فاتورة عادية'],
                    ['3', 'INV-003', 'محمد سالم', 'الدمام', '1200 ريال', 'فاتورة مستعجلة']
                ];

                doc.autoTable({
                    head: [['#', 'رقم الفاتورة', 'اسم العميل', 'المدينة', 'المبلغ', 'الملاحظات']],
                    body: tableData,
                    startY: 30,
                    styles: {
                        fontSize: 10,
                        cellPadding: 5,
                        halign: 'center'
                    },
                    headStyles: {
                        fillColor: [124, 58, 237],
                        textColor: [255, 255, 255],
                        fontStyle: 'bold'
                    }
                });

                doc.save('test-table.pdf');
                showResult('✅ تم إنشاء جدول PDF بنجاح!', 'success');
            } catch (error) {
                showResult('❌ خطأ في إنشاء جدول PDF: ' + error.message, 'error');
            }
        }

        function testFullReport() {
            try {
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF('portrait');

                // Title
                doc.setFontSize(18);
                doc.setFont('helvetica', 'bold');
                doc.text('تقرير الفواتير الجديدة', 105, 20, { align: 'center' });

                // Date and time
                doc.setFontSize(10);
                doc.setFont('helvetica', 'normal');
                const now = new Date();
                const dateStr = `${now.getDate()}/${now.getMonth() + 1}/${now.getFullYear()}`;
                const timeStr = `${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`;

                doc.text(`التاريخ: ${dateStr}`, 20, 35);
                doc.text(`الوقت: ${timeStr}`, 20, 42);

                // Table
                const tableData = [
                    ['1', 'INV-001', 'أحمد محمد', 'الرياض', '1,500 ريال', 'فاتورة تجريبية'],
                    ['2', 'INV-002', 'فاطمة علي', 'جدة', '2,000 ريال', 'فاتورة عادية'],
                    ['3', 'INV-003', 'محمد سالم', 'الدمام', '1,200 ريال', 'فاتورة مستعجلة'],
                    ['4', 'INV-004', 'سارة أحمد', 'مكة', '800 ريال', 'فاتورة صغيرة'],
                    ['5', 'INV-005', 'خالد محمود', 'المدينة', '3,000 ريال', 'فاتورة كبيرة']
                ];

                doc.autoTable({
                    head: [['#', 'رقم الفاتورة', 'اسم العميل', 'المدينة', 'المبلغ', 'الملاحظات']],
                    body: tableData,
                    startY: 50,
                    styles: {
                        fontSize: 9,
                        cellPadding: 3,
                        halign: 'center'
                    },
                    headStyles: {
                        fillColor: [124, 58, 237],
                        textColor: [255, 255, 255],
                        fontStyle: 'bold'
                    },
                    columnStyles: {
                        0: { cellWidth: 15 },
                        1: { cellWidth: 30 },
                        2: { cellWidth: 40 },
                        3: { cellWidth: 30 },
                        4: { cellWidth: 30 },
                        5: { cellWidth: 45 }
                    }
                });

                // Summary
                const finalY = doc.lastAutoTable.finalY + 15;
                const totalAmount = 8500;

                doc.setFontSize(12);
                doc.setFont('helvetica', 'bold');
                doc.text('ملخص التقرير:', 20, finalY);

                doc.setFont('helvetica', 'normal');
                doc.text(`• إجمالي الفواتير: ${tableData.length}`, 25, finalY + 8);
                doc.text(`• عدد العملاء: ${tableData.length}`, 25, finalY + 16);
                doc.text(`• المبلغ الإجمالي: ${totalAmount.toLocaleString()} ريال`, 25, finalY + 24);

                doc.save('test-full-report.pdf');
                showResult('✅ تم إنشاء التقرير الكامل بنجاح!', 'success');
            } catch (error) {
                showResult('❌ خطأ في إنشاء التقرير الكامل: ' + error.message, 'error');
            }
        }

        function createSampleData() {
            const sampleData = [
                { invoiceNumber: 'INV-001', customerName: 'أحمد محمد', city: 'الرياض', amount: 1500, notes: 'فاتورة تجريبية' },
                { invoiceNumber: 'INV-002', customerName: 'فاطمة علي', city: 'جدة', amount: 2000, notes: 'فاتورة عادية' },
                { invoiceNumber: 'INV-003', customerName: 'محمد سالم', city: 'الدمام', amount: 1200, notes: 'فاتورة مستعجلة' },
                { invoiceNumber: 'INV-004', customerName: 'سارة أحمد', city: 'مكة', amount: 800, notes: 'فاتورة صغيرة' },
                { invoiceNumber: 'INV-005', customerName: 'خالد محمود', city: 'المدينة', amount: 3000, notes: 'فاتورة كبيرة' }
            ];

            localStorage.setItem('sampleDebts', JSON.stringify(sampleData));

            let dataDisplay = '<h5>📊 البيانات التجريبية المحفوظة:</h5><ul>';
            sampleData.forEach(item => {
                dataDisplay += `<li><strong>${item.invoiceNumber}</strong> - ${item.customerName} (${item.city}) - ${item.amount} ريال</li>`;
            });
            dataDisplay += '</ul><p class="text-info">يمكنك الآن استخدام هذه البيانات في النظام الرئيسي.</p>';

            showResult(dataDisplay, 'success');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            showResult('🚀 مرحباً بك في اختبار PDF العربي! اختر أي اختبار من الأزرار أعلاه.', 'info');
        });
    </script>
</body>
</html>
