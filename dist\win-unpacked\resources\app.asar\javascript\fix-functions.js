// Fix Functions - Ensure all export/import functions work

// Simple CSV export function
function exportToCSV(type) {
    console.log('🔧 exportToCSV called with type:', type);

    if (typeof window.debtManager === 'undefined') {
        alert('نظام إدارة الديون غير جاهز');
        return;
    }

    try {
        // Get ALL data (not filtered data)
        let data = [];
        if (type === 'previous') {
            // استخدام جميع الديون السابقة وليس المفلترة
            data = debtManager.previousDebts || [];
            console.log(`📊 تصدير جميع الديون السابقة: ${data.length} دين`);
        } else {
            // استخدام جميع الفواتير الجديدة وليس المفلترة
            data = debtManager.debts || [];
            console.log(`📊 تصدير جميع الفواتير الجديدة: ${data.length} فاتورة`);
        }

        if (data.length === 0) {
            alert('لا توجد بيانات للتصدير');
            return;
        }

        // Create CSV content with proper Excel formatting
        let csvContent = '\uFEFF'; // UTF-8 BOM for Excel recognition

        // Use semicolon separator for better Excel compatibility in Arabic regions
        csvContent += 'رقم الفاتورة;اسم العميل;المدينة;المبلغ;الملاحظات\n';

        data.forEach(item => {
            // Clean and prepare each field
            const invoiceNumber = (item.invoiceNumber || '').toString().replace(/[;\n\r]/g, ' ');
            const customerName = (item.customerName || '').toString().replace(/[;\n\r]/g, ' ');
            const city = (item.city || '').toString().replace(/[;\n\r]/g, ' ');
            const amount = (item.amount || 0).toString();
            const notes = (item.notes || '').toString().replace(/[;\n\r]/g, ' ');

            // Create row with semicolon separator
            const row = [
                invoiceNumber,
                customerName,
                city,
                amount,
                notes
            ];
            csvContent += row.join(';') + '\n';
        });

        // Download file
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        link.href = url;
        link.download = `${type === 'previous' ? 'الديون_السابقة' : 'الفواتير_الجديدة'}_${new Date().toISOString().split('T')[0]}.csv`;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        URL.revokeObjectURL(url);

        if (typeof closeAllDropdowns === 'function') {
            closeAllDropdowns();
        }

        if (debtManager.showSuccess) {
            debtManager.showSuccess(`تم تصدير ${data.length} سجل إلى CSV بنجاح\n\n📋 تنسيق الأعمدة:\nA: رقم الفاتورة\nB: اسم العميل\nC: المدينة\nD: المبلغ\nE: الملاحظات\n\n💡 تم استخدام الفاصلة المنقوطة (;) لضمان فصل الأعمدة في Excel`);
        } else {
            alert(`تم تصدير ${data.length} سجل إلى CSV بنجاح`);
        }

    } catch (error) {
        console.error('CSV Export Error:', error);
        alert('حدث خطأ أثناء تصدير CSV: ' + error.message);
    }
}

// Enhanced Excel export function with proper column structure
function exportToExcel(type) {
    console.log('🔧 exportToExcel called with type:', type);

    if (typeof window.XLSX === 'undefined') {
        alert('مكتبة Excel غير متوفرة. يرجى إعادة تحميل الصفحة.');
        return;
    }

    if (typeof window.debtManager === 'undefined') {
        alert('نظام إدارة الديون غير جاهز');
        return;
    }

    try {
        // Get ALL data (not filtered data)
        let data = [];
        let fileName = '';

        if (type === 'previous') {
            // استخدام جميع الديون السابقة وليس المفلترة
            data = debtManager.previousDebts || [];
            fileName = 'الديون_السابقة';
            console.log(`📊 تصدير جميع الديون السابقة إلى Excel: ${data.length} دين`);
        } else {
            // استخدام جميع الفواتير الجديدة وليس المفلترة
            data = debtManager.debts || [];
            fileName = 'الفواتير_الجديدة';
            console.log(`📊 تصدير جميع الفواتير الجديدة إلى Excel: ${data.length} فاتورة`);
        }

        if (data.length === 0) {
            alert('لا توجد بيانات للتصدير');
            return;
        }

        // Prepare data with proper column structure
        // Each row will be an array with values in correct order
        const excelData = [
            // Header row
            ['رقم الفاتورة', 'اسم العميل', 'المدينة', 'المبلغ', 'الملاحظات'],
            // Data rows
            ...data.map(item => [
                item.invoiceNumber || '',
                item.customerName || '',
                item.city || '',
                item.amount || 0,
                item.notes || ''
            ])
        ];

        // Create workbook and worksheet
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.aoa_to_sheet(excelData); // Array of arrays to sheet

        // Set column widths for better display
        ws['!cols'] = [
            { wch: 15 }, // A: رقم الفاتورة
            { wch: 25 }, // B: اسم العميل
            { wch: 15 }, // C: المدينة
            { wch: 12 }, // D: المبلغ
            { wch: 30 }  // E: الملاحظات
        ];

        // Style the header row
        const headerStyle = {
            font: { bold: true, color: { rgb: "FFFFFF" } },
            fill: { fgColor: { rgb: "2563EB" } },
            alignment: { horizontal: "center", vertical: "center" }
        };

        // Apply header styling
        for (let col = 0; col < 5; col++) {
            const cellRef = XLSX.utils.encode_cell({ r: 0, c: col });
            if (!ws[cellRef]) ws[cellRef] = {};
            ws[cellRef].s = headerStyle;
        }

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(wb, ws, 'بيانات الفواتير');

        // Generate filename with date
        const currentDate = new Date().toISOString().split('T')[0];
        const fullFileName = `${fileName}_${currentDate}.xlsx`;

        // Write file
        XLSX.writeFile(wb, fullFileName);

        if (typeof closeAllDropdowns === 'function') {
            closeAllDropdowns();
        }

        if (debtManager.showSuccess) {
            debtManager.showSuccess(`تم تصدير ${data.length} سجل إلى Excel بنجاح`);
        } else {
            console.log(`تم تصدير ${data.length} سجل إلى Excel بنجاح`);
        }

    } catch (error) {
        console.error('Excel Export Error:', error);
        console.error('حدث خطأ أثناء تصدير البيانات إلى Excel: ' + error.message);
    }
}

// Function to create Arabic-friendly PDF using Canvas
function printArabicPDF(type) {
    console.log('🔧 printArabicPDF called with type:', type);

    try {
        // Show loading indicator
        if (typeof showToast === 'function') {
            showToast('جاري إنشاء ملف PDF بجودة عالية...', 'info');
        }

        // Check for jsPDF library with multiple possible locations
        let jsPDFConstructor = null;
        if (typeof window.jspdf !== 'undefined' && window.jspdf.jsPDF) {
            jsPDFConstructor = window.jspdf.jsPDF;
            console.log('✅ jsPDF found at window.jspdf.jsPDF');
        } else if (typeof window.jsPDF !== 'undefined') {
            jsPDFConstructor = window.jsPDF;
            console.log('✅ jsPDF found at window.jsPDF');
        } else {
            console.error('❌ jsPDF library not found');
            alert('مكتبة PDF غير متوفرة. يرجى إعادة تحميل الصفحة.');
            return;
        }

        // Check for html2canvas library
        if (typeof window.html2canvas === 'undefined') {
            console.error('❌ html2canvas library not loaded');
            alert('مكتبة html2canvas غير متوفرة. يرجى إعادة تحميل الصفحة.');
            return;
        }

        if (typeof window.debtManager === 'undefined') {
            console.error('❌ debtManager not available');
            alert('نظام إدارة الديون غير جاهز');
            return;
        }

        // Get data
        let data = [];
        let titleAr = '';

        if (type === 'previous') {
            // استخدام جميع الديون السابقة وليس المفلترة
            data = debtManager.previousDebts || [];
            titleAr = 'تقرير الديون السابقة';
            console.log(`📊 طباعة جميع الديون السابقة: ${data.length} دين`);
        } else {
            // استخدام جميع الفواتير الجديدة وليس المفلترة
            data = debtManager.debts || [];
            titleAr = 'تقرير الفواتير الجديدة';
            console.log(`📊 طباعة جميع الفواتير الجديدة: ${data.length} فاتورة`);
        }

        if (data.length === 0) {
            console.log('لا توجد بيانات للطباعة');
            return;
        }

        // Create readable canvas for clear printing
        const canvas = document.createElement('canvas');
        const scale = 2; // 2x resolution for crisp text
        canvas.width = 600 * scale; // Smaller width for larger text
        canvas.height = 800 * scale; // Smaller height for better scaling
        const ctx = canvas.getContext('2d');

        // Scale context for crisp rendering
        ctx.scale(scale, scale);

        // Optimize for readability
        ctx.imageSmoothingEnabled = true; // Better text quality
        ctx.textRenderingOptimization = 'optimizeQuality';

        // Set large, readable Arabic font
        ctx.font = '16px Arial, sans-serif';
        ctx.fillStyle = '#000000';
        ctx.textAlign = 'right';
        ctx.direction = 'rtl';

        // Clear canvas with white background
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, 600, 800);
        ctx.fillStyle = '#000000';

        // Draw title - large and clear
        ctx.font = 'bold 28px Arial';
        ctx.fillText(titleAr, 550, 50);

        // Simple line under title
        ctx.strokeStyle = '#000000';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(50, 70);
        ctx.lineTo(550, 70);
        ctx.stroke();

        // Draw date and time - larger font
        const now = new Date();
        const dateStr = now.toLocaleDateString('en-GB');
        const timeStr = now.toLocaleTimeString('en-GB', { hour12: false });
        ctx.font = '16px Arial';
        ctx.fillStyle = '#000000';
        ctx.fillText(`التاريخ: ${dateStr}`, 550, 95);
        ctx.fillText(`الوقت: ${timeStr}`, 350, 95);

        // Draw table headers - large and readable
        let y = 130;
        ctx.font = 'bold 16px Arial';
        ctx.fillStyle = '#000000';

        // Simple header line
        ctx.strokeStyle = '#000000';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(50, y + 15);
        ctx.lineTo(550, y + 15);
        ctx.stroke();

        // Header text - larger and clearer
        const headers = ['الملاحظات', 'المبلغ', 'المدينة', 'اسم العميل', 'رقم الفاتورة', '#'];
        let x = 530;
        const colWidths = [90, 80, 80, 90, 90, 30];

        headers.forEach((header, index) => {
            ctx.fillText(header, x, y);
            x -= colWidths[index];
        });

        // Draw data rows - large and readable
        y += 35;
        ctx.font = '14px Arial';
        ctx.fillStyle = '#000000';

        // Use Arabic HTML2Canvas approach for perfect Arabic support
        return generateArabicPDF(data, titleAr);

    } catch (error) {
        console.error('Arabic PDF Error:', error);
        alert('حدث خطأ أثناء إنشاء PDF: ' + error.message);
    }
}

// Arabic PDF generator using HTML2Canvas
function generateArabicPDF(data, titleAr) {
    // Create a temporary HTML table with Arabic text
    const tempDiv = document.createElement('div');
    tempDiv.style.cssText = `
        position: absolute;
        top: -9999px;
        left: -9999px;
        width: 800px;
        background: white;
        padding: 20px;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        direction: rtl;
        text-align: right;
    `;

    // Create HTML content with original Arabic text
    const now = new Date();
    const dateStr = now.toLocaleDateString('ar-SA');
    const timeStr = now.toLocaleTimeString('ar-SA', { hour12: false });

    const totalAmount = data.reduce((sum, item) => sum + (item.amount || 0), 0);
    const uniqueCustomers = new Set(data.map(item => `${item.customerName}_${item.city}`)).size;

    // Create optimized HTML for multi-page PDF with better performance
    const itemsPerPage = 30; // زيادة عدد العناصر لتقليل الصفحات
    const totalPages = Math.ceil(data.length / itemsPerPage);

    // Sort data for better organization
    const sortedData = [...data].sort((a, b) => {
        // ترتيب حسب المدينة أولاً ثم اسم العميل
        const cityCompare = (a.city || '').localeCompare(b.city || '', 'ar');
        if (cityCompare !== 0) return cityCompare;
        return (a.customerName || '').localeCompare(b.customerName || '', 'ar');
    });

    let htmlContent = '';

    for (let page = 0; page < totalPages; page++) {
        const startIndex = page * itemsPerPage;
        const endIndex = Math.min(startIndex + itemsPerPage, sortedData.length);
        const pageData = sortedData.slice(startIndex, endIndex);

        htmlContent += `
            <div style="page-break-after: ${page < totalPages - 1 ? 'always' : 'auto'}; min-height: 800px;">
                ${page === 0 ? `
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h1 style="color: #2980b9; margin: 0; font-size: 24px;">${titleAr}</h1>
                        <p style="color: #7f8c8d; margin: 5px 0;">نظام إدارة الديون</p>
                        <p style="color: #95a5a6; font-size: 12px;">التاريخ: ${dateStr} | الوقت: ${timeStr}</p>
                        <p style="color: #95a5a6; font-size: 12px;">إجمالي السجلات: ${data.length} | الصفحات: ${totalPages}</p>
                    </div>
                ` : `
                    <div style="text-align: center; margin-bottom: 20px;">
                        <h2 style="color: #2980b9; margin: 0; font-size: 18px;">${titleAr} - صفحة ${page + 1}</h2>
                    </div>
                `}

                <table style="width: 100%; border-collapse: collapse; margin: 8px 0; font-size: 11px; font-family: 'Arial', sans-serif;">
                    <thead>
                        <tr style="background: linear-gradient(135deg, #2980b9 0%, #3498db 100%); color: white; font-weight: bold;">
                            <th style="border: 2px solid #1f5f8b; padding: 6px; text-align: center; width: 6%; font-size: 10px;">#</th>
                            <th style="border: 2px solid #1f5f8b; padding: 6px; text-align: center; width: 16%; font-size: 10px;">رقم الفاتورة</th>
                            <th style="border: 2px solid #1f5f8b; padding: 6px; text-align: center; width: 28%; font-size: 10px;">اسم العميل</th>
                            <th style="border: 2px solid #1f5f8b; padding: 6px; text-align: center; width: 14%; font-size: 10px;">المدينة</th>
                            <th style="border: 2px solid #1f5f8b; padding: 6px; text-align: center; width: 16%; font-size: 10px;">المبلغ (ر.س)</th>
                            <th style="border: 2px solid #1f5f8b; padding: 6px; text-align: center; width: 20%; font-size: 10px;">الملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${pageData.map((item, index) => {
                            const rowNumber = startIndex + index + 1;
                            const isEven = rowNumber % 2 === 0;
                            const bgColor = isEven ? '#f8f9fa' : 'white';
                            const borderColor = '#d1d5db';

                            return `
                            <tr style="background-color: ${bgColor}; transition: background-color 0.2s;">
                                <td style="border: 1px solid ${borderColor}; padding: 4px; text-align: center; font-size: 10px; font-weight: 600;">${rowNumber}</td>
                                <td style="border: 1px solid ${borderColor}; padding: 4px; text-align: center; font-size: 10px; font-weight: 500;">${item.invoiceNumber || '-'}</td>
                                <td style="border: 1px solid ${borderColor}; padding: 4px; text-align: center; font-size: 10px; font-weight: 500; color: #2563eb;">${item.customerName || '-'}</td>
                                <td style="border: 1px solid ${borderColor}; padding: 4px; text-align: center; font-size: 10px; color: #059669;">${item.city || '-'}</td>
                                <td style="border: 1px solid ${borderColor}; padding: 4px; text-align: center; font-size: 10px; font-weight: 600; color: #dc2626;">${(item.amount || 0).toLocaleString('ar-SA')}</td>
                                <td style="border: 1px solid ${borderColor}; padding: 4px; text-align: center; font-size: 9px; color: #6b7280;">${(item.notes || '').length > 25 ? (item.notes || '').substring(0, 25) + '...' : (item.notes || '-')}</td>
                            </tr>`;
                        }).join('')}
                    </tbody>
                </table>

                <div style="text-align: center; margin-top: 15px; color: #95a5a6; font-size: 10px;">
                    صفحة ${page + 1} من ${totalPages} | السجلات ${startIndex + 1} - ${endIndex} من ${data.length}
                </div>
            </div>
        `;
    }

    // Add summary on last page
    htmlContent += `
        <div style="background-color: #ecf0f1; border: 2px solid #3498db; border-radius: 8px; padding: 15px; margin-top: 20px;">
            <h3 style="color: #2980b9; text-align: center; margin: 0 0 10px 0;">ملخص التقرير</h3>
            <p style="margin: 5px 0; font-size: 12px;"><strong>إجمالي الفواتير:</strong> ${data.length}</p>
            <p style="margin: 5px 0; font-size: 12px;"><strong>عدد العملاء:</strong> ${uniqueCustomers}</p>
            <p style="margin: 5px 0; font-size: 12px;"><strong>المبلغ الإجمالي:</strong> ${totalAmount.toLocaleString()} ريال سعودي</p>
            <p style="margin: 5px 0; font-size: 12px;"><strong>عدد الصفحات:</strong> ${totalPages}</p>
        </div>

        <div style="text-align: center; margin-top: 15px; color: #95a5a6; font-size: 10px;">
            تم إنشاء التقرير في: ${dateStr} الساعة ${timeStr}
        </div>
    `;

    tempDiv.innerHTML = htmlContent;

    document.body.appendChild(tempDiv);

    // Convert HTML to canvas then to PDF with enhanced settings
    html2canvas(tempDiv, {
        scale: 1.8, // تحسين الجودة مع الحفاظ على السرعة
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: tempDiv.scrollWidth,
        height: tempDiv.scrollHeight,
        scrollX: 0,
        scrollY: 0,
        windowWidth: 900, // عرض أكبر لجودة أفضل
        windowHeight: tempDiv.scrollHeight,
        removeContainer: true,
        imageTimeout: 8000, // وقت انتظار أطول للجودة
        logging: false, // إيقاف السجلات لتحسين الأداء
        onclone: function(clonedDoc) {
            // تحسين الخطوط والألوان في النسخة المستنسخة
            const style = clonedDoc.createElement('style');
            style.textContent = `
                * {
                    font-family: 'Arial', 'Tahoma', sans-serif !important;
                    -webkit-font-smoothing: antialiased;
                    -moz-osx-font-smoothing: grayscale;
                    text-rendering: optimizeLegibility;
                }
                table {
                    border-collapse: collapse !important;
                    font-variant-numeric: tabular-nums;
                }
                th, td {
                    border: 1px solid #d1d5db !important;
                    text-align: center !important;
                }
            `;
            clonedDoc.head.appendChild(style);
        }
    }).then(canvas => {
        // Use the constructor we found earlier
        const doc = new jsPDFConstructor('p', 'mm', 'a4');

        // تحسين جودة الصورة قبل إضافتها للـ PDF
        const imgData = canvas.toDataURL('image/jpeg', 0.95); // استخدام JPEG بجودة عالية

        // Calculate page dimensions with optimized margins
        const pageWidth = 210; // A4 width in mm
        const pageHeight = 297; // A4 height in mm
        const margin = 8; // هوامش أصغر لمساحة أكبر
        const contentWidth = pageWidth - (2 * margin);
        const contentHeight = pageHeight - (2 * margin);

        // Calculate how many pages we need with better scaling
        const imgHeightScaled = (canvas.height * contentWidth) / canvas.width;
        let heightLeft = imgHeightScaled;
        let position = 0;
        let pageCount = 1;

        console.log(`📄 PDF Info: Canvas ${canvas.width}x${canvas.height}, Scaled height: ${imgHeightScaled}mm`);

        // Add first page with enhanced quality
        doc.addImage(imgData, 'JPEG', margin, margin, contentWidth, Math.min(imgHeightScaled, contentHeight));
        heightLeft -= contentHeight;

        // Add additional pages if needed with better positioning
        while (heightLeft > 0) {
            position = heightLeft - imgHeightScaled;
            doc.addPage();
            doc.addImage(imgData, 'JPEG', margin, position + margin, contentWidth, imgHeightScaled);
            heightLeft -= contentHeight;
            pageCount++;
        }

        console.log(`📄 PDF created with ${pageCount} pages`);

        // Save the PDF with timestamp for uniqueness
        const timestamp = new Date().toISOString().split('T')[0];
        const fileName = titleAr === 'تقرير الفواتير الجديدة' ?
            `تقرير_الفواتير_الجديدة_${timestamp}.pdf` :
            `تقرير_الديون_السابقة_${timestamp}.pdf`;

        // Optimize PDF before saving
        doc.setProperties({
            title: titleAr,
            creator: 'نظام إدارة الديون',
            producer: 'نظام إدارة الديون المحسن'
        });

        doc.save(fileName);

        console.log(`✅ تم إنشاء PDF محسن بـ ${pageCount} صفحة في ${Date.now() - performance.now()}ms`);

        // Remove temporary element
        document.body.removeChild(tempDiv);

        // Show enhanced success message
        if (typeof showToast === 'function') {
            showToast(`تم إنشاء ملف PDF بجودة عالية! (${pageCount} صفحة)`, 'success');
        } else {
            alert(`تم إنشاء ملف PDF بجودة عالية! (${pageCount} صفحة)`);
        }
    }).catch(error => {
        console.error('Error generating PDF:', error);
        if (document.body.contains(tempDiv)) {
            document.body.removeChild(tempDiv);
        }
        if (typeof showToast === 'function') {
            showToast('حدث خطأ في إنشاء ملف PDF', 'error');
        } else {
            alert('حدث خطأ في إنشاء ملف PDF');
        }
    });

    } catch (error) {
        console.error('PDF Generation Error:', error);
        if (typeof showToast === 'function') {
            showToast('حدث خطأ في إنشاء ملف PDF: ' + error.message, 'error');
        } else {
            alert('حدث خطأ في إنشاء ملف PDF: ' + error.message);
        }
    }
}

// Professional table PDF generator
function createProfessionalTablePDF(data, titleAr) {
    // Initialize PDF document with optimized settings
    let doc;
    if (typeof window.jsPDF !== 'undefined') {
        doc = new window.jsPDF('p', 'mm', 'a4');
    } else if (typeof window.jspdf !== 'undefined' && window.jspdf.jsPDF) {
        doc = new window.jspdf.jsPDF('p', 'mm', 'a4');
    } else {
        throw new Error('jsPDF constructor not found');
    }

    // Set up document properties for better performance
    doc.setProperties({
        title: titleAr,
        creator: 'نظام إدارة الديون',
        producer: 'نظام إدارة الديون'
    });

    // Add title in English only for PDF compatibility
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(18);

    // Use English title to avoid encoding issues
    const titleDisplay = titleAr === 'تقرير الفواتير الجديدة' ? 'New Invoices Report' :
                        titleAr === 'تقرير الديون السابقة' ? 'Previous Debts Report' : 'Financial Report';
    doc.text(titleDisplay, 105, 20, { align: 'center' });

    // Add subtitle
    doc.setFontSize(12);
    doc.setFont('helvetica', 'normal');
    doc.text('Debt Management System', 105, 30, { align: 'center' });

    // Add date and time in English
    const now = new Date();
    const dateStr = now.toLocaleDateString('en-GB');
    const timeStr = now.toLocaleTimeString('en-GB', { hour12: false });
    doc.setFontSize(10);
    doc.text(`Date: ${dateStr}`, 20, 45);
    doc.text(`Time: ${timeStr}`, 120, 45);

    // Use English headers only for PDF compatibility
    const tableHeaders = [
        '#',
        'Invoice No.',
        'Customer Name',
        'City',
        'Amount (SAR)',
        'Notes'
    ];

    // Function to transliterate Arabic names for better PDF compatibility
    function transliterateArabic(text) {
        if (!text) return '';

        const arabicToLatin = {
            // أسماء شائعة
            'محمد': 'Mohammed', 'أحمد': 'Ahmed', 'علي': 'Ali', 'فاطمة': 'Fatima',
            'عبدالله': 'Abdullah', 'عبدالرحمن': 'Abdulrahman', 'خالد': 'Khalid',
            'سارة': 'Sarah', 'نورا': 'Nora', 'ريم': 'Reem', 'عمر': 'Omar',
            'يوسف': 'Youssef', 'مريم': 'Mariam', 'عائشة': 'Aisha', 'حسن': 'Hassan',
            'حسين': 'Hussein', 'زينب': 'Zainab', 'كريم': 'Kareem', 'ياسر': 'Yasser',
            'منى': 'Mona', 'هند': 'Hind', 'سلمان': 'Salman', 'عبدالعزيز': 'Abdulaziz',

            // المدن السعودية
            'الرياض': 'Riyadh', 'جدة': 'Jeddah', 'الدمام': 'Dammam', 'مكة': 'Makkah',
            'المدينة': 'Madinah', 'الطائف': 'Taif', 'أبها': 'Abha', 'تبوك': 'Tabuk',
            'الخبر': 'Khobar', 'القطيف': 'Qatif', 'حائل': 'Hail', 'جازان': 'Jazan',
            'نجران': 'Najran', 'الباحة': 'Baha', 'عرعر': 'Arar', 'سكاكا': 'Sakaka',

            // مصطلحات مالية
            'دفعة': 'Payment', 'أولى': 'First', 'كاملة': 'Complete', 'قسط': 'Installment',
            'شهري': 'Monthly', 'نقدي': 'Cash', 'تحويل': 'Transfer', 'مؤجل': 'Deferred',
            'عاجل': 'Urgent', 'متأخر': 'Late', 'مكتمل': 'Completed', 'معلق': 'Pending'
        };

        let result = text;
        for (const [arabic, latin] of Object.entries(arabicToLatin)) {
            result = result.replace(new RegExp(arabic, 'g'), latin);
        }

        return result;
    }

    const tableData = data.map((item, index) => [
        (index + 1).toString(),
        item.invoiceNumber || '',
        transliterateArabic(item.customerName || '') || item.customerName || '',
        transliterateArabic(item.city || '') || item.city || '',
        (item.amount || 0).toLocaleString() + ' SAR',
        transliterateArabic(item.notes || '') || (item.notes || '').length > 30 ? (item.notes || '').substring(0, 30) + '...' : (item.notes || '')
    ]);

    // Create enhanced professional table with autoTable
    if (typeof doc.autoTable === 'function') {
        doc.autoTable({
            head: [tableHeaders],
            body: tableData,
            startY: 55,
            theme: 'grid',
            styles: {
                font: 'helvetica',
                fontSize: 9,
                cellPadding: 5,
                overflow: 'linebreak',
                halign: 'center',
                valign: 'middle',
                lineColor: [44, 62, 80],
                lineWidth: 0.3,
                minCellHeight: 15
            },
            headStyles: {
                fillColor: [41, 128, 185],
                textColor: [255, 255, 255],
                fontStyle: 'bold',
                fontSize: 10,
                halign: 'center',
                valign: 'middle',
                minCellHeight: 20
            },
            columnStyles: {
                0: { cellWidth: 12, halign: 'center' },   // #
                1: { cellWidth: 28, halign: 'center' },   // Invoice No.
                2: { cellWidth: 45, halign: 'center' },   // Customer Name
                3: { cellWidth: 25, halign: 'center' },   // City
                4: { cellWidth: 25, halign: 'center' },   // Amount
                5: { cellWidth: 35, halign: 'center' }    // Notes
            },
            alternateRowStyles: {
                fillColor: [245, 248, 250]
            },
            bodyStyles: {
                textColor: [44, 62, 80]
            },
            margin: { top: 55, left: 10, right: 10 }
        });

        // Add enhanced summary section
        const finalY = doc.lastAutoTable.finalY + 20;
        const totalAmount = data.reduce((sum, item) => sum + (item.amount || 0), 0);
        const uniqueCustomers = new Set(data.map(item => `${item.customerName}_${item.city}`)).size;

        // Summary box with enhanced styling
        doc.setDrawColor(41, 128, 185);
        doc.setFillColor(245, 248, 250);
        doc.setLineWidth(1);
        doc.roundedRect(15, finalY, 180, 35, 3, 3, 'FD');

        // Summary title in English
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(12);
        doc.setTextColor(41, 128, 185);
        doc.text('Report Summary', 105, finalY + 10, { align: 'center' });

        // Summary data in English
        doc.setFont('helvetica', 'normal');
        doc.setFontSize(10);
        doc.setTextColor(44, 62, 80);
        doc.text(`Total Invoices: ${data.length}`, 25, finalY + 18);
        doc.text(`Number of Customers: ${uniqueCustomers}`, 25, finalY + 24);
        doc.text(`Total Amount: ${totalAmount.toLocaleString()} SAR`, 25, finalY + 30);

        // Generation info in English
        doc.setFontSize(8);
        doc.setTextColor(128, 128, 128);
        doc.text(`Generated on: ${dateStr} at ${timeStr}`, 105, finalY + 45, { align: 'center' });

    } else {
        // Fallback without autoTable - simple text layout
        let y = 50;
        doc.setFontSize(12);
        doc.setFont('helvetica', 'bold');
        doc.text('البيانات:', 20, y);
        y += 10;

        doc.setFont('helvetica', 'normal');
        doc.setFontSize(10);
        data.forEach((item, index) => {
            const line = `${index + 1}. ${item.invoiceNumber} - ${item.customerName} - ${item.city} - ${(item.amount || 0).toLocaleString()} ر.س`;
            doc.text(line, 25, y);
            y += 8;
            if (y > 270) {
                doc.addPage();
                y = 20;
            }
        });

        // Add summary for fallback
        y += 10;
        const totalAmount = data.reduce((sum, item) => sum + (item.amount || 0), 0);
        const uniqueCustomers = new Set(data.map(item => `${item.customerName}_${item.city}`)).size;

        doc.setFont('helvetica', 'bold');
        doc.text('ملخص التقرير:', 20, y);
        y += 8;
        doc.setFont('helvetica', 'normal');
        doc.text(`إجمالي الفواتير: ${data.length}`, 25, y);
        y += 6;
        doc.text(`عدد العملاء: ${uniqueCustomers}`, 25, y);
        y += 6;
        doc.text(`المبلغ الإجمالي: ${totalAmount.toLocaleString()} ريال سعودي`, 25, y);
    }

    // Save PDF with optimized filename
    const fileName = `${titleAr.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;
    doc.save(fileName);

    if (typeof closeAllDropdowns === 'function') {
        closeAllDropdowns();
    }

    if (window.debtManager && debtManager.showSuccess) {
        debtManager.showSuccess('تم إنشاء ملف PDF بنجاح');
    } else {
        alert('تم إنشاء ملف PDF بنجاح');
    }
}

// Simple test PDF function
function testPDF() {
    console.log('🧪 Testing PDF generation...');

    try {
        // Check libraries
        console.log('jspdf available:', typeof window.jspdf);
        console.log('html2canvas available:', typeof window.html2canvas);
        console.log('debtManager available:', typeof window.debtManager);

        // Try to create a simple PDF
        let jsPDFConstructor = null;
        if (typeof window.jspdf !== 'undefined' && window.jspdf.jsPDF) {
            jsPDFConstructor = window.jspdf.jsPDF;
        } else if (typeof window.jsPDF !== 'undefined') {
            jsPDFConstructor = window.jsPDF;
        }

        if (jsPDFConstructor) {
            const doc = new jsPDFConstructor();
            doc.text('Test PDF - مرحبا', 20, 20);
            doc.save('test.pdf');
            console.log('✅ Test PDF created successfully');
            alert('تم إنشاء ملف PDF تجريبي بنجاح!');
        } else {
            console.error('❌ jsPDF not found');
            alert('مكتبة PDF غير متوفرة');
        }
    } catch (error) {
        console.error('Test PDF Error:', error);
        alert('خطأ في اختبار PDF: ' + error.message);
    }
}

// Simple fallback PDF function
function simplePrintPDF(type) {
    console.log('🔧 simplePrintPDF called with type:', type);

    try {
        // Check for jsPDF
        let jsPDFConstructor = null;
        if (typeof window.jspdf !== 'undefined' && window.jspdf.jsPDF) {
            jsPDFConstructor = window.jspdf.jsPDF;
        } else if (typeof window.jsPDF !== 'undefined') {
            jsPDFConstructor = window.jsPDF;
        } else {
            alert('مكتبة PDF غير متوفرة');
            return;
        }

        // Check for debtManager
        if (typeof window.debtManager === 'undefined') {
            alert('نظام إدارة الديون غير جاهز');
            return;
        }

        // Get data
        let data = [];
        let title = '';

        if (type === 'previous') {
            data = debtManager.previousDebts || [];
            title = 'Previous Debts Report';
        } else {
            data = debtManager.debts || [];
            title = 'New Invoices Report';
        }

        if (data.length === 0) {
            alert('لا توجد بيانات للطباعة');
            return;
        }

        // Create PDF
        const doc = new jsPDFConstructor();

        // Add title
        doc.setFontSize(16);
        doc.text(title, 20, 20);

        // Add date
        const now = new Date();
        doc.setFontSize(10);
        doc.text(`Date: ${now.toLocaleDateString()}`, 20, 30);

        // Add data
        let y = 50;
        doc.setFontSize(12);
        data.forEach((item, index) => {
            if (y > 270) {
                doc.addPage();
                y = 20;
            }

            const line = `${index + 1}. ${item.invoiceNumber || ''} - ${item.customerName || ''} - ${item.city || ''} - ${(item.amount || 0).toLocaleString()} SAR`;
            doc.text(line, 20, y);
            y += 8;
        });

        // Save
        const fileName = `${title.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;
        doc.save(fileName);

        alert('تم إنشاء ملف PDF بنجاح!');

    } catch (error) {
        console.error('Simple PDF Error:', error);
        alert('حدث خطأ في إنشاء ملف PDF: ' + error.message);
    }
}

// Alias for backward compatibility - now points to Arabic PDF
function printPDF(type) {
    // Try advanced PDF first, fallback to simple if it fails
    try {
        printArabicPDF(type);
    } catch (error) {
        console.warn('Advanced PDF failed, trying simple PDF:', error);
        simplePrintPDF(type);
    }
}

// Simple file import function
function importFromFile(event, type) {
    console.log('🔧 importFromFile called with type:', type);

    // Check if debtManager is available
    if (typeof window.debtManager === 'undefined') {
        alert('نظام إدارة الديون غير جاهز. يرجى إعادة تحميل الصفحة.');
        console.error('debtManager not available for import');
        return;
    }

    const file = event.target.files[0];
    if (!file) {
        console.log('No file selected');
        return;
    }

    const fileName = file.name.toLowerCase();
    console.log('📁 File selected:', fileName);

    if (fileName.endsWith('.csv')) {
        importCSVFile(file, type, event);
    } else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
        alert('ملفات Excel غير مدعومة حالياً. يرجى استخدام CSV');
    } else {
        alert('يرجى اختيار ملف CSV (.csv)');
    }
}

// Enhanced CSV import function with proper column mapping
function importCSVFile(file, type, originalEvent = null) {
    console.log('📂 Starting CSV import for type:', type);
    console.log('📂 File details:', { name: file.name, size: file.size, type: file.type });

    // Double check debtManager
    if (typeof window.debtManager === 'undefined') {
        alert('نظام إدارة الديون غير جاهز. يرجى إعادة تحميل الصفحة.');
        return;
    }

    const reader = new FileReader();

    reader.onload = function(e) {
        try {
            console.log('📖 File read successfully');
            let csvData = e.target.result;

            // Remove BOM if present
            if (csvData.charCodeAt(0) === 0xFEFF) {
                csvData = csvData.slice(1);
            }

            // Split into lines
            const lines = csvData.split('\n').filter(line => line.trim());

            if (lines.length < 2) {
                alert('الملف فارغ أو لا يحتوي على بيانات');
                return;
            }

            // Check header format and detect separator
            const headerLine = lines[0].trim();
            const expectedHeaders = ['رقم الفاتورة', 'اسم العميل', 'المدينة', 'المبلغ', 'الملاحظات'];

            // Detect separator (semicolon or comma)
            const separator = headerLine.includes(';') ? ';' : ',';

            console.log('📋 Header found:', headerLine);
            console.log('📋 Separator detected:', separator);
            console.log('📋 Expected format:', expectedHeaders.join(separator));

            // Skip header and process data
            const processedData = [];

            for (let i = 1; i < lines.length; i++) {
                const line = lines[i].trim();
                if (!line) continue;

                // Enhanced CSV parsing with detected separator
                const parts = parseCSVLine(line, separator);

                if (parts.length >= 4) {
                    const newItem = {
                        id: Date.now() + i + Math.random(),
                        invoiceNumber: cleanCSVField(parts[0]) || '',
                        customerName: cleanCSVField(parts[1]) || '',
                        city: cleanCSVField(parts[2]) || '',
                        amount: parseFloat(cleanCSVField(parts[3])) || 0,
                        notes: cleanCSVField(parts[4]) || '',
                        date: new Date().toLocaleDateString('en-GB'),
                        timestamp: new Date().toISOString()
                    };

                    // Check for duplicates
                    if (!debtManager.isDuplicateInvoice(newItem.invoiceNumber)) {
                        processedData.push(newItem);
                    } else {
                        console.log('⚠️ Duplicate invoice skipped:', newItem.invoiceNumber);
                    }
                }
            }

            // Helper function to parse CSV line with flexible separator
            function parseCSVLine(line, separator = ',') {
                // Simple split for semicolon separator (more reliable for Arabic Excel)
                if (separator === ';') {
                    return line.split(';').map(field => field.trim());
                }

                // Advanced parsing for comma separator (handles quotes)
                const result = [];
                let current = '';
                let inQuotes = false;

                for (let i = 0; i < line.length; i++) {
                    const char = line[i];

                    if (char === '"') {
                        if (inQuotes && line[i + 1] === '"') {
                            // Escaped quote
                            current += '"';
                            i++; // Skip next quote
                        } else {
                            // Toggle quote state
                            inQuotes = !inQuotes;
                        }
                    } else if (char === separator && !inQuotes) {
                        // Field separator
                        result.push(current.trim());
                        current = '';
                    } else {
                        current += char;
                    }
                }

                // Add last field
                result.push(current.trim());
                return result;
            }

            // Helper function to clean CSV field
            function cleanCSVField(field) {
                if (!field) return '';
                return field.replace(/^"|"$/g, '').replace(/""/g, '"').trim();
            }

            console.log('📊 Processed data count:', processedData.length);
            console.log('📊 Sample processed data:', processedData.slice(0, 2));

            if (processedData.length > 0) {
                // Add to appropriate section
                if (type === 'previous') {
                    console.log('📥 Adding to previous debts');
                    if (!debtManager.previousDebts) {
                        debtManager.previousDebts = [];
                    }
                    debtManager.previousDebts.unshift(...processedData);
                    debtManager.savePreviousDebts();
                    debtManager.displayPreviousDebt(debtManager.previousDebts);
                } else {
                    console.log('📥 Adding to new invoices');
                    debtManager.debts.unshift(...processedData);
                    debtManager.saveData();
                    debtManager.updateStatistics();
                    debtManager.displayRecords();
                }

                console.log('✅ Import completed successfully');
                if (debtManager.showSuccess) {
                    debtManager.showSuccess(`تم استيراد ${processedData.length} سجل بنجاح`);
                } else {
                    alert(`تم استيراد ${processedData.length} سجل بنجاح`);
                }
            } else {
                console.log('❌ No valid data found');
                alert('لم يتم العثور على بيانات صالحة');
            }

            // Reset file input if available
            if (originalEvent && originalEvent.target) {
                originalEvent.target.value = '';
            }

        } catch (error) {
            console.error('CSV Import Error:', error);
            alert('حدث خطأ أثناء استيراد الملف: ' + error.message);
        }
    };

    reader.onerror = function() {
        alert('خطأ في قراءة الملف');
    };

    reader.readAsText(file, 'UTF-8');
}

// Function to check if all required libraries are loaded
function checkLibraries() {
    console.log('🔍 Checking required libraries...');

    const libraries = {
        'jsPDF': typeof window.jspdf !== 'undefined' || typeof window.jsPDF !== 'undefined',
        'html2canvas': typeof window.html2canvas !== 'undefined',
        'XLSX': typeof window.XLSX !== 'undefined',
        'debtManager': typeof window.debtManager !== 'undefined'
    };

    console.log('📚 Library status:', libraries);

    const missingLibraries = Object.entries(libraries)
        .filter(([name, loaded]) => !loaded)
        .map(([name]) => name);

    if (missingLibraries.length > 0) {
        console.warn('⚠️ Missing libraries:', missingLibraries);
        return false;
    } else {
        console.log('✅ All libraries loaded successfully');
        return true;
    }
}

// Initialize library checking when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for libraries to load
    setTimeout(() => {
        checkLibraries();

        // Add test functions to window for debugging
        window.testPDF = testPDF;
        window.simplePrintPDF = simplePrintPDF;
        window.checkLibraries = checkLibraries;
        window.exportCSV = exportCSV;
        window.exportExcel = exportExcel;
        window.printPDF = printPDF;
        window.printArabicPDF = printArabicPDF;
        window.toggleDropdown = toggleDropdown;
        window.closeAllDropdowns = closeAllDropdowns;

        console.log('🛠️ Debug functions available:');
        console.log('- testPDF() - Test basic PDF creation');
        console.log('- simplePrintPDF("new") - Simple PDF for new invoices');
        console.log('- simplePrintPDF("previous") - Simple PDF for previous debts');
        console.log('- checkLibraries() - Check library status');

        // Add click outside handler for dropdowns
        document.addEventListener('click', function(event) {
            const isDropdownButton = event.target.closest('.dropdown-btn');
            const isDropdownContent = event.target.closest('.dropdown-content');

            if (!isDropdownButton && !isDropdownContent) {
                closeAllDropdowns();
            }
        });
    }, 1000);
});

// Also check when window loads
window.addEventListener('load', function() {
    setTimeout(() => {
        checkLibraries();
    }, 500);
});

// Export functions for global access
function exportCSV(type) {
    console.log('🔧 exportCSV called with type:', type);

    try {
        if (typeof window.debtManager === 'undefined') {
            alert('نظام إدارة الديون غير جاهز. يرجى إعادة تحميل الصفحة.');
            return;
        }

        // Get data
        let data = [];
        let fileName = '';

        if (type === 'previous') {
            data = debtManager.previousDebts || [];
            fileName = 'الديون_السابقة';
        } else {
            data = debtManager.debts || [];
            fileName = 'الفواتير_الجديدة';
        }

        if (data.length === 0) {
            alert('لا توجد بيانات للتصدير');
            return;
        }

        // Create CSV content with UTF-8 BOM
        let csvContent = '\uFEFF'; // UTF-8 BOM
        csvContent += 'رقم الفاتورة,اسم العميل,المدينة,المبلغ,الملاحظات\n';

        // Add data rows
        data.forEach(item => {
            const row = [
                `"${(item.invoiceNumber || '').replace(/"/g, '""')}"`,
                `"${(item.customerName || '').replace(/"/g, '""')}"`,
                `"${(item.city || '').replace(/"/g, '""')}"`,
                `"${item.amount || 0}"`,
                `"${(item.notes || '').replace(/"/g, '""')}"`
            ];
            csvContent += row.join(',') + '\n';
        });

        // Create and download file
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        link.setAttribute('href', url);
        link.setAttribute('download', `${fileName}_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        alert(`تم تصدير ${data.length} سجل بنجاح إلى ملف CSV`);

    } catch (error) {
        console.error('CSV Export Error:', error);
        alert('حدث خطأ أثناء تصدير البيانات إلى CSV');
    }
}

// Export to Excel function
function exportExcel(type) {
    console.log('🔧 exportExcel called with type:', type);

    try {
        if (!window.XLSX) {
            alert('مكتبة Excel غير متوفرة. يرجى إعادة تحميل الصفحة.');
            return;
        }

        if (typeof window.debtManager === 'undefined') {
            alert('نظام إدارة الديون غير جاهز. يرجى إعادة تحميل الصفحة.');
            return;
        }

        // Get data
        let data = [];
        let fileName = '';

        if (type === 'previous') {
            data = debtManager.previousDebts || [];
            fileName = 'الديون_السابقة';
        } else {
            data = debtManager.debts || [];
            fileName = 'الفواتير_الجديدة';
        }

        if (data.length === 0) {
            alert('لا توجد بيانات للتصدير');
            return;
        }

        // Prepare data for Excel
        const excelData = data.map((item, index) => ({
            'رقم الفاتورة': item.invoiceNumber || '',
            'اسم العميل': item.customerName || '',
            'المدينة': item.city || '',
            'المبلغ': item.amount || 0,
            'الملاحظات': item.notes || ''
        }));

        // Create workbook and worksheet
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(excelData);

        // Set column widths
        const colWidths = [
            { wch: 18 }, // رقم الفاتورة
            { wch: 25 }, // اسم العميل
            { wch: 15 }, // المدينة
            { wch: 15 }, // المبلغ
            { wch: 35 }  // الملاحظات
        ];
        ws['!cols'] = colWidths;

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(wb, ws, 'بيانات الفواتير');

        // Generate file name
        const currentDate = new Date().toISOString().split('T')[0];
        const fullFileName = `${fileName}_${currentDate}.xlsx`;

        // Write file
        XLSX.writeFile(wb, fullFileName);

        alert(`تم تصدير ${data.length} سجل بنجاح إلى ملف Excel`);

    } catch (error) {
        console.error('Excel Export Error:', error);
        alert('حدث خطأ أثناء تصدير البيانات إلى Excel');
    }
}

// Toggle dropdown function
function toggleDropdown(dropdownId) {
    console.log('🔧 toggleDropdown called with id:', dropdownId);

    // Close all other dropdowns first
    const allDropdowns = document.querySelectorAll('.dropdown-content');
    allDropdowns.forEach(dropdown => {
        if (dropdown.id !== dropdownId) {
            dropdown.style.display = 'none';
        }
    });

    // Toggle the requested dropdown
    const dropdown = document.getElementById(dropdownId);
    if (dropdown) {
        dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
    }
}

// Close all dropdowns function
function closeAllDropdowns() {
    const allDropdowns = document.querySelectorAll('.dropdown-content');
    allDropdowns.forEach(dropdown => {
        dropdown.style.display = 'none';
    });
}

// Function to transliterate Arabic text for PDF compatibility
function transliterateArabic(text) {
    if (!text) return '';

    // Arabic to Latin transliteration map
    const arabicToLatin = {
        'أ': 'a', 'إ': 'i', 'آ': 'aa', 'ا': 'a', 'ب': 'b', 'ت': 't', 'ث': 'th',
        'ج': 'j', 'ح': 'h', 'خ': 'kh', 'د': 'd', 'ذ': 'dh', 'ر': 'r', 'ز': 'z',
        'س': 's', 'ش': 'sh', 'ص': 's', 'ض': 'd', 'ط': 't', 'ظ': 'z', 'ع': 'a',
        'غ': 'gh', 'ف': 'f', 'ق': 'q', 'ك': 'k', 'ل': 'l', 'م': 'm', 'ن': 'n',
        'ه': 'h', 'و': 'w', 'ي': 'y', 'ى': 'a', 'ة': 'h', 'ء': 'a',
        'َ': 'a', 'ُ': 'u', 'ِ': 'i', 'ً': 'an', 'ٌ': 'un', 'ٍ': 'in',
        'ْ': '', 'ّ': '', 'ـ': ''
    };

    // Common Arabic names and cities with their transliterations
    const commonNames = {
        'محمد': 'Mohammed',
        'أحمد': 'Ahmed',
        'علي': 'Ali',
        'فاطمة': 'Fatima',
        'عائشة': 'Aisha',
        'خديجة': 'Khadija',
        'عبدالله': 'Abdullah',
        'عبدالرحمن': 'Abdulrahman',
        'إبراهيم': 'Ibrahim',
        'يوسف': 'Youssef',
        'مريم': 'Mariam',
        'نورا': 'Nora',
        'سارة': 'Sarah',
        'هند': 'Hind',
        'خالد': 'Khalid',
        'سالم': 'Salem',
        'الرياض': 'Riyadh',
        'جدة': 'Jeddah',
        'الدمام': 'Dammam',
        'مكة': 'Makkah',
        'مكة المكرمة': 'Makkah Al-Mukarramah',
        'المدينة': 'Madinah',
        'المدينة المنورة': 'Madinah Al-Munawwarah',
        'الطائف': 'Taif',
        'أبها': 'Abha',
        'تبوك': 'Tabuk',
        'حائل': 'Hail',
        'القصيم': 'Qassim',
        'الخبر': 'Khobar',
        'الظهران': 'Dhahran'
    };

    // Check if it's a common name/city first
    const trimmedText = text.trim();
    if (commonNames[trimmedText]) {
        return commonNames[trimmedText];
    }

    // If not a common name, transliterate character by character
    let result = '';
    for (let i = 0; i < text.length; i++) {
        const char = text[i];
        if (arabicToLatin[char]) {
            result += arabicToLatin[char];
        } else if (char.match(/[a-zA-Z0-9\s\-_.,!@#$%^&*()]/)) {
            // Keep Latin characters, numbers, and common symbols
            result += char;
        } else {
            // For unknown characters, try to represent them
            result += char;
        }
    }

    // Clean up the result
    result = result.replace(/\s+/g, ' ').trim();

    // Capitalize first letter of each word
    result = result.split(' ').map(word =>
        word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(' ');

    return result || text; // Return original if transliteration failed
}

// Function to check if libraries are loaded
function checkLibraries() {
    const libraries = {
        jsPDF: typeof window.jsPDF !== 'undefined' || (typeof window.jspdf !== 'undefined' && window.jspdf.jsPDF),
        XLSX: typeof window.XLSX !== 'undefined',
        html2canvas: typeof window.html2canvas !== 'undefined'
    };

    console.log('📚 Library Status:', libraries);

    if (!libraries.jsPDF) {
        console.warn('⚠️ jsPDF not loaded properly');
        showNotification('تحذير: مكتبة PDF قد لا تعمل بشكل صحيح', 'warning');
    }

    if (!libraries.XLSX) {
        console.warn('⚠️ XLSX not loaded properly');
    }

    return libraries;
}

// Simple notification function if debtManager is not available
function showNotification(message, type = 'success') {
    // Try debtManager first
    if (window.debtManager && debtManager.showSuccess && type === 'success') {
        debtManager.showSuccess(message);
        return;
    }
    if (window.debtManager && debtManager.showError && type === 'error') {
        debtManager.showError(message);
        return;
    }

    // Fallback to simple toast
    const toast = document.createElement('div');
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : '#ef4444'};
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        z-index: 9999;
        font-family: Cairo, sans-serif;
        box-shadow: 0 4px 20px rgba(0,0,0,0.2);
        max-width: 400px;
        direction: rtl;
    `;
    toast.textContent = message;

    document.body.appendChild(toast);

    setTimeout(() => {
        if (document.body.contains(toast)) {
            document.body.removeChild(toast);
        }
    }, 4000);
}

// Test function removed to clean up interface

// Assign functions to window immediately
window.exportToCSV = exportToCSV;
window.exportToExcel = exportToExcel;
window.printPDF = printPDF;
window.importFromFile = importFromFile;
window.showNotification = showNotification;

console.log('🔧 Fix functions loaded and assigned to window object');
console.log('- exportToCSV:', typeof window.exportToCSV);
console.log('- exportToExcel:', typeof window.exportToExcel);
console.log('- printPDF:', typeof window.printPDF);
console.log('- importFromFile:', typeof window.importFromFile);

// Auto-test disabled to keep interface clean
