<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار استيراد Excel</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #7c3aed;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .file-input {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            width: 100%;
        }
        .btn {
            background: #7c3aed;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #6d28d9;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .success {
            background: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        .error {
            background: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
        .info {
            background: #dbeafe;
            border: 1px solid #3b82f6;
            color: #1e40af;
        }
        .sample-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 14px;
        }
        .sample-table th,
        .sample-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .sample-table th {
            background: #7c3aed;
            color: white;
        }
        .sample-table tr:nth-child(even) {
            background: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار استيراد Excel</h1>
        
        <div class="test-section">
            <h3>📊 نموذج البيانات المطلوبة</h3>
            <p>هذا هو الشكل الصحيح للبيانات التي يجب أن تكون في ملف Excel:</p>
            
            <table class="sample-table">
                <thead>
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>اسم العميل</th>
                        <th>المدينة</th>
                        <th>المبلغ</th>
                        <th>الملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>INV-001</td>
                        <td>أحمد محمد</td>
                        <td>الرياض</td>
                        <td>1500</td>
                        <td>فاتورة تجريبية</td>
                    </tr>
                    <tr>
                        <td>INV-002</td>
                        <td>فاطمة علي</td>
                        <td>جدة</td>
                        <td>2000</td>
                        <td>فاتورة عادية</td>
                    </tr>
                    <tr>
                        <td>INV-003</td>
                        <td>محمد سالم</td>
                        <td>الدمام</td>
                        <td>1200</td>
                        <td>فاتورة مستعجلة</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <h3>📁 اختبار استيراد ملف Excel</h3>
            <p>اختر ملف Excel (.xlsx) لاختبار عملية الاستيراد:</p>
            
            <input type="file" 
                   id="excelFile" 
                   class="file-input" 
                   accept=".xlsx,.xls"
                   onchange="testImport(event)">
            
            <div>
                <button class="btn" onclick="document.getElementById('excelFile').click()">
                    📂 اختيار ملف Excel
                </button>
                <button class="btn" onclick="clearResults()">
                    🗑️ مسح النتائج
                </button>
            </div>
            
            <div id="importResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>💡 نصائح للاختبار</h3>
            <div class="info result">
✅ للاختبار الناجح:
• استخدم الأعمدة: رقم الفاتورة، اسم العميل، المدينة، المبلغ، الملاحظات
• تأكد من وجود بيانات في كل الحقول المطلوبة
• استخدم أرقام فواتير فريدة
• احفظ الملف بصيغة .xlsx

❌ أخطاء شائعة:
• أسماء أعمدة خاطئة (مثل: رقم، اسم، مكان بدلاً من الأسماء الصحيحة)
• خلايا فارغة في الحقول المطلوبة
• مبالغ نصية (مثل: "ألف وخمسمائة" بدلاً من 1500)
• أرقام فواتير مكررة
            </div>
        </div>
    </div>

    <!-- Include required libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    
    <script>
        // Mock debtManager for testing
        const debtManager = {
            debts: [],
            showSuccess: function(message) {
                showResult(message, 'success');
            },
            showError: function(message) {
                showResult(message, 'error');
            },
            saveData: function() {
                console.log('Data saved (mock)');
            },
            updateStatistics: function() {
                console.log('Statistics updated (mock)');
            },
            displayRecords: function() {
                console.log('Records displayed (mock)');
            }
        };

        function showResult(message, type) {
            const resultDiv = document.getElementById('importResult');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
            resultDiv.style.display = 'block';
        }

        function clearResults() {
            const resultDiv = document.getElementById('importResult');
            resultDiv.style.display = 'none';
            resultDiv.textContent = '';
            
            // Clear file input
            document.getElementById('excelFile').value = '';
        }

        function testImport(event) {
            const file = event.target.files[0];
            if (!file) return;

            // Show loading
            showResult('🔄 جاري قراءة الملف...', 'info');

            // Use the same import logic as the main application
            importFromExcel(event, 'new');
        }

        // Include the import function from the main application
        // This is a simplified version for testing
        function importFromExcel(event, type) {
            const file = event.target.files[0];
            if (!file) return;

            // Validate file type
            const validTypes = [
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-excel'
            ];

            if (!validTypes.includes(file.type)) {
                debtManager.showError('❌ يرجى اختيار ملف Excel صحيح (.xlsx أو .xls)');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, {
                        type: 'array',
                        cellText: false,
                        cellDates: true,
                        codepage: 65001,
                        raw: false
                    });

                    const firstSheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[firstSheetName];

                    const jsonData = XLSX.utils.sheet_to_json(worksheet, {
                        header: 1,
                        defval: '',
                        blankrows: false
                    });

                    if (jsonData.length < 2) {
                        debtManager.showError('❌ الملف فارغ أو لا يحتوي على بيانات صالحة');
                        return;
                    }

                    const headers = jsonData[0];
                    let dataRows = jsonData.slice(1);

                    // Filter out summary rows
                    dataRows = dataRows.filter(row => {
                        if (!row || row.length === 0) return false;
                        const firstCell = row[0]?.toString().trim();
                        const secondCell = row[1]?.toString().trim();
                        
                        if (!firstCell && secondCell && 
                            (secondCell.includes('الإجمالي') || secondCell.includes('إجمالي') || 
                             secondCell.includes('Total') || secondCell.includes('فاتورة'))) {
                            return false;
                        }
                        return true;
                    });

                    if (dataRows.length === 0) {
                        debtManager.showError('❌ الملف لا يحتوي على بيانات صالحة بعد تصفية الصفوف');
                        return;
                    }

                    // Map headers
                    const headerMap = {};
                    headers.forEach((header, index) => {
                        if (!header) return;
                        
                        const originalHeader = header.toString().trim();
                        const normalizedHeader = originalHeader.toLowerCase();

                        if (originalHeader === 'رقم الفاتورة' || 
                            normalizedHeader.includes('فاتورة') || 
                            normalizedHeader.includes('invoice')) {
                            headerMap.invoiceNumber = index;
                        }
                        else if (originalHeader === 'اسم العميل' || 
                                 normalizedHeader.includes('عميل') || 
                                 normalizedHeader.includes('customer')) {
                            headerMap.customerName = index;
                        }
                        else if (originalHeader === 'المدينة' || 
                                 normalizedHeader.includes('مدينة') || 
                                 normalizedHeader.includes('city')) {
                            headerMap.city = index;
                        }
                        else if (originalHeader === 'المبلغ' || 
                                 normalizedHeader.includes('مبلغ') || 
                                 normalizedHeader.includes('amount')) {
                            headerMap.amount = index;
                        }
                        else if (originalHeader === 'الملاحظات' || 
                                 normalizedHeader.includes('ملاحظات') || 
                                 normalizedHeader.includes('notes')) {
                            headerMap.notes = index;
                        }
                    });

                    // Validate headers
                    const missingHeaders = [];
                    if (!headerMap.invoiceNumber) missingHeaders.push('رقم الفاتورة');
                    if (!headerMap.customerName) missingHeaders.push('اسم العميل');
                    if (!headerMap.city) missingHeaders.push('المدينة');
                    if (!headerMap.amount) missingHeaders.push('المبلغ');

                    if (missingHeaders.length > 0) {
                        const foundHeaders = headers.filter(h => h && h.toString().trim()).join('، ');
                        let errorMessage = `❌ الأعمدة المفقودة: ${missingHeaders.join('، ')}`;

                        if (foundHeaders) {
                            errorMessage += `\n\n📋 الأعمدة الموجودة في الملف:\n${foundHeaders}`;
                        }

                        errorMessage += `\n\n✅ الأعمدة المطلوبة:`;
                        errorMessage += `\n• رقم الفاتورة (مطلوب)`;
                        errorMessage += `\n• اسم العميل (مطلوب)`;
                        errorMessage += `\n• المدينة (مطلوب)`;
                        errorMessage += `\n• المبلغ (مطلوب)`;
                        errorMessage += `\n• الملاحظات (اختياري)`;

                        debtManager.showError(errorMessage);
                        console.log('Headers found:', headers);
                        console.log('Header mapping:', headerMap);
                        return;
                    }

                    // Process data
                    const processedData = [];
                    let successCount = 0;
                    let errorCount = 0;
                    const errors = [];

                    dataRows.forEach((row, index) => {
                        try {
                            if (!row || row.every(cell => !cell || cell.toString().trim() === '')) {
                                return;
                            }

                            const invoiceNumber = row[headerMap.invoiceNumber]?.toString().trim() || '';
                            const customerName = row[headerMap.customerName]?.toString().trim() || '';
                            const city = row[headerMap.city]?.toString().trim() || '';
                            const amountStr = row[headerMap.amount]?.toString().trim() || '0';
                            const notes = row[headerMap.notes]?.toString().trim() || '';

                            const amount = parseFloat(amountStr.replace(/[^\d.-]/g, ''));

                            if (!invoiceNumber) {
                                errors.push(`الصف ${index + 2}: رقم الفاتورة مفقود`);
                                errorCount++;
                                return;
                            }
                            if (!customerName) {
                                errors.push(`الصف ${index + 2}: اسم العميل مفقود`);
                                errorCount++;
                                return;
                            }
                            if (!city) {
                                errors.push(`الصف ${index + 2}: المدينة مفقودة`);
                                errorCount++;
                                return;
                            }
                            if (isNaN(amount) || amount <= 0) {
                                errors.push(`الصف ${index + 2}: المبلغ غير صحيح "${amountStr}"`);
                                errorCount++;
                                return;
                            }

                            const isDuplicate = processedData.some(debt => debt.invoiceNumber === invoiceNumber);
                            if (isDuplicate) {
                                errors.push(`الصف ${index + 2}: رقم الفاتورة "${invoiceNumber}" مكرر`);
                                errorCount++;
                                return;
                            }

                            processedData.push({
                                invoiceNumber,
                                customerName,
                                city,
                                amount,
                                notes
                            });

                            successCount++;

                        } catch (error) {
                            errors.push(`الصف ${index + 2}: خطأ في معالجة البيانات`);
                            errorCount++;
                        }
                    });

                    // Show results
                    let message = `✅ تم اختبار ${successCount} سجل بنجاح`;

                    if (errorCount > 0) {
                        message += `\n⚠️ تم العثور على ${errorCount} خطأ`;

                        if (errors.length > 0) {
                            message += '\n\nالأخطاء:';
                            const maxErrorsToShow = 5;
                            for (let i = 0; i < Math.min(errors.length, maxErrorsToShow); i++) {
                                message += `\n• ${errors[i]}`;
                            }

                            if (errors.length > maxErrorsToShow) {
                                message += `\n... و ${errors.length - maxErrorsToShow} خطأ آخر`;
                            }
                        }
                    }

                    if (successCount > 0) {
                        const totalAmount = processedData.reduce((sum, item) => sum + item.amount, 0);
                        message += `\n\n📊 إجمالي المبلغ: ${totalAmount.toLocaleString()} ريال`;
                        message += `\n📋 عدد السجلات الصالحة: ${successCount}`;
                    }

                    if (errorCount === 0 && successCount > 0) {
                        message += `\n\n🎉 الملف جاهز للاستيراد في النظام الأصلي!`;
                        debtManager.showSuccess(message);
                    } else if (successCount > 0) {
                        debtManager.showError(message);
                    } else {
                        debtManager.showError('❌ لم يتم العثور على بيانات صالحة في الملف');
                    }

                } catch (error) {
                    console.error('Import Error:', error);
                    debtManager.showError('❌ خطأ في قراءة الملف. تأكد من أن الملف بصيغة Excel صحيحة');
                }
            };

            reader.onerror = function() {
                debtManager.showError('❌ خطأ في قراءة الملف');
            };

            reader.readAsArrayBuffer(file);
        }
    </script>
</body>
</html>
