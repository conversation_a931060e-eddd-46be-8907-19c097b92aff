@echo off
chcp 65001 >nul
title نظام إدارة ديون العملاء - Customer Debt Management System
color 0A

echo ========================================
echo    نظام إدارة ديون العملاء
echo    Customer Debt Management System
echo ========================================
echo.

echo [1/5] تحقق من تثبيت Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.7 أو أحدث من: https://python.org
    echo.
    pause
    exit /b 1
)
echo ✅ Python مثبت بنجاح

echo.
echo [2/5] التحقق من pip...
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: pip غير متوفر
    echo يرجى إعادة تثبيت Python مع pip
    pause
    exit /b 1
)
echo ✅ pip متوفر

echo.
echo [3/5] تثبيت المتطلبات...
cd /d "%~dp0python"
echo تثبيت Flask و المكتبات المطلوبة...
pip install -r requirements.txt --quiet --disable-pip-version-check
if errorlevel 1 (
    echo ❌ خطأ في تثبيت المتطلبات
    echo يرجى التحقق من الاتصال بالإنترنت وإعادة المحاولة
    echo.
    pause
    exit /b 1
)
echo ✅ تم تثبيت المتطلبات بنجاح

echo.
echo [4/5] إعداد قاعدة البيانات...
cd /d "%~dp0"
if not exist "data" mkdir "data"
if not exist "data\customers.json" (
    echo {"debts": []} > "data\customers.json"
    echo ✅ تم إنشاء ملف قاعدة البيانات
) else (
    echo ✅ ملف قاعدة البيانات موجود
)

echo.
echo [5/5] تشغيل الخادم...
echo 🌐 سيتم فتح النظام على: http://localhost:5000
echo 📊 لوحة الإدارة جاهزة للاستخدام
echo 💾 البيانات محفوظة في: %~dp0data\customers.json
echo.
echo ⚠️  للإيقاف اضغط Ctrl+C
echo.

echo انتظار 3 ثوان قبل فتح المتصفح...
timeout /t 3 >nul

echo فتح المتصفح...
start "" "http://localhost:5000"

echo تشغيل الخادم...
cd /d "%~dp0python"
python app.py

echo.
echo ========================================
echo تم إيقاف الخادم بنجاح
echo شكراً لاستخدام نظام إدارة ديون العملاء
echo ========================================
pause
