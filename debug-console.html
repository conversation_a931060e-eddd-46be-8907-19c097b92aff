<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص شامل للمشاكل</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-size: 12px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🔍 تشخيص شامل لمشاكل التصدير والاستيراد</h1>
    
    <div class="container">
        <h2>📊 فحص حالة النظام</h2>
        <button onclick="runFullDiagnostic()">تشخيص شامل</button>
        <button onclick="clearLog()">مسح السجل</button>
        <div id="diagnosticResults"></div>
    </div>
    
    <div class="container">
        <h2>🧪 اختبار الوظائف</h2>
        <div class="test-grid">
            <button onclick="testLibraries()">اختبار المكتبات</button>
            <button onclick="testDebtManager()">اختبار debtManager</button>
            <button onclick="testDropdowns()">اختبار القوائم</button>
            <button onclick="testPDFFunction()">اختبار PDF</button>
            <button onclick="testExcelFunction()">اختبار Excel</button>
            <button onclick="testImportFunction()">اختبار الاستيراد</button>
        </div>
    </div>
    
    <div class="container">
        <h2>📝 سجل التشخيص</h2>
        <div id="log" class="log"></div>
    </div>

    <!-- نفس المكتبات المستخدمة في النظام الأصلي -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdn.sheetjs.com/xlsx-0.20.1/package/dist/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            logDiv.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`${prefix} ${message}`);
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        function addResult(message, type) {
            const resultsDiv = document.getElementById('diagnosticResults');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = message;
            resultsDiv.appendChild(div);
        }

        function runFullDiagnostic() {
            document.getElementById('diagnosticResults').innerHTML = '';
            log('🔄 بدء التشخيص الشامل...');
            
            // فحص المكتبات
            testLibraries();
            
            // فحص debtManager
            setTimeout(() => testDebtManager(), 500);
            
            // فحص الوظائف
            setTimeout(() => testDropdowns(), 1000);
            
            log('✅ انتهى التشخيص الشامل');
        }

        function testLibraries() {
            log('🔍 فحص المكتبات...');
            
            // فحص jsPDF
            if (typeof window.jspdf !== 'undefined') {
                log('✅ مكتبة jsPDF محملة', 'success');
                addResult('✅ مكتبة jsPDF محملة', 'success');
                
                // فحص تفصيلي
                try {
                    const { jsPDF } = window.jspdf;
                    const testDoc = new jsPDF();
                    log('✅ يمكن إنشاء مستند PDF', 'success');
                } catch (error) {
                    log('❌ خطأ في إنشاء مستند PDF: ' + error.message, 'error');
                    addResult('❌ خطأ في إنشاء مستند PDF', 'error');
                }
            } else {
                log('❌ مكتبة jsPDF غير محملة', 'error');
                addResult('❌ مكتبة jsPDF غير محملة', 'error');
            }

            // فحص XLSX
            if (typeof window.XLSX !== 'undefined') {
                log('✅ مكتبة XLSX محملة', 'success');
                addResult('✅ مكتبة XLSX محملة', 'success');
                
                // فحص تفصيلي
                try {
                    const testData = [{'test': 'value'}];
                    const wb = XLSX.utils.book_new();
                    const ws = XLSX.utils.json_to_sheet(testData);
                    log('✅ يمكن إنشاء workbook', 'success');
                } catch (error) {
                    log('❌ خطأ في إنشاء workbook: ' + error.message, 'error');
                    addResult('❌ خطأ في إنشاء workbook', 'error');
                }
            } else {
                log('❌ مكتبة XLSX غير محملة', 'error');
                addResult('❌ مكتبة XLSX غير محملة', 'error');
            }

            // فحص html2canvas
            if (typeof window.html2canvas !== 'undefined') {
                log('✅ مكتبة html2canvas محملة', 'success');
                addResult('✅ مكتبة html2canvas محملة', 'success');
            } else {
                log('❌ مكتبة html2canvas غير محملة', 'error');
                addResult('❌ مكتبة html2canvas غير محملة', 'error');
            }
        }

        function testDebtManager() {
            log('🔍 فحص debtManager...');
            
            if (typeof window.debtManager !== 'undefined') {
                log('✅ debtManager موجود', 'success');
                addResult('✅ debtManager موجود', 'success');
                
                // فحص الخصائص
                if (window.debtManager.debts) {
                    log(`✅ debts موجود (${window.debtManager.debts.length} عنصر)`, 'success');
                } else {
                    log('❌ debts غير موجود', 'error');
                    addResult('❌ debts غير موجود', 'error');
                }
                
                if (window.debtManager.previousDebts) {
                    log(`✅ previousDebts موجود (${window.debtManager.previousDebts.length} عنصر)`, 'success');
                } else {
                    log('❌ previousDebts غير موجود', 'error');
                    addResult('❌ previousDebts غير موجود', 'error');
                }
                
                // فحص الوظائف
                if (typeof window.debtManager.showSuccess === 'function') {
                    log('✅ showSuccess موجود', 'success');
                } else {
                    log('❌ showSuccess غير موجود', 'error');
                    addResult('❌ showSuccess غير موجود', 'error');
                }
                
                if (typeof window.debtManager.showError === 'function') {
                    log('✅ showError موجود', 'success');
                } else {
                    log('❌ showError غير موجود', 'error');
                    addResult('❌ showError غير موجود', 'error');
                }
                
            } else {
                log('❌ debtManager غير موجود', 'error');
                addResult('❌ debtManager غير موجود', 'error');
            }
        }

        function testDropdowns() {
            log('🔍 فحص وظائف القوائم المنسدلة...');
            
            if (typeof window.toggleDropdown === 'function') {
                log('✅ toggleDropdown موجود', 'success');
                addResult('✅ toggleDropdown موجود', 'success');
            } else {
                log('❌ toggleDropdown غير موجود', 'error');
                addResult('❌ toggleDropdown غير موجود', 'error');
            }
            
            if (typeof window.closeAllDropdowns === 'function') {
                log('✅ closeAllDropdowns موجود', 'success');
                addResult('✅ closeAllDropdowns موجود', 'success');
            } else {
                log('❌ closeAllDropdowns غير موجود', 'error');
                addResult('❌ closeAllDropdowns غير موجود', 'error');
            }
        }

        function testPDFFunction() {
            log('🔍 اختبار وظيفة PDF...');
            
            if (typeof window.printPDF === 'function') {
                log('✅ printPDF موجود', 'success');
                addResult('✅ printPDF موجود', 'success');
            } else {
                log('❌ printPDF غير موجود', 'error');
                addResult('❌ printPDF غير موجود', 'error');
            }
        }

        function testExcelFunction() {
            log('🔍 اختبار وظيفة Excel...');
            
            if (typeof window.exportToExcel === 'function') {
                log('✅ exportToExcel موجود', 'success');
                addResult('✅ exportToExcel موجود', 'success');
            } else {
                log('❌ exportToExcel غير موجود', 'error');
                addResult('❌ exportToExcel غير موجود', 'error');
            }
        }

        function testImportFunction() {
            log('🔍 اختبار وظيفة الاستيراد...');
            
            if (typeof window.importFromExcel === 'function') {
                log('✅ importFromExcel موجود', 'success');
                addResult('✅ importFromExcel موجود', 'success');
            } else {
                log('❌ importFromExcel غير موجود', 'error');
                addResult('❌ importFromExcel غير موجود', 'error');
            }
        }

        // تشغيل التشخيص عند التحميل
        window.addEventListener('load', function() {
            log('🎯 صفحة التشخيص جاهزة');
            
            // محاولة تحميل الملفات من النظام الأصلي
            try {
                // تحميل app.js
                const appScript = document.createElement('script');
                appScript.src = '../javascript/app.js';
                appScript.onload = function() {
                    log('✅ تم تحميل app.js', 'success');
                };
                appScript.onerror = function() {
                    log('❌ فشل تحميل app.js', 'error');
                };
                document.head.appendChild(appScript);
                
                // تحميل export-import.js
                const exportScript = document.createElement('script');
                exportScript.src = '../javascript/export-import.js';
                exportScript.onload = function() {
                    log('✅ تم تحميل export-import.js', 'success');
                    
                    // تشغيل التشخيص بعد تحميل الملفات
                    setTimeout(runFullDiagnostic, 1000);
                };
                exportScript.onerror = function() {
                    log('❌ فشل تحميل export-import.js', 'error');
                };
                document.head.appendChild(exportScript);
                
            } catch (error) {
                log('❌ خطأ في تحميل الملفات: ' + error.message, 'error');
            }
        });
    </script>
</body>
</html>
