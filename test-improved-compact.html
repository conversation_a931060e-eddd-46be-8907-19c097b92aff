<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التصميم المحسن والمدمج</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 50px rgba(0,0,0,0.4);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 42px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .improved-banner {
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            border: 4px solid #3b82f6;
            border-radius: 20px;
            padding: 40px;
            margin: 30px 0;
            box-shadow: 0 10px 30px rgba(59, 130, 246, 0.4);
            text-align: center;
        }
        
        /* نسخ الأنماط المحسنة الجديدة */
        .stats-section {
            background: #f8fafc;
            border-radius: 8px;
            padding: 12px;
            border: 1px solid #e2e8f0;
            margin: 20px 0;
        }
        .stats-section h5 {
            margin: 0 0 10px 0;
            color: #1e293b;
            font-weight: 600;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        .top-customers-list {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        .top-customer-item {
            background: #ffffff;
            border-radius: 6px;
            padding: 5px 8px;
            display: flex;
            align-items: center;
            gap: 8px;
            border: 1px solid #e5e7eb;
            transition: all 0.2s ease;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
            min-height: 32px;
        }
        .top-customer-item:hover {
            background: #f9fafb;
            border-color: #9ca3af;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .customer-rank {
            background: #3b82f6;
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 0.7rem;
            flex-shrink: 0;
        }
        .customer-info {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
            min-width: 0;
        }
        .customer-details {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 6px;
            flex: 1;
            min-width: 0;
        }
        .customer-stats {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 1px;
            flex-shrink: 0;
        }
        .customer-name {
            font-weight: 600;
            color: #1f2937;
            font-size: 0.8rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 120px;
        }
        .customer-city {
            font-size: 0.7rem;
            color: #6b7280;
            display: flex;
            align-items: center;
            gap: 3px;
            white-space: nowrap;
        }
        .customer-city::before {
            content: "📍";
            font-size: 0.7rem;
        }
        .customer-amount {
            font-weight: 700;
            color: #059669;
            font-size: 0.75rem;
            background: #f0fdf4;
            padding: 2px 4px;
            border-radius: 3px;
            border: 1px solid #bbf7d0;
            white-space: nowrap;
        }
        .customer-invoices {
            font-size: 0.65rem;
            color: #6b7280;
            white-space: nowrap;
        }
        .customer-invoices-list {
            font-size: 0.6rem;
            color: #6b7280;
            display: inline-block;
            margin: 0;
            font-style: italic;
            background: #f9fafb;
            padding: 1px 3px;
            border-radius: 2px;
            border: 1px solid #e5e7eb;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 80px;
        }
        
        .comparison-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 30px 0;
        }
        .before-after {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            border: 2px solid #dee2e6;
        }
        .before {
            border-color: #dc3545;
        }
        .after {
            border-color: #28a745;
        }
        .before h3 {
            color: #dc3545;
        }
        .after h3 {
            color: #28a745;
        }
        .old-item {
            background: white;
            padding: 8px 12px;
            margin: 6px 0;
            border-radius: 8px;
            min-height: 40px;
            display: flex;
            align-items: center;
            gap: 12px;
            border: 1px solid #d1d5db;
        }
        .old-rank {
            background: #3b82f6;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
        }
        
        .improvement-list {
            list-style: none;
            padding: 0;
        }
        .improvement-list li {
            background: #e8f5e8;
            margin: 15px 0;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #28a745;
        }
        .improvement-list li::before {
            content: "✅ ";
            font-weight: bold;
            color: #28a745;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 التصميم المحسن والمدمج</h1>
        
        <div class="improved-banner">
            <h2 style="color: #1e40af; font-size: 32px; margin-bottom: 20px;">⚡ تحسين الشكل وتقليل الحجم</h2>
            <p style="font-size: 18px; color: #1e40af;">حاوية أصغر مع شكل محسن وتنظيم أفضل</p>
        </div>

        <ul class="improvement-list">
            <li><strong>تقليل حجم الحاوية:</strong> من 40px إلى 32px ارتفاع</li>
            <li><strong>تصغير العناصر:</strong> رقم الترتيب من 24px إلى 20px</li>
            <li><strong>تقليل المساحات:</strong> padding وgap أصغر</li>
            <li><strong>تحسين الخطوط:</strong> أحجام متوازنة وواضحة</li>
            <li><strong>تبسيط التأثيرات:</strong> hover بسيط وسريع</li>
            <li><strong>تحسين التنظيم:</strong> توزيع محسن للمساحة</li>
        </ul>

        <div class="comparison-section">
            <div class="before-after before">
                <h3>❌ قبل التحسين (حجم أكبر)</h3>
                <div style="background: #f8fafc; padding: 12px; border-radius: 8px;">
                    <div class="old-item">
                        <div class="old-rank">1</div>
                        <div style="flex: 1; display: flex; gap: 12px; align-items: center;">
                            <span style="font-size: 0.9rem; font-weight: 600; color: #1f2937; max-width: 140px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">أحمد محمد العلي</span>
                            <span style="font-size: 0.8rem; color: #6b7280;">📍 الرياض</span>
                            <span style="font-size: 0.7rem; color: #6b7280; background: #f9fafb; padding: 2px 4px; border-radius: 3px; border: 1px solid #e5e7eb;">001, 002</span>
                        </div>
                        <div style="display: flex; flex-direction: column; align-items: flex-end;">
                            <span style="font-size: 0.85rem; font-weight: 700; color: #059669; background: #f0fdf4; padding: 3px 6px; border-radius: 4px; border: 1px solid #bbf7d0;">15,750.75 ر.س</span>
                            <span style="font-size: 0.75rem; color: #6b7280;">2 فاتورة</span>
                        </div>
                    </div>
                </div>
                <p style="color: #dc3545; font-size: 12px;">ارتفاع 40px + مساحات كبيرة</p>
            </div>

            <div class="before-after after">
                <h3>✅ بعد التحسين (حجم مدمج)</h3>
                <div class="stats-section" style="margin: 0;">
                    <div class="top-customers-list">
                        <div class="top-customer-item">
                            <span class="customer-rank">1</span>
                            <div class="customer-info">
                                <div class="customer-details">
                                    <span class="customer-name" title="أحمد محمد العلي">أحمد محمد العلي</span>
                                    <span class="customer-city">الرياض</span>
                                    <span class="customer-invoices-list" title="الفواتير: 001, 002">001, 002</span>
                                </div>
                                <div class="customer-stats">
                                    <span class="customer-amount">15,750.75 ر.س</span>
                                    <span class="customer-invoices">2 فاتورة</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <p style="color: #28a745; font-size: 12px;">ارتفاع 32px + مساحات مدمجة</p>
            </div>
        </div>

        <div class="stats-section">
            <h5><i class="fas fa-crown" style="color: #fbbf24;"></i> أكبر العملاء - التصميم المحسن</h5>
            <div class="top-customers-list">
                <div class="top-customer-item">
                    <span class="customer-rank">1</span>
                    <div class="customer-info">
                        <div class="customer-details">
                            <span class="customer-name" title="أحمد محمد العلي الطويل جداً">أحمد محمد العلي الطويل جداً</span>
                            <span class="customer-city">الرياض</span>
                            <span class="customer-invoices-list" title="الفواتير: 001, 002, 003, 004">001, 002, 003, 004</span>
                        </div>
                        <div class="customer-stats">
                            <span class="customer-amount">15,750.75 ر.س</span>
                            <span class="customer-invoices">4 فاتورة</span>
                        </div>
                    </div>
                </div>

                <div class="top-customer-item">
                    <span class="customer-rank">2</span>
                    <div class="customer-info">
                        <div class="customer-details">
                            <span class="customer-name" title="فاطمة عبدالله الزهراني">فاطمة عبدالله الزهراني</span>
                            <span class="customer-city">جدة</span>
                            <span class="customer-invoices-list" title="الفواتير: 005, 006">005, 006</span>
                        </div>
                        <div class="customer-stats">
                            <span class="customer-amount">12,500.50 ر.س</span>
                            <span class="customer-invoices">2 فاتورة</span>
                        </div>
                    </div>
                </div>

                <div class="top-customer-item">
                    <span class="customer-rank">3</span>
                    <div class="customer-info">
                        <div class="customer-details">
                            <span class="customer-name" title="محمد سالم القحطاني">محمد سالم القحطاني</span>
                            <span class="customer-city">الدمام</span>
                            <span class="customer-invoices-list" title="الفواتير: 007">007</span>
                        </div>
                        <div class="customer-stats">
                            <span class="customer-amount">8,750.25 ر.س</span>
                            <span class="customer-invoices">1 فاتورة</span>
                        </div>
                    </div>
                </div>

                <div class="top-customer-item">
                    <span class="customer-rank">4</span>
                    <div class="customer-info">
                        <div class="customer-details">
                            <span class="customer-name" title="خالد أحمد المطيري">خالد أحمد المطيري</span>
                            <span class="customer-city">المدينة المنورة</span>
                            <span class="customer-invoices-list" title="الفواتير: 008, 009, 010">008, 009, 010</span>
                        </div>
                        <div class="customer-stats">
                            <span class="customer-amount">7,200.00 ر.س</span>
                            <span class="customer-invoices">3 فاتورة</span>
                        </div>
                    </div>
                </div>

                <div class="top-customer-item">
                    <span class="customer-rank">5</span>
                    <div class="customer-info">
                        <div class="customer-details">
                            <span class="customer-name" title="نورا عبدالله الغامدي">نورا عبدالله الغامدي</span>
                            <span class="customer-city">مكة المكرمة</span>
                            <span class="customer-invoices-list" title="الفواتير: 011">011</span>
                        </div>
                        <div class="customer-stats">
                            <span class="customer-amount">5,800.25 ر.س</span>
                            <span class="customer-invoices">1 فاتورة</span>
                        </div>
                    </div>
                </div>

                <div class="top-customer-item">
                    <span class="customer-rank">6</span>
                    <div class="customer-info">
                        <div class="customer-details">
                            <span class="customer-name" title="سارة أحمد الشهري">سارة أحمد الشهري</span>
                            <span class="customer-city">أبها</span>
                            <span class="customer-invoices-list" title="الفواتير: 012, 013">012, 013</span>
                        </div>
                        <div class="customer-stats">
                            <span class="customer-amount">4,500.00 ر.س</span>
                            <span class="customer-invoices">2 فاتورة</span>
                        </div>
                    </div>
                </div>

                <div class="top-customer-item">
                    <span class="customer-rank">7</span>
                    <div class="customer-info">
                        <div class="customer-details">
                            <span class="customer-name" title="عبدالله محمد الحربي">عبدالله محمد الحربي</span>
                            <span class="customer-city">الطائف</span>
                            <span class="customer-invoices-list" title="الفواتير: 014">014</span>
                        </div>
                        <div class="customer-stats">
                            <span class="customer-amount">3,200.75 ر.س</span>
                            <span class="customer-invoices">1 فاتورة</span>
                        </div>
                    </div>
                </div>

                <div class="top-customer-item">
                    <span class="customer-rank">8</span>
                    <div class="customer-info">
                        <div class="customer-details">
                            <span class="customer-name" title="مريم سالم العتيبي">مريم سالم العتيبي</span>
                            <span class="customer-city">بريدة</span>
                            <span class="customer-invoices-list" title="الفواتير: 015, 016, 017">015, 016, 017</span>
                        </div>
                        <div class="customer-stats">
                            <span class="customer-amount">2,800.50 ر.س</span>
                            <span class="customer-invoices">3 فاتورة</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div style="background: #f8f9fa; padding: 20px; border-radius: 15px; margin: 30px 0;">
            <h3>📊 مقارنة التحسينات:</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
                <div>
                    <h4 style="color: #dc3545;">قبل التحسين:</h4>
                    <ul style="font-size: 14px; color: #666;">
                        <li>ارتفاع الحاوية: 40px</li>
                        <li>رقم الترتيب: 24px</li>
                        <li>مساحات كبيرة: 12px</li>
                        <li>خطوط كبيرة: 0.8-0.9rem</li>
                        <li>مسافات واسعة: 6px</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color: #28a745;">بعد التحسين:</h4>
                    <ul style="font-size: 14px; color: #666;">
                        <li>ارتفاع الحاوية: 32px</li>
                        <li>رقم الترتيب: 20px</li>
                        <li>مساحات مدمجة: 8px</li>
                        <li>خطوط متوازنة: 0.6-0.8rem</li>
                        <li>مسافات مدمجة: 4px</li>
                    </ul>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; padding: 20px; background: #dbeafe; border-radius: 15px;">
            <h3 style="color: #1e40af;">🎉 تم تحسين الشكل وتقليل الحجم!</h3>
            <p style="color: #1e40af; font-size: 18px;">حاوية مدمجة مع شكل محسن وتنظيم أفضل للبيانات</p>
            <div style="margin-top: 20px;">
                <span style="background: #3b82f6; color: white; padding: 10px 20px; border-radius: 25px; font-weight: bold;">
                    توازن مثالي بين الحجم والوضوح
                </span>
            </div>
        </div>
    </div>
</body>
</html>
