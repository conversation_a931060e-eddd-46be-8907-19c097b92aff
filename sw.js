// Service Worker for Offline Functionality
// نظام إدارة ديون العملاء - Service Worker

const CACHE_NAME = 'debt-manager-v1.0.0';
const OFFLINE_URL = './offline.html';

// الملفات المطلوبة للعمل بدون إنترنت
const CACHE_FILES = [
    './',
    './index.html',
    './css/style.css',
    './javascript/app.js',
    './javascript/export-import.js',
    './libs/jspdf.min.js',
    './manifest.json',
    './offline.html',
    // الخطوط المحلية
    './fonts/arabic-font.woff2',
    // الأيقونات
    './icons/icon-192x192.png',
    './icons/icon-512x512.png'
];

// تثبيت Service Worker
self.addEventListener('install', (event) => {
    console.log('🔧 Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then((cache) => {
                console.log('📦 Service Worker: Caching files...');
                return cache.addAll(CACHE_FILES);
            })
            .then(() => {
                console.log('✅ Service Worker: All files cached successfully');
                return self.skipWaiting();
            })
            .catch((error) => {
                console.error('❌ Service Worker: Caching failed:', error);
            })
    );
});

// تفعيل Service Worker
self.addEventListener('activate', (event) => {
    console.log('🚀 Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys()
            .then((cacheNames) => {
                return Promise.all(
                    cacheNames.map((cacheName) => {
                        if (cacheName !== CACHE_NAME) {
                            console.log('🗑️ Service Worker: Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('✅ Service Worker: Activated successfully');
                return self.clients.claim();
            })
    );
});

// التعامل مع الطلبات
self.addEventListener('fetch', (event) => {
    // تجاهل الطلبات غير HTTP/HTTPS
    if (!event.request.url.startsWith('http')) {
        return;
    }

    // استراتيجية Cache First للملفات الثابتة
    if (CACHE_FILES.includes(event.request.url.replace(self.location.origin, '.'))) {
        event.respondWith(
            caches.match(event.request)
                .then((response) => {
                    if (response) {
                        console.log('📦 Service Worker: Serving from cache:', event.request.url);
                        return response;
                    }
                    
                    return fetch(event.request)
                        .then((response) => {
                            // تخزين الاستجابة في الكاش
                            if (response.status === 200) {
                                const responseClone = response.clone();
                                caches.open(CACHE_NAME)
                                    .then((cache) => {
                                        cache.put(event.request, responseClone);
                                    });
                            }
                            return response;
                        });
                })
                .catch(() => {
                    // إذا فشل كل شيء، أرجع صفحة offline
                    if (event.request.destination === 'document') {
                        return caches.match(OFFLINE_URL);
                    }
                })
        );
    } else {
        // للطلبات الأخرى، جرب الشبكة أولاً ثم الكاش
        event.respondWith(
            fetch(event.request)
                .then((response) => {
                    // تخزين الاستجابات الناجحة
                    if (response.status === 200 && event.request.method === 'GET') {
                        const responseClone = response.clone();
                        caches.open(CACHE_NAME)
                            .then((cache) => {
                                cache.put(event.request, responseClone);
                            });
                    }
                    return response;
                })
                .catch(() => {
                    // إذا فشلت الشبكة، جرب الكاش
                    return caches.match(event.request)
                        .then((response) => {
                            if (response) {
                                return response;
                            }
                            
                            // إذا كان طلب صفحة، أرجع صفحة offline
                            if (event.request.destination === 'document') {
                                return caches.match(OFFLINE_URL);
                            }
                        });
                })
        );
    }
});

// التعامل مع رسائل من التطبيق الرئيسي
self.addEventListener('message', (event) => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
    
    if (event.data && event.data.type === 'GET_VERSION') {
        event.ports[0].postMessage({ version: CACHE_NAME });
    }
});

// تحديث الكاش عند الحاجة
self.addEventListener('sync', (event) => {
    if (event.tag === 'background-sync') {
        console.log('🔄 Service Worker: Background sync triggered');
        // يمكن إضافة منطق مزامنة البيانات هنا
    }
});

// إشعار عند تحديث التطبيق
self.addEventListener('updatefound', () => {
    console.log('🆕 Service Worker: Update found!');
});

console.log('✅ Service Worker: Loaded successfully');
