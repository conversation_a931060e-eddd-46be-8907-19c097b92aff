<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح طباعة التحليلات</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON><PERSON>, Aria<PERSON>, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 50px rgba(0,0,0,0.4);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 42px;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .fix-banner {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            border: 4px solid #28a745;
            border-radius: 20px;
            padding: 40px;
            margin: 30px 0;
            box-shadow: 0 10px 30px rgba(40, 167, 69, 0.4);
            text-align: center;
        }
        .test-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 25px 40px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 24px;
            margin: 20px 10px;
            transition: all 0.3s ease;
            font-weight: bold;
            box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
            display: inline-block;
            text-align: center;
            min-width: 250px;
        }
        .test-btn:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 30px rgba(231, 76, 60, 0.6);
        }
        .status {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 16px;
        }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .buttons-container {
            text-align: center;
            margin: 30px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إصلاح طباعة التحليلات</h1>
        
        <div class="fix-banner">
            <h2 style="color: #155724; font-size: 32px; margin-bottom: 20px;">✅ تم إصلاح مشكلة الصفحة السوداء</h2>
            <p style="font-size: 18px; color: #155724;">استبدال html2canvas بالحل الجديد المضمون</p>
        </div>

        <div id="status" class="status">
            <div class="success">✅ الحل الجديد جاهز للاختبار</div>
        </div>

        <div class="buttons-container">
            <button class="test-btn" onclick="testNewCustomers()">
                👥 اختبار العملاء الجدد
            </button>
            <button class="test-btn" onclick="testInactiveCustomers()">
                😴 اختبار العملاء غير النشطين
            </button>
            <button class="test-btn" onclick="testStatistics()">
                📊 اختبار الإحصائيات
            </button>
        </div>
    </div>

    <script>
        // محاكاة وظائف التحليلات
        const mockDebtManager = {
            // وظيفة فتح صفحة طباعة للتحليلات - الحل الجديد
            openAnalyticsPrintPage: function(data, headers, title) {
                console.log('🖨️ فتح صفحة طباعة التحليلات...');
                
                try {
                    const printHTML = this.createAnalyticsHTML(data, headers, title);
                    
                    if (!printHTML) {
                        throw new Error('فشل في إنشاء HTML للطباعة');
                    }
                    
                    // فتح نافذة جديدة
                    const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
                    
                    if (!printWindow) {
                        throw new Error('فشل في فتح نافذة الطباعة - تأكد من السماح للنوافذ المنبثقة');
                    }
                    
                    // كتابة HTML في النافذة الجديدة
                    printWindow.document.write(printHTML);
                    printWindow.document.close();
                    
                    // التركيز على النافذة الجديدة
                    printWindow.focus();
                    
                    console.log('✅ تم فتح صفحة طباعة التحليلات بنجاح');
                    return true;
                    
                } catch (error) {
                    console.error('❌ خطأ في فتح صفحة طباعة التحليلات:', error);
                    alert('فشل في فتح صفحة الطباعة: ' + error.message);
                    return false;
                }
            },

            // إنشاء HTML للتحليلات
            createAnalyticsHTML: function(data, headers, title) {
                try {
                    // التاريخ الحالي
                    const currentDate = new Date().toLocaleDateString('ar-SA', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        weekday: 'long'
                    });
                    
                    // إنشاء HTML للطباعة
                    let printHTML = '<!DOCTYPE html><html lang="ar" dir="rtl"><head><meta charset="UTF-8"><title>' + title + '</title>';
                    printHTML += '<style>@media print {@page {size: A4; margin: 15mm;} body {margin: 0; padding: 0; font-family: Arial, sans-serif; font-size: 12pt; direction: rtl; text-align: right;} .no-print {display: none !important;}}';
                    printHTML += 'body {font-family: Arial, sans-serif; direction: rtl; text-align: right; margin: 0; padding: 20px; background: #fff; color: #000; font-size: 14px;}';
                    printHTML += '.header {text-align: center; margin-bottom: 30px; border-bottom: 3px solid #2c3e50; padding-bottom: 20px;}';
                    printHTML += '.title {font-size: 28px; font-weight: bold; color: #2c3e50; margin-bottom: 10px;}';
                    printHTML += '.date {font-size: 16px; color: #666; margin-bottom: 10px;}';
                    printHTML += '.analytics-table {width: 100%; border-collapse: collapse; margin: 20px 0; font-size: 14px;}';
                    printHTML += '.analytics-table th {background: #2c3e50; color: #fff; padding: 15px 10px; text-align: center; font-weight: bold; border: 2px solid #34495e; font-size: 16px;}';
                    printHTML += '.analytics-table td {padding: 12px 10px; text-align: center; border: 1px solid #ddd; vertical-align: middle;}';
                    printHTML += '.analytics-table tr:nth-child(even) {background: #f8f9fa;} .analytics-table tr:nth-child(odd) {background: #fff;}';
                    printHTML += '.print-button {background: #27ae60; color: white; border: none; padding: 15px 30px; font-size: 18px; border-radius: 8px; cursor: pointer; margin: 20px; font-weight: bold;}';
                    printHTML += '.buttons-container {text-align: center; margin: 30px 0; padding: 20px; background: #f8f9fa; border-radius: 10px;}';
                    printHTML += '@media print {.buttons-container {display: none !important;}}</style></head><body>';
                    
                    printHTML += '<div class="buttons-container no-print">';
                    printHTML += '<button class="print-button" onclick="window.print()">🖨️ طباعة</button>';
                    printHTML += '<button class="print-button" onclick="window.print();" style="background: #e74c3c;">💾 حفظ PDF</button>';
                    printHTML += '<button class="print-button" onclick="window.close()" style="background: #95a5a6;">❌ إغلاق</button></div>';
                    
                    printHTML += '<div class="header"><div class="title">' + title + '</div><div class="date">' + currentDate + '</div></div>';
                    
                    printHTML += '<table class="analytics-table"><thead><tr>';
                    
                    // إضافة العناوين
                    headers.forEach(header => {
                        printHTML += '<th>' + header + '</th>';
                    });
                    printHTML += '</tr></thead><tbody>';

                    // إضافة البيانات
                    data.forEach((row, index) => {
                        printHTML += '<tr>';
                        if (Array.isArray(row)) {
                            row.forEach(cell => {
                                printHTML += '<td>' + (cell || '') + '</td>';
                            });
                        } else {
                            // إذا كانت البيانات كائن
                            headers.forEach(header => {
                                printHTML += '<td>' + (row[header] || '') + '</td>';
                            });
                        }
                        printHTML += '</tr>';
                    });

                    printHTML += '</tbody></table></body></html>';

                    return printHTML;
                    
                } catch (error) {
                    console.error('❌ خطأ في إنشاء HTML للتحليلات:', error);
                    return null;
                }
            }
        };

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            statusDiv.innerHTML = `<div class="${className}">${message}</div>`;
            console.log(message);
        }

        function testNewCustomers() {
            updateStatus('🔄 اختبار طباعة العملاء الجدد...', 'warning');
            
            const data = [
                ['أحمد محمد العلي', 'الرياض', '001', '1,500.75 ر.س'],
                ['فاطمة عبدالله الزهراني', 'جدة', '002', '2,500.50 ر.س'],
                ['محمد سالم القحطاني', 'الدمام', '003', '3,750.25 ر.س']
            ];
            
            const headers = ['اسم العميل', 'المدينة', 'رقم الفاتورة', 'المبلغ'];
            const title = 'تقرير العملاء الجدد';
            
            const success = mockDebtManager.openAnalyticsPrintPage(data, headers, title);
            
            if (success) {
                updateStatus('✅ نجح اختبار العملاء الجدد - لا مزيد من الصفحة السوداء!', 'success');
            } else {
                updateStatus('❌ فشل اختبار العملاء الجدد', 'error');
            }
        }

        function testInactiveCustomers() {
            updateStatus('🔄 اختبار طباعة العملاء غير النشطين...', 'warning');
            
            const data = [
                ['خالد أحمد المطيري', 'المدينة المنورة', '004', '1,200.00 ر.س'],
                ['نورا عبدالله الغامدي', 'مكة المكرمة', '005', '800.25 ر.س']
            ];
            
            const headers = ['اسم العميل', 'المدينة', 'رقم الفاتورة', 'المبلغ'];
            const title = 'تقرير العملاء غير النشطين';
            
            const success = mockDebtManager.openAnalyticsPrintPage(data, headers, title);
            
            if (success) {
                updateStatus('✅ نجح اختبار العملاء غير النشطين - النصوص العربية واضحة!', 'success');
            } else {
                updateStatus('❌ فشل اختبار العملاء غير النشطين', 'error');
            }
        }

        function testStatistics() {
            updateStatus('🔄 اختبار طباعة الإحصائيات...', 'warning');
            
            const data = [
                ['1', 'أحمد محمد العلي', 'الرياض', '2', '3,000.75 ر.س'],
                ['2', 'فاطمة عبدالله الزهراني', 'جدة', '1', '2,500.50 ر.س'],
                ['3', 'محمد سالم القحطاني', 'الدمام', '1', '3,750.25 ر.س']
            ];
            
            const headers = ['الترتيب', 'اسم العميل', 'المدينة', 'عدد الفواتير', 'إجمالي المبلغ'];
            const title = 'تقرير الإحصائيات المتقدمة';
            
            const success = mockDebtManager.openAnalyticsPrintPage(data, headers, title);
            
            if (success) {
                updateStatus('✅ نجح اختبار الإحصائيات - تم حل مشكلة الصفحة السوداء!', 'success');
            } else {
                updateStatus('❌ فشل اختبار الإحصائيات', 'error');
            }
        }

        console.log('✅ صفحة اختبار إصلاح التحليلات جاهزة');
    </script>
</body>
</html>
