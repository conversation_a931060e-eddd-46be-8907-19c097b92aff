<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إزالة جميع الرسائل</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 50px rgba(0,0,0,0.4);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 42px;
            background: linear-gradient(135deg, #28a745, #20c997);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .success-banner {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            border: 4px solid #28a745;
            border-radius: 20px;
            padding: 40px;
            margin: 30px 0;
            box-shadow: 0 10px 30px rgba(40, 167, 69, 0.4);
            text-align: center;
        }
        .test-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 25px 40px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 24px;
            margin: 20px 10px;
            transition: all 0.3s ease;
            font-weight: bold;
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
            display: inline-block;
            text-align: center;
            min-width: 250px;
        }
        .test-btn:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 30px rgba(40, 167, 69, 0.6);
        }
        .status {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 16px;
        }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .buttons-container {
            text-align: center;
            margin: 30px 0;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            background: #e8f5e8;
            margin: 15px 0;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #28a745;
        }
        .feature-list li::before {
            content: "✅ ";
            font-weight: bold;
            color: #28a745;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔇 إزالة جميع الرسائل</h1>
        
        <div class="success-banner">
            <h2 style="color: #155724; font-size: 32px; margin-bottom: 20px;">✅ تم إزالة جميع الرسائل المزعجة</h2>
            <p style="font-size: 18px; color: #155724;">لا مزيد من النوافذ المنبثقة أو الرسائل التوضيحية</p>
        </div>

        <ul class="feature-list">
            <li><strong>إزالة رسائل التعليمات:</strong> لا مزيد من التعليمات في صفحة الطباعة</li>
            <li><strong>إزالة تنبيهات PDF:</strong> لا مزيد من رسائل "كيفية حفظ PDF"</li>
            <li><strong>إزالة تنبيهات الأخطاء:</strong> الأخطاء تظهر في وحدة التحكم فقط</li>
            <li><strong>إزالة رسائل النجاح:</strong> النجاح يظهر في الواجهة بدون نوافذ منبثقة</li>
            <li><strong>طباعة صامتة:</strong> فتح صفحة الطباعة مباشرة بدون رسائل</li>
        </ul>

        <div id="status" class="status">
            <div class="success">✅ جميع الرسائل تم إزالتها - تجربة مستخدم نظيفة</div>
        </div>

        <div class="buttons-container">
            <button class="test-btn" onclick="testSilentPrint()">
                🖨️ اختبار الطباعة الصامتة
            </button>
            <button class="test-btn" onclick="testSilentExport()">
                📊 اختبار التصدير الصامت
            </button>
        </div>
    </div>

    <script>
        // محاكاة الحل الجديد بدون رسائل
        function createSilentHTML(data, title) {
            const currentDate = new Date().toLocaleDateString('ar-SA');
            
            let printHTML = '<!DOCTYPE html><html lang="ar" dir="rtl"><head><meta charset="UTF-8"><title>' + title + '</title>';
            printHTML += '<style>@media print {@page {size: A4; margin: 15mm;} body {margin: 0; padding: 0; font-family: Arial, sans-serif; font-size: 12pt; direction: rtl; text-align: right;} .no-print {display: none !important;}}';
            printHTML += 'body {font-family: Arial, sans-serif; direction: rtl; text-align: right; margin: 0; padding: 20px; background: #fff; color: #000; font-size: 14px;}';
            printHTML += '.header {text-align: center; margin-bottom: 30px; border-bottom: 3px solid #2c3e50; padding-bottom: 20px;}';
            printHTML += '.title {font-size: 28px; font-weight: bold; color: #2c3e50; margin-bottom: 10px;}';
            printHTML += '.date {font-size: 16px; color: #666; margin-bottom: 10px;}';
            printHTML += '.report-table {width: 100%; border-collapse: collapse; margin: 20px 0; font-size: 14px;}';
            printHTML += '.report-table th {background: #2c3e50; color: #fff; padding: 15px 10px; text-align: center; font-weight: bold; border: 2px solid #34495e; font-size: 16px;}';
            printHTML += '.report-table td {padding: 12px 10px; text-align: center; border: 1px solid #ddd; vertical-align: middle;}';
            printHTML += '.report-table tr:nth-child(even) {background: #f8f9fa;} .report-table tr:nth-child(odd) {background: #fff;}';
            printHTML += '.print-button {background: #27ae60; color: white; border: none; padding: 15px 30px; font-size: 18px; border-radius: 8px; cursor: pointer; margin: 20px; font-weight: bold;}';
            printHTML += '.buttons-container {text-align: center; margin: 30px 0; padding: 20px; background: #f8f9fa; border-radius: 10px;}';
            printHTML += '@media print {.buttons-container {display: none !important;}}</style></head><body>';
            
            // لا توجد تعليمات أو رسائل
            
            printHTML += '<div class="buttons-container no-print">';
            printHTML += '<button class="print-button" onclick="window.print()">🖨️ طباعة</button>';
            printHTML += '<button class="print-button" onclick="window.print();" style="background: #e74c3c;">💾 حفظ PDF</button>';
            printHTML += '<button class="print-button" onclick="window.close()" style="background: #95a5a6;">❌ إغلاق</button></div>';
            
            printHTML += '<div class="header"><div class="title">' + title + '</div><div class="date">' + currentDate + '</div></div>';
            
            printHTML += '<table class="report-table"><thead><tr>';
            printHTML += '<th>رقم الفاتورة</th><th>اسم العميل</th><th>المدينة</th><th>المبلغ (ر.س)</th><th>الملاحظات</th>';
            printHTML += '</tr></thead><tbody>';

            data.forEach((item, index) => {
                const amount = parseFloat(item.amount) || 0;
                printHTML += '<tr>';
                printHTML += '<td>' + (item.invoiceNumber || '') + '</td>';
                printHTML += '<td>' + (item.customerName || '') + '</td>';
                printHTML += '<td>' + (item.city || '') + '</td>';
                printHTML += '<td>' + amount.toLocaleString('ar-SA') + '</td>';
                printHTML += '<td>' + (item.notes || '') + '</td>';
                printHTML += '</tr>';
            });

            printHTML += '</tbody></table></body></html>';
            return printHTML;
        }

        function openSilentPrintPage(data, title) {
            try {
                const printHTML = createSilentHTML(data, title);
                const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
                
                if (!printWindow) {
                    console.error('فشل في فتح نافذة الطباعة');
                    return false;
                }
                
                printWindow.document.write(printHTML);
                printWindow.document.close();
                printWindow.focus();
                
                console.log('✅ تم فتح صفحة الطباعة الصامتة بنجاح');
                return true;
                
            } catch (error) {
                console.error('❌ خطأ في الطباعة الصامتة:', error);
                return false;
            }
        }

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            statusDiv.innerHTML = `<div class="${className}">${message}</div>`;
            console.log(message);
        }

        function testSilentPrint() {
            updateStatus('🔄 اختبار الطباعة الصامتة...', 'warning');
            
            const testData = [
                {
                    invoiceNumber: '001',
                    customerName: 'أحمد محمد العلي',
                    city: 'الرياض',
                    amount: 1500.75,
                    notes: 'ملاحظة تجريبية عربية'
                },
                {
                    invoiceNumber: '002',
                    customerName: 'فاطمة عبدالله الزهراني',
                    city: 'جدة',
                    amount: 2500.50,
                    notes: 'معلومات إضافية مهمة'
                }
            ];
            
            const success = openSilentPrintPage(testData, 'اختبار الطباعة الصامتة');
            
            if (success) {
                updateStatus('✅ نجحت الطباعة الصامتة - لا رسائل منبثقة!', 'success');
            } else {
                updateStatus('❌ فشلت الطباعة الصامتة', 'error');
            }
        }

        function testSilentExport() {
            updateStatus('🔄 اختبار التصدير الصامت...', 'warning');
            
            // محاكاة تصدير صامت
            try {
                console.log('📊 تم تصدير البيانات بنجاح');
                updateStatus('✅ نجح التصدير الصامت - لا رسائل تأكيد!', 'success');
            } catch (error) {
                console.error('❌ خطأ في التصدير:', error);
                updateStatus('❌ فشل التصدير الصامت', 'error');
            }
        }

        console.log('✅ صفحة اختبار إزالة الرسائل جاهزة');
    </script>
</body>
</html>
