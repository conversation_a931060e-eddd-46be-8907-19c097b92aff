<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المكتبات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .test-button {
            background: #28a745;
        }
        .test-button:hover {
            background: #1e7e34;
        }
    </style>
</head>
<body>
    <h1>🔧 اختبار مكتبات التصدير والاستيراد</h1>
    
    <div class="test-container">
        <h2>📊 حالة المكتبات</h2>
        <div id="libraryStatus"></div>
    </div>
    
    <div class="test-container">
        <h2>🧪 اختبار الوظائف</h2>
        <button class="test-button" onclick="testPDF()">اختبار PDF</button>
        <button class="test-button" onclick="testExcel()">اختبار Excel</button>
        <button class="test-button" onclick="testAll()">اختبار شامل</button>
        <div id="testResults"></div>
    </div>

    <!-- نفس المكتبات المستخدمة في النظام الأصلي -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdn.sheetjs.com/xlsx-0.20.1/package/dist/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <script>
        // فحص حالة المكتبات عند تحميل الصفحة
        window.addEventListener('load', function() {
            checkLibraries();
        });

        function checkLibraries() {
            const statusDiv = document.getElementById('libraryStatus');
            let html = '';

            // فحص jsPDF
            if (typeof window.jspdf !== 'undefined') {
                html += '<div class="status success">✅ مكتبة jsPDF محملة بنجاح</div>';
            } else {
                html += '<div class="status error">❌ مكتبة jsPDF غير محملة</div>';
            }

            // فحص XLSX
            if (typeof window.XLSX !== 'undefined') {
                html += '<div class="status success">✅ مكتبة XLSX محملة بنجاح</div>';
            } else {
                html += '<div class="status error">❌ مكتبة XLSX غير محملة</div>';
            }

            // فحص html2canvas
            if (typeof window.html2canvas !== 'undefined') {
                html += '<div class="status success">✅ مكتبة html2canvas محملة بنجاح</div>';
            } else {
                html += '<div class="status error">❌ مكتبة html2canvas غير محملة</div>';
            }

            // فحص jsPDF AutoTable
            if (typeof window.jspdf !== 'undefined' && window.jspdf.jsPDF && window.jspdf.jsPDF.API.autoTable) {
                html += '<div class="status success">✅ مكتبة jsPDF AutoTable محملة بنجاح</div>';
            } else {
                html += '<div class="status error">❌ مكتبة jsPDF AutoTable غير محملة</div>';
            }

            statusDiv.innerHTML = html;
        }

        function testPDF() {
            const resultsDiv = document.getElementById('testResults');
            
            try {
                if (!window.jspdf) {
                    throw new Error('مكتبة jsPDF غير متوفرة');
                }

                const { jsPDF } = window.jspdf;
                const doc = new jsPDF();
                
                doc.setFontSize(16);
                doc.text('Test PDF Document', 20, 20);
                doc.text('Arabic Test: اختبار النص العربي', 20, 40);
                
                doc.save('test-pdf.pdf');
                
                resultsDiv.innerHTML = '<div class="status success">✅ تم إنشاء PDF بنجاح!</div>';
            } catch (error) {
                resultsDiv.innerHTML = `<div class="status error">❌ خطأ في PDF: ${error.message}</div>`;
            }
        }

        function testExcel() {
            const resultsDiv = document.getElementById('testResults');
            
            try {
                if (!window.XLSX) {
                    throw new Error('مكتبة XLSX غير متوفرة');
                }

                const testData = [
                    { 'رقم الفاتورة': 'INV-001', 'اسم العميل': 'أحمد محمد', 'المبلغ': 1500 },
                    { 'رقم الفاتورة': 'INV-002', 'اسم العميل': 'فاطمة علي', 'المبلغ': 2000 }
                ];

                const wb = XLSX.utils.book_new();
                const ws = XLSX.utils.json_to_sheet(testData);
                XLSX.utils.book_append_sheet(wb, ws, 'اختبار');
                
                XLSX.writeFile(wb, 'test-excel.xlsx');
                
                resultsDiv.innerHTML = '<div class="status success">✅ تم إنشاء Excel بنجاح!</div>';
            } catch (error) {
                resultsDiv.innerHTML = `<div class="status error">❌ خطأ في Excel: ${error.message}</div>`;
            }
        }

        function testAll() {
            checkLibraries();
            
            setTimeout(() => {
                testPDF();
            }, 500);
            
            setTimeout(() => {
                testExcel();
            }, 1000);
        }
    </script>
</body>
</html>
