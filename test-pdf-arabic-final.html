<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار PDF العربي النهائي</title>
    <style>
        body {
            font-family: '<PERSON><PERSON>e UI', <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 28px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #3498db;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .test-btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
    
    <!-- PDF Libraries -->
    <script src="https://unpkg.com/jspdf@latest/dist/jspdf.umd.min.js"></script>
    <script src="https://unpkg.com/jspdf-autotable@latest/dist/jspdf.plugin.autotable.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار PDF العربي النهائي</h1>
        
        <div class="test-section">
            <h3>📊 اختبار البيانات العربية</h3>
            <p>هذا الاختبار يتحقق من طباعة البيانات العربية كما هي بدون تحويل أو ترجمة</p>
            
            <button class="test-btn" onclick="testArabicDataPDF()">
                🖨️ اختبار PDF بالبيانات العربية
            </button>
            
            <button class="test-btn" onclick="testMixedDataPDF()">
                🌐 اختبار PDF بالبيانات المختلطة
            </button>
            
            <button class="test-btn" onclick="testEnglishDataPDF()">
                🔤 اختبار PDF بالبيانات الإنجليزية
            </button>
        </div>

        <div id="result" class="result">
            <h4>📋 نتائج الاختبار:</h4>
            <div id="resultContent"></div>
        </div>
    </div>

    <script>
        function showResult(message, type) {
            const result = document.getElementById('result');
            const content = document.getElementById('resultContent');
            
            result.className = `result ${type}`;
            content.innerHTML = message;
            result.style.display = 'block';
            
            // Auto hide after 5 seconds for success messages
            if (type === 'success') {
                setTimeout(() => {
                    result.style.display = 'none';
                }, 5000);
            }
        }

        function testArabicDataPDF() {
            try {
                showResult('جاري إنشاء PDF بالبيانات العربية...', 'info');
                
                // Sample Arabic data
                const arabicData = [
                    { invoiceNumber: 'INV-001', customerName: 'أحمد محمد علي', city: 'الرياض', amount: 1500, notes: 'فاتورة تجريبية عربية' },
                    { invoiceNumber: 'INV-002', customerName: 'فاطمة عبدالله', city: 'جدة', amount: 2000, notes: 'ملاحظات باللغة العربية' },
                    { invoiceNumber: 'INV-003', customerName: 'محمد سالم الأحمد', city: 'الدمام', amount: 1200, notes: 'نص عربي طويل يحتوي على تفاصيل مهمة' },
                    { invoiceNumber: 'INV-004', customerName: 'سارة أحمد محمود', city: 'مكة المكرمة', amount: 800, notes: 'بيانات عربية أصلية' },
                    { invoiceNumber: 'INV-005', customerName: 'خالد عبدالرحمن', city: 'المدينة المنورة', amount: 3000, notes: 'اختبار النصوص العربية الطويلة والمعقدة' }
                ];

                createTestPDF(arabicData, 'تقرير البيانات العربية', 'Arabic_Data_Test');
                
            } catch (error) {
                showResult('❌ خطأ في اختبار البيانات العربية: ' + error.message, 'error');
            }
        }

        function testMixedDataPDF() {
            try {
                showResult('جاري إنشاء PDF بالبيانات المختلطة...', 'info');
                
                // Mixed Arabic and English data
                const mixedData = [
                    { invoiceNumber: 'INV-001', customerName: 'أحمد محمد', city: 'Riyadh', amount: 1500, notes: 'Mixed Arabic English' },
                    { invoiceNumber: 'BILL-002', customerName: 'John Smith', city: 'جدة', amount: 2000, notes: 'عربي وإنجليزي معاً' },
                    { invoiceNumber: 'فات-003', customerName: 'سارة أحمد', city: 'Dubai', amount: 1200, notes: 'Arabic رقم English' },
                    { invoiceNumber: 'REF-004', customerName: 'Mohammed Ali', city: 'الكويت', amount: 800, notes: '123 Arabic نص 456' },
                    { invoiceNumber: 'ORD-005', customerName: 'فاطمة Smith', city: 'London', amount: 3000, notes: 'International عالمي Global' }
                ];

                createTestPDF(mixedData, 'تقرير البيانات المختلطة', 'Mixed_Data_Test');
                
            } catch (error) {
                showResult('❌ خطأ في اختبار البيانات المختلطة: ' + error.message, 'error');
            }
        }

        function testEnglishDataPDF() {
            try {
                showResult('جاري إنشاء PDF بالبيانات الإنجليزية...', 'info');
                
                // English data
                const englishData = [
                    { invoiceNumber: 'INV-001', customerName: 'John Smith', city: 'New York', amount: 1500, notes: 'English test invoice' },
                    { invoiceNumber: 'INV-002', customerName: 'Jane Doe', city: 'London', amount: 2000, notes: 'Standard English text' },
                    { invoiceNumber: 'INV-003', customerName: 'Mike Johnson', city: 'Paris', amount: 1200, notes: 'International business' },
                    { invoiceNumber: 'INV-004', customerName: 'Sarah Wilson', city: 'Berlin', amount: 800, notes: 'European client' },
                    { invoiceNumber: 'INV-005', customerName: 'David Brown', city: 'Tokyo', amount: 3000, notes: 'Global partnership deal' }
                ];

                createTestPDF(englishData, 'English Data Report', 'English_Data_Test');
                
            } catch (error) {
                showResult('❌ خطأ في اختبار البيانات الإنجليزية: ' + error.message, 'error');
            }
        }

        function createTestPDF(data, title, filename) {
            // Create HTML content for the report
            const reportHTML = createTestReportHTML(data, title);

            // Create a temporary div to render the HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = reportHTML;
            tempDiv.style.position = 'absolute';
            tempDiv.style.left = '-9999px';
            tempDiv.style.top = '-9999px';
            tempDiv.style.width = '1000px';
            tempDiv.style.backgroundColor = 'white';
            tempDiv.style.padding = '20px';
            tempDiv.style.fontFamily = 'Segoe UI, Tahoma, Arial, sans-serif';
            tempDiv.style.direction = 'rtl';

            document.body.appendChild(tempDiv);

            // Use html2canvas to convert HTML to image, then add to PDF
            html2canvas(tempDiv, {
                scale: 2.5,
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff',
                width: 1000,
                height: tempDiv.scrollHeight,
                foreignObjectRendering: true,
                onclone: function(clonedDoc) {
                    const style = clonedDoc.createElement('style');
                    style.textContent = `
                        * {
                            font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif !important;
                            -webkit-font-smoothing: antialiased;
                            -moz-osx-font-smoothing: grayscale;
                            text-rendering: optimizeLegibility;
                        }
                        table { border-collapse: collapse !important; }
                        th, td { 
                            border: 1px solid #ddd !important; 
                            text-align: center !important; 
                            white-space: nowrap;
                        }
                        [dir="rtl"] { direction: rtl !important; }
                        [dir="ltr"] { direction: ltr !important; }
                    `;
                    clonedDoc.head.appendChild(style);
                    
                    // Apply direction based on content
                    const allElements = clonedDoc.querySelectorAll('*');
                    allElements.forEach(el => {
                        if (el.textContent && /[\u0600-\u06FF]/.test(el.textContent)) {
                            el.style.direction = 'rtl';
                        }
                    });
                }
            }).then(canvas => {
                document.body.removeChild(tempDiv);

                const { jsPDF } = window.jspdf;
                const pdf = new jsPDF('p', 'mm', 'a4');

                const imgData = canvas.toDataURL('image/jpeg', 0.98);
                const imgWidth = 200;
                const pageHeight = 290;
                const imgHeight = (canvas.height * imgWidth) / canvas.width;
                
                pdf.addImage(imgData, 'JPEG', 5, 5, imgWidth, Math.min(imgHeight, pageHeight));
                
                const pdfFilename = `${filename}_${new Date().toISOString().split('T')[0]}.pdf`;
                pdf.save(pdfFilename);

                showResult(`✅ تم إنشاء ${title} بنجاح!\n📄 اسم الملف: ${pdfFilename}\n🔍 تحقق من أن البيانات تظهر كما هي بدون تحويل`, 'success');
                
            }).catch(error => {
                document.body.removeChild(tempDiv);
                showResult('❌ خطأ في إنشاء PDF: ' + error.message, 'error');
            });
        }

        function createTestReportHTML(data, title) {
            const now = new Date();
            const dateStr = `${now.getDate()}/${now.getMonth() + 1}/${now.getFullYear()}`;
            const timeStr = `${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`;

            return `
                <div style="font-family: 'Segoe UI', Tahoma, Arial, sans-serif; direction: rtl; padding: 20px; background: white;">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h1 style="color: #2c3e50; font-size: 24px; margin-bottom: 10px; direction: rtl;">${title}</h1>
                        <p style="color: #7f8c8d; font-size: 14px;">التاريخ: ${dateStr} | الوقت: ${timeStr}</p>
                    </div>

                    <table style="width: 100%; border-collapse: collapse; font-size: 12px; margin: 20px 0;">
                        <thead>
                            <tr style="background: linear-gradient(135deg, #3498db, #2980b9); color: white;">
                                <th style="border: 2px solid #1f5f8b; padding: 8px; text-align: center; font-weight: bold;">#</th>
                                <th style="border: 2px solid #1f5f8b; padding: 8px; text-align: center; font-weight: bold;">رقم الفاتورة</th>
                                <th style="border: 2px solid #1f5f8b; padding: 8px; text-align: center; font-weight: bold;">اسم العميل</th>
                                <th style="border: 2px solid #1f5f8b; padding: 8px; text-align: center; font-weight: bold;">المدينة</th>
                                <th style="border: 2px solid #1f5f8b; padding: 8px; text-align: center; font-weight: bold;">المبلغ</th>
                                <th style="border: 2px solid #1f5f8b; padding: 8px; text-align: center; font-weight: bold;">الملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${data.map((item, index) => {
                                const isEven = (index + 1) % 2 === 0;
                                const bgColor = isEven ? '#f8f9fa' : 'white';
                                
                                // عرض البيانات كما هي بدون تحويل
                                const invoiceNumber = item.invoiceNumber || '-';
                                const customerName = item.customerName || '-';
                                const city = item.city || '-';
                                const amount = (item.amount || 0).toLocaleString('en-US');
                                const notes = item.notes || '-';

                                return `
                                <tr style="background-color: ${bgColor};">
                                    <td style="border: 1px solid #ddd; padding: 6px; text-align: center; font-weight: 600;">${index + 1}</td>
                                    <td style="border: 1px solid #ddd; padding: 6px; text-align: center; direction: ltr;">${invoiceNumber}</td>
                                    <td style="border: 1px solid #ddd; padding: 6px; text-align: center; direction: rtl; color: #2563eb;">${customerName}</td>
                                    <td style="border: 1px solid #ddd; padding: 6px; text-align: center; direction: rtl; color: #059669;">${city}</td>
                                    <td style="border: 1px solid #ddd; padding: 6px; text-align: center; direction: ltr; color: #dc2626; font-weight: 600;">${amount} ر.س</td>
                                    <td style="border: 1px solid #ddd; padding: 6px; text-align: center; direction: rtl; color: #6b7280;">${notes}</td>
                                </tr>`;
                            }).join('')}
                        </tbody>
                    </table>

                    <div style="text-align: center; margin-top: 30px; color: #7f8c8d; font-size: 12px;">
                        <p>تم إنشاء هذا التقرير بواسطة نظام اختبار PDF العربي المحسن</p>
                        <p>البيانات معروضة كما هي بدون أي تحويل أو ترجمة</p>
                    </div>
                </div>
            `;
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            showResult('🚀 مرحباً بك في اختبار PDF العربي النهائي!\n\n✅ تم تحسين النظام لعرض البيانات كما هي بدون تحويل\n🔍 اختبر الوظائف أعلاه للتأكد من جودة PDF', 'info');
        });
    </script>
</body>
</html>
