<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشغيل نظام إدارة الديون - بدون إنترنت</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
            color: #333;
        }

        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 90%;
        }

        .logo {
            font-size: 80px;
            margin-bottom: 20px;
        }

        .title {
            font-size: 32px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .subtitle {
            font-size: 18px;
            color: #7f8c8d;
            margin-bottom: 30px;
        }

        .features {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            text-align: right;
        }

        .features h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 20px;
            text-align: center;
        }

        .feature-list {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            list-style: none;
        }

        .feature-list li {
            padding: 10px;
            background: white;
            border-radius: 8px;
            color: #34495e;
            font-weight: 500;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .feature-list li::before {
            content: "✅";
            margin-left: 10px;
        }

        .buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            min-width: 200px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .status {
            margin-top: 30px;
            padding: 20px;
            border-radius: 10px;
            font-weight: bold;
        }

        .status-online {
            background: #d1f2eb;
            color: #00b894;
            border: 2px solid #00cec9;
        }

        .status-offline {
            background: #ffeaa7;
            color: #d63031;
            border: 2px solid #fdcb6e;
        }

        .instructions {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            text-align: right;
            border-left: 4px solid #2196f3;
        }

        .instructions h4 {
            color: #1976d2;
            margin-bottom: 15px;
            text-align: center;
        }

        .instructions ol {
            padding-right: 20px;
        }

        .instructions li {
            margin-bottom: 8px;
            color: #424242;
        }

        @media (max-width: 768px) {
            .feature-list {
                grid-template-columns: 1fr;
            }
            
            .buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">💰</div>
        <h1 class="title">نظام إدارة ديون العملاء</h1>
        <p class="subtitle">تطبيق شامل يعمل بدون إنترنت ويمكن تثبيته على سطح المكتب</p>

        <div class="features">
            <h3>🌟 الميزات المتاحة</h3>
            <ul class="feature-list">
                <li>إدارة الديون والفواتير</li>
                <li>إحصائيات وتحليلات متقدمة</li>
                <li>البحث والفرز الذكي</li>
                <li>تصدير البيانات (CSV/Excel)</li>
                <li>عمل بدون إنترنت</li>
                <li>تثبيت على سطح المكتب</li>
                <li>حفظ البيانات محلياً</li>
                <li>واجهة عربية كاملة</li>
            </ul>
        </div>

        <div class="buttons">
            <a href="html/index.html" class="btn btn-primary">
                🚀 تشغيل التطبيق
            </a>
            <button onclick="installApp()" class="btn btn-success" id="installBtn" style="display: none;">
                📱 تثبيت على سطح المكتب
            </button>
            <a href="تعليمات_التثبيت_والعمل_بدون_انترنت.md" class="btn btn-info">
                📋 تعليمات التثبيت
            </a>
        </div>

        <div class="status" id="connectionStatus">
            <span id="statusText">فحص حالة الاتصال...</span>
        </div>

        <div class="instructions">
            <h4>📱 كيفية التثبيت على سطح المكتب</h4>
            <ol>
                <li>اضغط على "تشغيل التطبيق" أعلاه</li>
                <li>ابحث عن أيقونة التثبيت في شريط العنوان (⬇️)</li>
                <li>اضغط على "تثبيت" أو "Install"</li>
                <li>اختر "تثبيت" في النافذة المنبثقة</li>
                <li>سيظهر التطبيق على سطح المكتب</li>
            </ol>
        </div>
    </div>

    <script>
        // فحص حالة الاتصال
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            const statusText = document.getElementById('statusText');

            if (navigator.onLine) {
                statusElement.className = 'status status-online';
                statusText.textContent = '🌐 متصل بالإنترنت - جميع الميزات متاحة';
            } else {
                statusElement.className = 'status status-offline';
                statusText.textContent = '📡 غير متصل - التطبيق يعمل بشكل طبيعي بدون إنترنت';
            }
        }

        // PWA Install
        let deferredPrompt;
        const installBtn = document.getElementById('installBtn');

        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            installBtn.style.display = 'inline-block';
        });

        function installApp() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('User accepted the install prompt');
                        alert('🎉 تم تثبيت التطبيق بنجاح!');
                    }
                    deferredPrompt = null;
                    installBtn.style.display = 'none';
                });
            } else {
                alert('📱 لتثبيت التطبيق:\n\n1. افتح التطبيق في المتصفح\n2. ابحث عن أيقونة التثبيت في شريط العنوان\n3. اضغط "تثبيت"');
            }
        }

        // مراقبة تغيير حالة الاتصال
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);

        // فحص أولي
        updateConnectionStatus();

        // رسالة ترحيب
        setTimeout(() => {
            console.log('🎉 مرحباً بك في نظام إدارة ديون العملاء!');
            console.log('💡 يمكنك تثبيت التطبيق على سطح المكتب للوصول السريع');
            console.log('📡 التطبيق يعمل بدون إنترنت بعد التحميل الأول');
        }, 1000);

        // إظهار معلومات المتصفح
        const browserInfo = {
            userAgent: navigator.userAgent,
            language: navigator.language,
            platform: navigator.platform,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine
        };

        console.log('🔍 معلومات المتصفح:', browserInfo);
    </script>
</body>
</html>
