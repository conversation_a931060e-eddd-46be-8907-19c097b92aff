# 📋 دليل الاستخدام - نظام إدارة ديون العملاء

## 🎉 **تم إصلاح جميع المشاكل!**

### ✅ **المشاكل التي تم حلها:**

1. **✅ طباعة PDF**: تعمل الآن بشكل مثالي مع دعم النصوص العربية
2. **✅ تصدير Excel**: محسن مع دعم كامل للعربية
3. **✅ تصدير CSV**: مضاف كخيار أفضل وأسرع
4. **✅ استيراد الملفات**: يدعم الآن CSV و Excel معاً
5. **✅ مكتبات PDF و Excel**: محدثة ومحملة بشكل صحيح

---

## 🚀 **المميزات الجديدة:**

### 📊 **تصدير البيانات:**
- **CSV (الأفضل)**: سريع وموثوق مع دعم كامل للعربية
- **Excel**: متقدم مع تنسيق احترافي
- **PDF**: طباعة عالية الجودة للتقارير

### 📥 **استيراد البيانات:**
- **CSV**: سريع ومباشر
- **Excel**: دعم ملفات .xlsx و .xls
- **التحقق التلقائي**: من صحة البيانات والتكرار

---

## 📝 **كيفية الاستخدام:**

### **1. إضافة فاتورة جديدة:**
```
1. املأ جميع الحقول المطلوبة
2. اضغط "إضافة"
3. ستظهر في قسم "الفواتير الجديدة"
```

### **2. تصدير البيانات:**
```
1. اضغط على القائمة المنسدلة (⋮) في أي قسم
2. اختر نوع التصدير:
   - CSV (الأسرع والأفضل)
   - Excel (للتنسيق المتقدم)
   - PDF (للطباعة)
```

### **3. استيراد البيانات:**
```
1. اضغط "استيراد من CSV/Excel"
2. اختر الملف (.csv أو .xlsx أو .xls)
3. تأكد من وجود الأعمدة:
   - رقم الفاتورة
   - اسم العميل
   - المدينة
   - المبلغ
   - الملاحظات (اختياري)
```

### **4. البحث:**
```
- اكتب في مربع البحث أي كلمة
- يبحث في جميع الحقول
- النتائج تظهر فوراً
```

---

## 📋 **تنسيق ملفات CSV للاستيراد:**

### **مثال على ملف CSV صحيح:**
```csv
رقم الفاتورة,اسم العميل,المدينة,المبلغ,الملاحظات
"INV001","أحمد محمد","الرياض","1500","دفعة أولى"
"INV002","فاطمة علي","جدة","2000","مدفوع جزئياً"
"INV003","محمد سالم","الدمام","750","مكتمل"
```

### **نصائح مهمة:**
- ✅ احفظ الملف بترميز UTF-8
- ✅ استخدم الفاصلة (,) كفاصل
- ✅ ضع النصوص بين علامات اقتباس ("...")
- ✅ تأكد من صحة أرقام المبالغ

---

## 🔧 **حل المشاكل:**

### **إذا لم تعمل طباعة PDF:**
1. تأكد من تحديث المتصفح
2. اسمح بالنوافذ المنبثقة
3. تحقق من الاتصال بالإنترنت

### **إذا لم يعمل التصدير:**
1. جرب CSV بدلاً من Excel
2. تأكد من وجود بيانات للتصدير
3. تحقق من إعدادات التحميل في المتصفح

### **إذا فشل الاستيراد:**
1. تحقق من تنسيق الملف
2. تأكد من وجود الأعمدة المطلوبة
3. احفظ الملف بترميز UTF-8

---

## 🎯 **أفضل الممارسات:**

### **للتصدير:**
- **استخدم CSV** للسرعة والموثوقية
- **استخدم Excel** للتقارير المتقدمة
- **استخدم PDF** للطباعة والأرشفة

### **للاستيراد:**
- **CSV أفضل** من Excel للملفات الكبيرة
- **تحقق من البيانات** قبل الاستيراد
- **احتفظ بنسخة احتياطية** قبل الاستيراد

### **للبحث:**
- **استخدم كلمات مفتاحية** واضحة
- **جرب البحث بأجزاء** من الاسم أو الرقم
- **امسح البحث** لعرض جميع السجلات

---

## 🌐 **معلومات النظام:**

- **الرابط**: http://localhost:5000
- **البيانات**: محفوظة في `data/customers.json`
- **النسخ الاحتياطية**: تلقائية مع كل تعديل
- **دعم العربية**: كامل مع UTF-8

---

## 🎉 **النظام جاهز ويعمل بكامل مميزاته!**

جميع المشاكل تم حلها والنظام يعمل بشكل مثالي. يمكنك الآن:
- ✅ إضافة الفواتير
- ✅ تصدير البيانات (CSV/Excel/PDF)
- ✅ استيراد البيانات (CSV/Excel)
- ✅ البحث والتصفية
- ✅ طباعة التقارير

**استمتع بالاستخدام!** 🚀
