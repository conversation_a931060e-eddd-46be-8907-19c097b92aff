<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح الترميز العربي في PDF</title>
    <script src="https://unpkg.com/jspdf@latest/dist/jspdf.umd.min.js"></script>
    <script src="arabic-pdf-fix.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 32px;
        }
        .problem-section {
            background: #f8d7da;
            border: 2px solid #f5c6cb;
            border-radius: 10px;
            padding: 25px;
            margin: 20px 0;
        }
        .solution-section {
            background: #d4edda;
            border: 2px solid #c3e6cb;
            border-radius: 10px;
            padding: 25px;
            margin: 20px 0;
        }
        .test-section {
            background: #fff3cd;
            border: 2px solid #ffeaa7;
            border-radius: 10px;
            padding: 25px;
            margin: 20px 0;
        }
        .test-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
        }
        .test-btn.safe {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
        }
        .test-btn.safe:hover {
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: center;
        }
        .comparison-table th {
            background: #2c3e50;
            color: white;
            font-weight: bold;
        }
        .problem {
            background: #f8d7da;
            color: #721c24;
        }
        .solution {
            background: #d4edda;
            color: #155724;
        }
        .test-data {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            direction: ltr;
            text-align: left;
        }
        .arabic-text {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
        }
        .english-text {
            font-size: 16px;
            color: #34495e;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار إصلاح الترميز العربي في PDF</h1>
        
        <div class="problem-section">
            <h3>❌ المشكلة السابقة:</h3>
            <p><strong>النصوص العربية تظهر كرموز غريبة في PDF:</strong></p>
            <div class="test-data">
                بدلاً من: أحمد محمد - الرياض<br>
                يظهر: ÞªÞÞÞ ÞÞÞ - ÞÞÞÞÞÞª
            </div>
            <p><strong>الأسباب:</strong></p>
            <ul>
                <li>مشاكل في ترميز UTF-8</li>
                <li>عدم دعم الخطوط العربية في jsPDF</li>
                <li>تضارب في معالجة النصوص المختلطة</li>
                <li>مشاكل في الضغط والتحويل</li>
            </ul>
        </div>

        <div class="solution-section">
            <h3>✅ الحل المطبق:</h3>
            <p><strong>إصلاح شامل للترميز مع دعم اللغات المختلطة:</strong></p>
            <ul>
                <li><strong>معالجة آمنة للنصوص:</strong> تنظيف وتحويل النصوص قبل الإضافة</li>
                <li><strong>تعطيل الضغط:</strong> لتجنب مشاكل الترميز</li>
                <li><strong>تحويل الأرقام:</strong> من العربية إلى الإنجليزية</li>
                <li><strong>إزالة الرموز الخاصة:</strong> التي تسبب مشاكل</li>
                <li><strong>نظام Fallback:</strong> عدة مستويات من الحلول</li>
                <li><strong>دعم اللغات المختلطة:</strong> عربية وإنجليزية معاً</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🧪 اختبارات الترميز:</h3>
            
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>نوع البيانات</th>
                        <th>النص الأصلي</th>
                        <th>النتيجة المتوقعة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>أسماء عربية</strong></td>
                        <td class="arabic-text">أحمد محمد، فاطمة علي</td>
                        <td class="solution">نص واضح ومقروء</td>
                    </tr>
                    <tr>
                        <td><strong>أسماء إنجليزية</strong></td>
                        <td class="english-text">Ahmed Mohamed, Fatima Ali</td>
                        <td class="solution">نص واضح ومقروء</td>
                    </tr>
                    <tr>
                        <td><strong>مدن عربية</strong></td>
                        <td class="arabic-text">الرياض، جدة، الدمام</td>
                        <td class="solution">نص واضح ومقروء</td>
                    </tr>
                    <tr>
                        <td><strong>مدن إنجليزية</strong></td>
                        <td class="english-text">Riyadh, Jeddah, Dammam</td>
                        <td class="solution">نص واضح ومقروء</td>
                    </tr>
                    <tr>
                        <td><strong>أرقام عربية</strong></td>
                        <td class="arabic-text">١٢٣٤٥</td>
                        <td class="solution">تحويل إلى: 12345</td>
                    </tr>
                    <tr>
                        <td><strong>نصوص مختلطة</strong></td>
                        <td class="arabic-text">Ahmed أحمد ١٢٣</td>
                        <td class="solution">Ahmed أحمد 123</td>
                    </tr>
                </tbody>
            </table>

            <div style="text-align: center; margin: 30px 0;">
                <button class="test-btn safe" onclick="testSafePDF()">
                    🔧 اختبار الإصلاح الآمن
                </button>
                
                <button class="test-btn" onclick="testMixedLanguages()">
                    🌍 اختبار اللغات المختلطة
                </button>
                
                <button class="test-btn" onclick="testSpecialCharacters()">
                    🔤 اختبار الرموز الخاصة
                </button>
                
                <button class="test-btn" onclick="testLargeDataset()">
                    📚 اختبار بيانات كبيرة
                </button>
            </div>
        </div>

        <div id="log" class="log" style="display: none;"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            logDiv.style.display = 'block';
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type === 'error' ? 'color: red;' : type === 'success' ? 'color: green;' : '';
            logDiv.innerHTML += `<div style="${colorClass}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            const logDiv = document.getElementById('log');
            logDiv.innerHTML = '';
            logDiv.style.display = 'none';
        }

        function testSafePDF() {
            clearLog();
            log('🧪 اختبار الإصلاح الآمن للترميز العربي...', 'info');
            
            if (typeof window.testSafePDF === 'function') {
                window.testSafePDF();
            } else {
                log('❌ وظيفة الاختبار غير متوفرة', 'error');
                alert('❌ وظيفة الاختبار غير متوفرة. تأكد من تحميل arabic-pdf-fix.js');
            }
        }

        function testMixedLanguages() {
            clearLog();
            log('🌍 اختبار اللغات المختلطة...', 'info');
            
            const mixedData = [
                {
                    invoiceNumber: '001',
                    customerName: 'أحمد محمد',
                    city: 'الرياض',
                    amount: 1500,
                    notes: 'ملاحظة عربية'
                },
                {
                    invoiceNumber: '002',
                    customerName: 'Ahmed Mohamed',
                    city: 'Riyadh',
                    amount: 2500,
                    notes: 'English note'
                },
                {
                    invoiceNumber: '003',
                    customerName: 'فاطمة Ali',
                    city: 'جدة Jeddah',
                    amount: 3500,
                    notes: 'Mixed نص مختلط'
                }
            ];
            
            if (typeof window.createSafePDFWithMixedLanguages === 'function') {
                const result = createSafePDFWithMixedLanguages(mixedData, 'اختبار اللغات المختلطة Mixed Languages Test', 'mixed');
                
                if (result.success) {
                    log(`✅ نجح اختبار اللغات المختلطة: ${result.fileName}`, 'success');
                    alert(`✅ نجح الاختبار!\n\nالملف: ${result.fileName}\nالصفحات: ${result.pages}\nالسجلات: ${result.records}\n\n🔍 افتح الملف وتحقق من أن النصوص العربية والإنجليزية تظهر بشكل صحيح`);
                } else {
                    log(`❌ فشل اختبار اللغات المختلطة: ${result.error}`, 'error');
                    alert(`❌ فشل الاختبار: ${result.error}`);
                }
            } else {
                log('❌ وظيفة اللغات المختلطة غير متوفرة', 'error');
                alert('❌ وظيفة اللغات المختلطة غير متوفرة');
            }
        }

        function testSpecialCharacters() {
            clearLog();
            log('🔤 اختبار الرموز الخاصة...', 'info');
            
            const specialData = [
                {
                    invoiceNumber: '001',
                    customerName: 'أحمد محمد (الأب)',
                    city: 'الرياض - المملكة',
                    amount: 1500.75,
                    notes: 'ملاحظة: دفع جزئي ٥٠٪'
                },
                {
                    invoiceNumber: '002',
                    customerName: 'Company & Co.',
                    city: 'Riyadh @ KSA',
                    amount: 2500.25,
                    notes: 'Note: 50% payment'
                }
            ];
            
            if (typeof window.createSafePDFWithMixedLanguages === 'function') {
                const result = createSafePDFWithMixedLanguages(specialData, 'اختبار الرموز الخاصة Special Characters', 'special');
                
                if (result.success) {
                    log(`✅ نجح اختبار الرموز الخاصة: ${result.fileName}`, 'success');
                    alert(`✅ نجح الاختبار!\n\nالملف: ${result.fileName}\n\n🔍 تحقق من معالجة الرموز الخاصة والأرقام العربية`);
                } else {
                    log(`❌ فشل اختبار الرموز الخاصة: ${result.error}`, 'error');
                }
            }
        }

        function testLargeDataset() {
            clearLog();
            log('📚 اختبار بيانات كبيرة...', 'info');
            
            const largeData = [];
            const arabicNames = ['أحمد محمد', 'فاطمة علي', 'محمد سالم', 'نورا أحمد', 'خالد عبدالله'];
            const englishNames = ['Ahmed Mohamed', 'Fatima Ali', 'Mohamed Salem', 'Nora Ahmed', 'Khalid Abdullah'];
            const arabicCities = ['الرياض', 'جدة', 'الدمام', 'مكة المكرمة', 'المدينة المنورة'];
            const englishCities = ['Riyadh', 'Jeddah', 'Dammam', 'Makkah', 'Madinah'];
            
            for (let i = 1; i <= 50; i++) {
                const useArabic = Math.random() > 0.5;
                largeData.push({
                    invoiceNumber: String(i).padStart(3, '0'),
                    customerName: useArabic ? 
                        arabicNames[Math.floor(Math.random() * arabicNames.length)] :
                        englishNames[Math.floor(Math.random() * englishNames.length)],
                    city: useArabic ? 
                        arabicCities[Math.floor(Math.random() * arabicCities.length)] :
                        englishCities[Math.floor(Math.random() * englishCities.length)],
                    amount: Math.floor(Math.random() * 5000) + 500,
                    notes: useArabic ? 'ملاحظة تجريبية' : 'Test note'
                });
            }
            
            log(`📊 تم إنشاء ${largeData.length} سجل للاختبار...`, 'info');
            
            if (typeof window.createSafePDFWithMixedLanguages === 'function') {
                const result = createSafePDFWithMixedLanguages(largeData, 'اختبار البيانات الكبيرة Large Dataset Test', 'large');
                
                if (result.success) {
                    log(`✅ نجح اختبار البيانات الكبيرة: ${result.fileName}`, 'success');
                    alert(`✅ نجح الاختبار!\n\nالملف: ${result.fileName}\nالصفحات: ${result.pages}\nالسجلات: ${result.records}\n\n🔍 تحقق من أن جميع الصفحات تحتوي على نصوص واضحة`);
                } else {
                    log(`❌ فشل اختبار البيانات الكبيرة: ${result.error}`, 'error');
                }
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('🔥 صفحة اختبار إصلاح الترميز العربي جاهزة', 'success');
            
            if (typeof window.createSafePDFWithMixedLanguages === 'function') {
                log('✅ وظيفة الإصلاح الآمن متوفرة', 'success');
            } else {
                log('❌ وظيفة الإصلاح الآمن غير متوفرة', 'error');
            }
        });
    </script>
</body>
</html>
