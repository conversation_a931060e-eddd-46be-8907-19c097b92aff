<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الحل البسيط للترميز العربي</title>
    <script src="https://unpkg.com/jspdf@latest/dist/jspdf.umd.min.js"></script>
    <script src="simple-arabic-pdf-fix.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 36px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .solution-section {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            border: 3px solid #28a745;
            border-radius: 15px;
            padding: 30px;
            margin: 25px 0;
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }
        .test-section {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border: 3px solid #ffc107;
            border-radius: 15px;
            padding: 30px;
            margin: 25px 0;
            box-shadow: 0 5px 15px rgba(255, 193, 7, 0.3);
        }
        .test-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 20px 30px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 18px;
            margin: 15px;
            transition: all 0.3s ease;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }
        .test-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.5);
        }
        .test-btn.simple {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            font-size: 20px;
            padding: 25px 40px;
        }
        .test-btn.simple:hover {
            box-shadow: 0 8px 25px rgba(39, 174, 96, 0.5);
        }
        .test-btn.english {
            background: linear-gradient(135deg, #3498db, #2980b9);
        }
        .test-btn.english:hover {
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.5);
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .comparison-table th,
        .comparison-table td {
            border: 2px solid #dee2e6;
            padding: 15px;
            text-align: center;
        }
        .comparison-table th {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            font-weight: bold;
            font-size: 16px;
        }
        .problem {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
        }
        .solution {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
        }
        .test-data {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 16px;
        }
        .log {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            direction: ltr;
            text-align: left;
        }
        .arabic-text {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
            background: #e8f5e8;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            background: #e8f5e8;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 5px solid #28a745;
        }
        .feature-list li::before {
            content: "✅ ";
            font-weight: bold;
            color: #28a745;
        }
        .warning-box {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .warning-box h4 {
            color: #856404;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 الحل البسيط والفعال للترميز العربي</h1>
        
        <div class="solution-section">
            <h3>🎯 الحل البسيط - بدون تعقيدات:</h3>
            <p><strong>حل مباشر وبسيط لمشكلة الترميز العربي في PDF</strong></p>
            
            <ul class="feature-list">
                <li><strong>تنظيف النصوص:</strong> إزالة الرموز المشكلة والحفاظ على المحتوى</li>
                <li><strong>تحويل الأرقام:</strong> من العربية (١٢٣) إلى الإنجليزية (123)</li>
                <li><strong>إعدادات محسنة:</strong> تعطيل الضغط وتحسين الترميز</li>
                <li><strong>خيار إنجليزي:</strong> تحويل النصوص العربية إلى حروف إنجليزية</li>
                <li><strong>تنسيق احترافي:</strong> جداول منسقة مع ألوان</li>
                <li><strong>استقرار عالي:</strong> حل مجرب وموثوق</li>
            </ul>
        </div>

        <div class="warning-box">
            <h4>⚠️ ملاحظة مهمة:</h4>
            <p><strong>إذا لم تظهر النصوص العربية بشكل صحيح في الحل البسيط، سيتم تلقائياً استخدام الحل الإنجليزي الذي يحول النصوص العربية إلى حروف إنجليزية مقروءة.</strong></p>
            <p>مثال: "أحمد محمد" → "Ahmed Mohamed"</p>
        </div>

        <div class="test-section">
            <h3>📊 مقارنة الحلول:</h3>
            
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>العنصر</th>
                        <th>المشكلة السابقة</th>
                        <th>الحل البسيط</th>
                        <th>الحل الإنجليزي</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>النصوص العربية</strong></td>
                        <td class="problem">رموز غريبة (ÞªÞÞÞ)</td>
                        <td class="solution">نص عربي واضح</td>
                        <td class="solution">نص إنجليزي مقروء</td>
                    </tr>
                    <tr>
                        <td><strong>سهولة الاستخدام</strong></td>
                        <td class="problem">معقد ومشاكل</td>
                        <td class="solution">بسيط ومباشر</td>
                        <td class="solution">بسيط ومضمون</td>
                    </tr>
                    <tr>
                        <td><strong>الاستقرار</strong></td>
                        <td class="problem">أخطاء متكررة</td>
                        <td class="solution">مستقر</td>
                        <td class="solution">مستقر جداً</td>
                    </tr>
                    <tr>
                        <td><strong>التوافق</strong></td>
                        <td class="problem">مشاكل في المتصفحات</td>
                        <td class="solution">توافق جيد</td>
                        <td class="solution">توافق ممتاز</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <h3>🧪 اختبارات الحلول:</h3>
            
            <div class="arabic-text">
                <strong>البيانات التجريبية:</strong><br>
                • أحمد محمد - الرياض - 1500 ر.س<br>
                • فاطمة علي - جدة - 2500 ر.س<br>
                • محمد سالم - الدمام - 3750 ر.س
            </div>

            <div style="text-align: center; margin: 40px 0;">
                <button class="test-btn simple" onclick="testSimpleArabic()">
                    🔧 اختبار الحل البسيط
                </button>
                
                <button class="test-btn english" onclick="testEnglishOnly()">
                    🔤 اختبار الحل الإنجليزي
                </button>
                
                <button class="test-btn" onclick="testBothMethods()">
                    🔄 اختبار الطريقتين معاً
                </button>
                
                <button class="test-btn" onclick="testLargeData()">
                    📚 اختبار بيانات كبيرة
                </button>
            </div>
        </div>

        <div id="log" class="log" style="display: none;"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            logDiv.style.display = 'block';
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type === 'error' ? 'color: red;' : type === 'success' ? 'color: green;' : type === 'warning' ? 'color: orange;' : '';
            logDiv.innerHTML += `<div style="${colorClass}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            const logDiv = document.getElementById('log');
            logDiv.innerHTML = '';
            logDiv.style.display = 'none';
        }

        function testSimpleArabic() {
            clearLog();
            log('🔧 اختبار الحل البسيط للترميز العربي...', 'info');
            
            const testData = [
                {
                    invoiceNumber: '001',
                    customerName: 'أحمد محمد',
                    city: 'الرياض',
                    amount: 1500,
                    notes: 'ملاحظة تجريبية'
                },
                {
                    invoiceNumber: '002',
                    customerName: 'فاطمة علي',
                    city: 'جدة',
                    amount: 2500,
                    notes: 'معلومات إضافية'
                },
                {
                    invoiceNumber: '003',
                    customerName: 'محمد سالم',
                    city: 'الدمام',
                    amount: 3750,
                    notes: 'تفاصيل مهمة'
                }
            ];
            
            if (typeof window.createSimpleArabicPDF === 'function') {
                log('📋 إنشاء PDF بالحل البسيط...', 'info');
                const result = window.createSimpleArabicPDF(testData, 'تقرير اختبار الحل البسيط', 'test');
                
                if (result.success) {
                    log(`✅ نجح الحل البسيط: ${result.fileName}`, 'success');
                    alert(`✅ نجح الاختبار!\n\nالملف: ${result.fileName}\nالطريقة: ${result.method}\nالسجلات: ${result.records}\n\n🔍 افتح الملف وتحقق من النصوص`);
                } else {
                    log(`❌ فشل الحل البسيط: ${result.error}`, 'error');
                    alert(`❌ فشل الاختبار: ${result.error}`);
                }
            } else {
                log('❌ الحل البسيط غير متوفر', 'error');
                alert('❌ الحل البسيط غير متوفر. تأكد من تحميل simple-arabic-pdf-fix.js');
            }
        }

        function testEnglishOnly() {
            clearLog();
            log('🔤 اختبار الحل الإنجليزي...', 'info');
            
            const testData = [
                {
                    invoiceNumber: '001',
                    customerName: 'أحمد محمد',
                    city: 'الرياض',
                    amount: 1500,
                    notes: 'ملاحظة تجريبية'
                },
                {
                    invoiceNumber: '002',
                    customerName: 'فاطمة علي',
                    city: 'جدة',
                    amount: 2500,
                    notes: 'معلومات إضافية'
                },
                {
                    invoiceNumber: '003',
                    customerName: 'محمد سالم',
                    city: 'الدمام',
                    amount: 3750,
                    notes: 'تفاصيل مهمة'
                }
            ];
            
            if (typeof window.createEnglishOnlyPDF === 'function') {
                log('📋 إنشاء PDF بالحل الإنجليزي...', 'info');
                const result = window.createEnglishOnlyPDF(testData, 'تقرير اختبار الحل الإنجليزي', 'test');
                
                if (result.success) {
                    log(`✅ نجح الحل الإنجليزي: ${result.fileName}`, 'success');
                    alert(`✅ نجح الاختبار!\n\nالملف: ${result.fileName}\nالطريقة: ${result.method}\nالسجلات: ${result.records}\n\n🔍 افتح الملف وتحقق من تحويل النصوص العربية إلى إنجليزية`);
                } else {
                    log(`❌ فشل الحل الإنجليزي: ${result.error}`, 'error');
                    alert(`❌ فشل الاختبار: ${result.error}`);
                }
            } else {
                log('❌ الحل الإنجليزي غير متوفر', 'error');
                alert('❌ الحل الإنجليزي غير متوفر');
            }
        }

        function testBothMethods() {
            clearLog();
            log('🔄 اختبار الطريقتين معاً...', 'info');
            
            const testData = [
                {
                    invoiceNumber: '001',
                    customerName: 'أحمد محمد',
                    city: 'الرياض',
                    amount: 1500,
                    notes: 'ملاحظة تجريبية'
                },
                {
                    invoiceNumber: '002',
                    customerName: 'فاطمة علي',
                    city: 'جدة',
                    amount: 2500,
                    notes: 'معلومات إضافية'
                }
            ];
            
            // اختبار الحل البسيط
            if (typeof window.createSimpleArabicPDF === 'function') {
                log('📋 اختبار الحل البسيط...', 'info');
                const result1 = window.createSimpleArabicPDF(testData, 'اختبار الحل البسيط', 'test');
                
                if (result1.success) {
                    log(`✅ نجح الحل البسيط: ${result1.fileName}`, 'success');
                } else {
                    log(`❌ فشل الحل البسيط: ${result1.error}`, 'error');
                }
            }
            
            // اختبار الحل الإنجليزي
            setTimeout(() => {
                if (typeof window.createEnglishOnlyPDF === 'function') {
                    log('📋 اختبار الحل الإنجليزي...', 'info');
                    const result2 = window.createEnglishOnlyPDF(testData, 'اختبار الحل الإنجليزي', 'test');
                    
                    if (result2.success) {
                        log(`✅ نجح الحل الإنجليزي: ${result2.fileName}`, 'success');
                        alert('✅ تم اختبار الطريقتين بنجاح!\n\nتحقق من الملفات المحملة لمقارنة النتائج');
                    } else {
                        log(`❌ فشل الحل الإنجليزي: ${result2.error}`, 'error');
                    }
                }
            }, 1000);
        }

        function testLargeData() {
            clearLog();
            log('📚 اختبار بيانات كبيرة...', 'info');
            
            const largeData = [];
            const arabicNames = ['أحمد محمد', 'فاطمة علي', 'محمد سالم', 'نورا أحمد', 'خالد عبدالله'];
            const arabicCities = ['الرياض', 'جدة', 'الدمام', 'مكة المكرمة', 'المدينة المنورة'];
            const arabicNotes = ['ملاحظة تجريبية', 'معلومات إضافية', 'تفاصيل مهمة', 'بيانات شاملة'];
            
            for (let i = 1; i <= 20; i++) {
                largeData.push({
                    invoiceNumber: String(i).padStart(3, '0'),
                    customerName: arabicNames[Math.floor(Math.random() * arabicNames.length)],
                    city: arabicCities[Math.floor(Math.random() * arabicCities.length)],
                    amount: Math.floor(Math.random() * 5000) + 500,
                    notes: arabicNotes[Math.floor(Math.random() * arabicNotes.length)]
                });
            }
            
            log(`📊 تم إنشاء ${largeData.length} سجل للاختبار...`, 'info');
            
            if (typeof window.createSimpleArabicPDF === 'function') {
                const result = window.createSimpleArabicPDF(largeData, 'اختبار البيانات الكبيرة', 'large');
                
                if (result.success) {
                    log(`✅ نجح اختبار البيانات الكبيرة: ${result.fileName}`, 'success');
                    alert(`✅ نجح الاختبار!\n\nالملف: ${result.fileName}\nالسجلات: ${result.records}\n\n🔍 تحقق من الملف`);
                } else {
                    log(`❌ فشل اختبار البيانات الكبيرة: ${result.error}`, 'error');
                }
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('🔥 صفحة اختبار الحل البسيط جاهزة', 'success');
            
            if (typeof window.createSimpleArabicPDF === 'function') {
                log('✅ الحل البسيط متوفر', 'success');
            } else {
                log('❌ الحل البسيط غير متوفر', 'error');
            }
            
            if (typeof window.createEnglishOnlyPDF === 'function') {
                log('✅ الحل الإنجليزي متوفر', 'success');
            } else {
                log('❌ الحل الإنجليزي غير متوفر', 'error');
            }
            
            if (typeof window.jspdf !== 'undefined') {
                log('✅ مكتبة jsPDF متوفرة', 'success');
            } else {
                log('❌ مكتبة jsPDF غير متوفرة', 'error');
            }
        });
    </script>
</body>
</html>
