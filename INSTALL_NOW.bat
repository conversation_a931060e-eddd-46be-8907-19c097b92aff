@echo off
chcp 65001 >nul
title تثبيت فوري - نظام إدارة ديون العملاء

echo.
echo 🚀 التثبيت الفوري لنظام إدارة ديون العملاء
echo ============================================
echo.

:: فحص Node.js
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo.
    echo 📥 تحميل Node.js...
    start https://nodejs.org/
    echo.
    echo بعد تثبيت Node.js:
    echo 1. أعد تشغيل الكمبيوتر
    echo 2. شغل هذا الملف مرة أخرى
    pause
    exit /b 1
)

echo ✅ Node.js مثبت
echo.

:: فحص ملفات المشروع
if not exist "package.json" (
    echo ❌ ملفات المشروع غير موجودة
    echo تأكد من تشغيل هذا الملف في مجلد المشروع
    pause
    exit /b 1
)

echo ✅ ملفات المشروع موجودة
echo.

:: تثبيت المتطلبات
echo 📦 تثبيت المتطلبات...
npm install

if %errorlevel% neq 0 (
    echo ❌ فشل التثبيت - محاولة إصلاح...
    npm cache clean --force
    if exist node_modules rmdir /s /q node_modules
    npm install
)

if %errorlevel% neq 0 (
    echo ❌ فشل التثبيت نهائياً
    echo يرجى تشغيل الملف كمدير أو التحقق من الإنترنت
    pause
    exit /b 1
)

echo ✅ تم التثبيت بنجاح!
echo.

:: بناء التطبيق
echo 🏗️ بناء تطبيق سطح المكتب...
npm run build-win

if %errorlevel% equ 0 (
    echo.
    echo 🎉 تم بناء التطبيق بنجاح!
    echo.
    if exist "dist" (
        echo 📁 فتح مجلد الملفات...
        explorer dist
        echo.
        echo 🚀 يمكنك الآن تشغيل Setup.exe لتثبيت التطبيق
    )
) else (
    echo ❌ فشل في بناء التطبيق
    echo.
    echo 🔄 تجربة التشغيل المباشر...
    npm start
)

echo.
echo 🎯 انتهى التثبيت
pause
