// Enhanced Arabic Font Support for PDF
// This file provides comprehensive Arabic text rendering for jsPDF

// Arabic to Latin character mapping for PDF compatibility
const arabicToLatinMap = {
    'ا': 'a', 'ب': 'b', 'ت': 't', 'ث': 'th', 'ج': 'j', 'ح': 'h', 'خ': 'kh',
    'د': 'd', 'ذ': 'dh', 'ر': 'r', 'ز': 'z', 'س': 's', 'ش': 'sh', 'ص': 's',
    'ض': 'd', 'ط': 't', 'ظ': 'z', 'ع': 'a', 'غ': 'gh', 'ف': 'f', 'ق': 'q',
    'ك': 'k', 'ل': 'l', 'م': 'm', 'ن': 'n', 'ه': 'h', 'و': 'w', 'ي': 'y',
    'ة': 'h', 'ى': 'a', 'ء': 'a', 'آ': 'aa', 'أ': 'a', 'إ': 'i', 'ؤ': 'o', 'ئ': 'e'
};

// Arabic text processing function with transliteration fallback
function processArabicText(text) {
    if (!text) return '';

    let processedText = text.toString().trim();

    // Convert Arabic numerals to English
    const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    const englishNumerals = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

    arabicNumerals.forEach((arabicNum, index) => {
        const regex = new RegExp(arabicNum, 'g');
        processedText = processedText.replace(regex, englishNumerals[index]);
    });

    return processedText;
}

// Transliterate Arabic text to Latin for PDF fallback
function transliterateArabic(text) {
    if (!text) return '';

    let transliterated = text.toString();

    // Replace Arabic characters with Latin equivalents
    Object.keys(arabicToLatinMap).forEach(arabicChar => {
        const regex = new RegExp(arabicChar, 'g');
        transliterated = transliterated.replace(regex, arabicToLatinMap[arabicChar]);
    });

    return transliterated;
}

// Enhanced Arabic text rendering for PDF with multiple fallbacks
function renderArabicTextInPDF(doc, text, x, y, options = {}) {
    if (!text) return false;

    try {
        // Method 1: Try direct Arabic rendering
        doc.setFont('helvetica', options.fontStyle || 'normal');
        doc.text(text.toString(), x, y, {
            align: options.align || 'center',
            maxWidth: options.maxWidth,
            ...options
        });
        return true;
    } catch (error1) {
        try {
            // Method 2: Try with processed text
            const processedText = processArabicText(text);
            doc.text(processedText, x, y, {
                align: options.align || 'center',
                maxWidth: options.maxWidth,
                ...options
            });
            return true;
        } catch (error2) {
            try {
                // Method 3: Try transliteration
                const transliterated = transliterateArabic(text);
                doc.text(`[${transliterated}]`, x, y, {
                    align: options.align || 'center',
                    maxWidth: options.maxWidth,
                    ...options
                });
                return true;
            } catch (error3) {
                // Method 4: Final fallback - simple text
                console.warn('All Arabic rendering methods failed, using simple fallback');
                doc.text(text.toString().substring(0, 50), x, y, options);
                return false;
            }
        }
    }
}

// Create Arabic-compatible table for autoTable with fallbacks
function createArabicTable(doc, data, options = {}) {
    try {
        // Prepare table data with multiple fallback methods
        let processedHead = [];
        let processedBody = [];

        // Process headers
        if (options.head && options.head.length > 0) {
            processedHead = options.head.map(row =>
                row.map(cell => {
                    if (!cell) return '';
                    // Try different processing methods
                    try {
                        return cell.toString();
                    } catch (e) {
                        return transliterateArabic(cell.toString());
                    }
                })
            );
        }

        // Process body data
        processedBody = data.map(row =>
            row.map(cell => {
                if (!cell) return '';
                try {
                    return cell.toString();
                } catch (e) {
                    return transliterateArabic(cell.toString());
                }
            })
        );

        // Enhanced autoTable options
        const arabicOptions = {
            head: processedHead,
            body: processedBody,
            startY: options.startY || 50,
            styles: {
                font: 'helvetica',
                fontSize: 9,
                cellPadding: 3,
                textColor: [0, 0, 0],
                lineColor: [200, 200, 200],
                lineWidth: 0.1,
                halign: 'center',
                valign: 'middle',
                ...options.styles
            },
            headStyles: {
                fillColor: [124, 58, 237],
                textColor: [255, 255, 255],
                fontStyle: 'bold',
                halign: 'center',
                fontSize: 10,
                ...options.headStyles
            },
            bodyStyles: {
                halign: 'center',
                valign: 'middle',
                ...options.bodyStyles
            },
            alternateRowStyles: {
                fillColor: [248, 250, 252],
                ...options.alternateRowStyles
            },
            columnStyles: {
                ...options.columnStyles
            },
            margin: {
                top: 50,
                right: 15,
                bottom: 40,
                left: 15,
                ...options.margin
            },
            didDrawPage: function(data) {
                // Add page numbers
                doc.setFontSize(8);
                doc.setFont('helvetica', 'normal');
                const pageText = `Page ${data.pageNumber}`;
                doc.text(pageText, doc.internal.pageSize.width / 2, doc.internal.pageSize.height - 10, { align: 'center' });

                if (options.didDrawPage) {
                    options.didDrawPage(data);
                }
            }
        };

        // Create the table
        doc.autoTable(arabicOptions);
        return true;

    } catch (error) {
        console.error('Arabic table creation error:', error);

        // Simple fallback table
        try {
            doc.autoTable({
                head: [['#', 'Invoice', 'Customer', 'City', 'Amount', 'Notes', 'Date']],
                body: data.map((row, index) => [
                    (index + 1).toString(),
                    row[1] || '',
                    row[2] || '',
                    row[3] || '',
                    row[4] || '',
                    row[5] || '',
                    row[6] || ''
                ]),
                startY: options.startY || 50,
                styles: { fontSize: 9, cellPadding: 3 },
                headStyles: { fillColor: [124, 58, 237], textColor: [255, 255, 255] }
            });
            return true;
        } catch (fallbackError) {
            console.error('Fallback table creation failed:', fallbackError);
            return false;
        }
    }
}

// Arabic number formatting
function formatArabicNumber(number) {
    if (typeof number !== 'number') return number;

    // Format number with Arabic locale
    try {
        return number.toLocaleString('ar-SA');
    } catch (error) {
        // Fallback to regular formatting
        return number.toLocaleString();
    }
}

// Arabic date formatting
function formatArabicDate(date) {
    try {
        if (typeof date === 'string') {
            date = new Date(date);
        }
        return date.toLocaleDateString('ar-SA');
    } catch (error) {
        return date.toString();
    }
}

// Arabic time formatting
function formatArabicTime(date) {
    try {
        if (typeof date === 'string') {
            date = new Date(date);
        }
        return date.toLocaleTimeString('ar-SA');
    } catch (error) {
        return date.toString();
    }
}

// Export functions for use in other files
window.ArabicPDFSupport = {
    processArabicText,
    renderArabicTextInPDF,
    createArabicTable,
    formatArabicNumber,
    formatArabicDate,
    formatArabicTime
};
