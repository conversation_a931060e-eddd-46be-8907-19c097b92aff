<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء أيقونة افتراضية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: center;
            padding: 20px;
            background: #f0f0f0;
        }
        
        .icon-preview {
            width: 256px;
            height: 256px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            border-radius: 20%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            position: relative;
        }
        
        .icon-symbol {
            font-size: 120px;
            color: white;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
        
        .icon-text {
            position: absolute;
            bottom: 20px;
            color: white;
            font-weight: bold;
            font-size: 24px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.5);
        }
        
        .download-btn {
            background: #27ae60;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        }
        
        .download-btn:hover {
            background: #229954;
            transform: translateY(-2px);
        }
        
        .instructions {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px auto;
            max-width: 600px;
            text-align: right;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .instructions h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .instructions ol {
            text-align: right;
            padding-right: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
            color: #34495e;
        }
    </style>
</head>
<body>
    <h1>🎨 إنشاء أيقونة افتراضية لنظام إدارة الديون</h1>
    
    <div class="icon-preview" id="iconPreview">
        <div class="icon-symbol">💰</div>
        <div class="icon-text">ديون</div>
    </div>
    
    <div>
        <button class="download-btn" onclick="downloadIcon(256)">تحميل 256x256</button>
        <button class="download-btn" onclick="downloadIcon(128)">تحميل 128x128</button>
        <button class="download-btn" onclick="downloadIcon(64)">تحميل 64x64</button>
        <button class="download-btn" onclick="downloadIcon(32)">تحميل 32x32</button>
    </div>
    
    <div class="instructions">
        <h3>📋 تعليمات الاستخدام:</h3>
        <ol>
            <li>اضغط على أزرار التحميل أعلاه لتحميل الأيقونات بأحجام مختلفة</li>
            <li>احفظ الأيقونات في مجلد <code>build/</code> في مشروعك</li>
            <li>أعد تسمية الملفات:
                <ul>
                    <li><code>icon-256.png</code> → <code>icon.png</code></li>
                    <li>استخدم أداة تحويل لإنشاء <code>icon.ico</code> و <code>icon.icns</code></li>
                </ul>
            </li>
            <li>شغل <code>install-windows.bat</code> لبناء التطبيق</li>
        </ol>
        
        <h3>🛠️ أدوات تحويل الأيقونات:</h3>
        <ul>
            <li><strong>أونلاين:</strong> <a href="https://convertio.co/png-ico/" target="_blank">convertio.co</a></li>
            <li><strong>أونلاين:</strong> <a href="https://icoconvert.com/" target="_blank">icoconvert.com</a></li>
            <li><strong>برنامج:</strong> GIMP (مجاني)</li>
        </ul>
    </div>

    <script>
        function downloadIcon(size) {
            // إنشاء canvas
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // رسم الخلفية المتدرجة
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#3498db');
            gradient.addColorStop(1, '#2980b9');
            
            // رسم الشكل مع الزوايا المدورة
            const radius = size * 0.1;
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, radius);
            ctx.fill();
            
            // رسم الرمز
            ctx.fillStyle = 'white';
            ctx.font = `${size * 0.4}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.shadowColor = 'rgba(0,0,0,0.3)';
            ctx.shadowBlur = size * 0.02;
            ctx.shadowOffsetY = size * 0.02;
            ctx.fillText('💰', size / 2, size / 2 - size * 0.05);
            
            // رسم النص
            if (size >= 64) {
                ctx.font = `bold ${size * 0.08}px Arial`;
                ctx.shadowBlur = size * 0.01;
                ctx.shadowOffsetY = size * 0.01;
                ctx.fillText('ديون', size / 2, size - size * 0.15);
            }
            
            // تحميل الصورة
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `icon-${size}.png`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            });
        }
        
        // إضافة دعم roundRect للمتصفحات القديمة
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.beginPath();
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
    </script>
</body>
</html>
