<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء أيقونات التطبيق</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            margin: 0;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .icon-preview {
            width: 256px;
            height: 256px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            border-radius: 25%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px auto;
            box-shadow: 0 15px 35px rgba(0,0,0,0.4);
            position: relative;
            overflow: hidden;
        }
        
        .icon-preview::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            animation: shine 3s infinite;
        }
        
        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
            100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        }
        
        .icon-content {
            position: relative;
            z-index: 2;
            text-align: center;
        }
        
        .icon-symbol {
            font-size: 120px;
            margin-bottom: 10px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            filter: drop-shadow(0 0 10px rgba(255,255,255,0.3));
        }
        
        .icon-text {
            color: white;
            font-weight: bold;
            font-size: 24px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.5);
        }
        
        .download-section {
            margin: 30px 0;
        }
        
        .download-btn {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
            transition: all 0.3s ease;
            font-weight: bold;
        }
        
        .download-btn:hover {
            background: linear-gradient(135deg, #229954, #1e8449);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
        }
        
        .instructions {
            background: rgba(255, 255, 255, 0.15);
            padding: 25px;
            border-radius: 15px;
            margin: 30px auto;
            max-width: 600px;
            text-align: right;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .instructions h3 {
            color: #fff;
            margin-bottom: 20px;
            font-size: 20px;
        }
        
        .instructions ol, .instructions ul {
            text-align: right;
            padding-right: 20px;
        }
        
        .instructions li {
            margin-bottom: 10px;
            color: #f8f9fa;
            line-height: 1.6;
        }
        
        .instructions code {
            background: rgba(0, 0, 0, 0.3);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
        
        .size-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .size-option {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .size-preview {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            border-radius: 20%;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }
        
        .auto-download {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            font-size: 18px;
            padding: 20px 40px;
            margin: 20px 0;
        }
        
        .auto-download:hover {
            background: linear-gradient(135deg, #c0392b, #a93226);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 إنشاء أيقونات نظام إدارة ديون العملاء</h1>
        
        <div class="icon-preview" id="iconPreview">
            <div class="icon-content">
                <div class="icon-symbol">💰</div>
                <div class="icon-text">ديون</div>
            </div>
        </div>
        
        <div class="download-section">
            <button class="download-btn auto-download" onclick="downloadAllIcons()">
                📦 تحميل جميع الأيقونات تلقائياً
            </button>
        </div>
        
        <div class="size-grid">
            <div class="size-option">
                <div class="size-preview">💰</div>
                <button class="download-btn" onclick="downloadIcon(16)">16x16</button>
            </div>
            <div class="size-option">
                <div class="size-preview">💰</div>
                <button class="download-btn" onclick="downloadIcon(32)">32x32</button>
            </div>
            <div class="size-option">
                <div class="size-preview">💰</div>
                <button class="download-btn" onclick="downloadIcon(64)">64x64</button>
            </div>
            <div class="size-option">
                <div class="size-preview">💰</div>
                <button class="download-btn" onclick="downloadIcon(128)">128x128</button>
            </div>
            <div class="size-option">
                <div class="size-preview">💰</div>
                <button class="download-btn" onclick="downloadIcon(256)">256x256</button>
            </div>
            <div class="size-option">
                <div class="size-preview">💰</div>
                <button class="download-btn" onclick="downloadIcon(512)">512x512</button>
            </div>
        </div>
        
        <div class="instructions">
            <h3>📋 تعليمات الاستخدام:</h3>
            <ol>
                <li>اضغط على "تحميل جميع الأيقونات تلقائياً" لتحميل جميع الأحجام</li>
                <li>أو اضغط على أزرار الأحجام المختلفة لتحميل حجم معين</li>
                <li>ضع الأيقونات في مجلد <code>build/</code> مع الأسماء التالية:
                    <ul>
                        <li><code>icon.png</code> (الأيقونة الرئيسية 256x256)</li>
                        <li><code>icon.ico</code> (أيقونة Windows)</li>
                        <li><code>icon.icns</code> (أيقونة Mac)</li>
                    </ul>
                </li>
                <li>شغل <code>install-windows.bat</code> لبناء التطبيق</li>
            </ol>
            
            <h3>🛠️ تحويل الأيقونات:</h3>
            <ul>
                <li><strong>لـ Windows (.ico):</strong> استخدم <a href="https://convertio.co/png-ico/" target="_blank" style="color: #74b9ff;">convertio.co</a></li>
                <li><strong>لـ Mac (.icns):</strong> استخدم <a href="https://cloudconvert.com/png-to-icns" target="_blank" style="color: #74b9ff;">cloudconvert.com</a></li>
                <li><strong>أو استخدم GIMP:</strong> برنامج مجاني لتحويل الصور</li>
            </ul>
        </div>
    </div>

    <script>
        function createIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // رسم الخلفية المتدرجة
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#3498db');
            gradient.addColorStop(1, '#2980b9');
            
            // رسم الشكل مع الزوايا المدورة
            const radius = size * 0.125;
            ctx.fillStyle = gradient;
            ctx.beginPath();
            roundRect(ctx, 0, 0, size, size, radius);
            ctx.fill();
            
            // إضافة تأثير الإضاءة
            const lightGradient = ctx.createRadialGradient(size * 0.3, size * 0.3, 0, size * 0.3, size * 0.3, size * 0.7);
            lightGradient.addColorStop(0, 'rgba(255, 255, 255, 0.3)');
            lightGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
            ctx.fillStyle = lightGradient;
            ctx.fill();
            
            // رسم الرمز
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size * 0.4}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.shadowColor = 'rgba(0,0,0,0.3)';
            ctx.shadowBlur = size * 0.02;
            ctx.shadowOffsetY = size * 0.02;
            ctx.fillText('💰', size / 2, size / 2 - size * 0.05);
            
            // رسم النص
            if (size >= 64) {
                ctx.font = `bold ${size * 0.08}px Arial`;
                ctx.shadowBlur = size * 0.01;
                ctx.shadowOffsetY = size * 0.01;
                ctx.fillText('ديون', size / 2, size - size * 0.15);
            }
            
            return canvas;
        }
        
        function roundRect(ctx, x, y, width, height, radius) {
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.lineTo(x + width - radius, y);
            ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
            ctx.lineTo(x + width, y + height - radius);
            ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
            ctx.lineTo(x + radius, y + height);
            ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
            ctx.lineTo(x, y + radius);
            ctx.quadraticCurveTo(x, y, x + radius, y);
            ctx.closePath();
        }
        
        function downloadIcon(size) {
            const canvas = createIcon(size);
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `icon-${size}x${size}.png`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            });
        }
        
        function downloadAllIcons() {
            const sizes = [16, 32, 64, 128, 256, 512];
            let delay = 0;
            
            sizes.forEach(size => {
                setTimeout(() => {
                    downloadIcon(size);
                }, delay);
                delay += 500; // تأخير نصف ثانية بين كل تحميل
            });
            
            // تحميل الأيقونة الرئيسية
            setTimeout(() => {
                const canvas = createIcon(256);
                canvas.toBlob(function(blob) {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'icon.png';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                });
            }, delay);
            
            alert('🎉 بدأ تحميل جميع الأيقونات!\n\nسيتم تحميل 7 ملفات:\n- 6 أحجام مختلفة\n- icon.png (الأيقونة الرئيسية)');
        }
    </script>
</body>
</html>
