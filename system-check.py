#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
System Health Check for Customer Debt Management System
فحص سلامة نظام إدارة ديون العملاء
"""

import os
import sys
import json
import importlib.util
from pathlib import Path

def check_python_version():
    """Check Python version"""
    print("🔍 فحص إصدار Python...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 7:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - مدعوم")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - غير مدعوم (يتطلب 3.7+)")
        return False

def check_required_packages():
    """Check if required packages are installed"""
    print("\n📦 فحص الحزم المطلوبة...")
    
    required_packages = [
        'flask',
        'flask_cors',
        'werkzeug',
        'jinja2',
        'itsdangerous',
        'click',
        'blinker'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            spec = importlib.util.find_spec(package)
            if spec is not None:
                print(f"✅ {package} - مثبت")
            else:
                print(f"❌ {package} - غير مثبت")
                missing_packages.append(package)
        except ImportError:
            print(f"❌ {package} - غير مثبت")
            missing_packages.append(package)
    
    return missing_packages

def check_file_structure():
    """Check if all required files exist"""
    print("\n📁 فحص هيكل الملفات...")
    
    required_files = [
        'python/app.py',
        'python/database.py',
        'python/requirements.txt',
        'html/index.html',
        'css/style.css',
        'javascript/app.js',
        'javascript/export-import.js',
        'javascript/arabic-font.js',
        'javascript/fix-functions.js',
        'start.bat',
        'start.sh',
        'config.json'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - مفقود")
            missing_files.append(file_path)
    
    return missing_files

def check_data_directory():
    """Check data directory and database file"""
    print("\n💾 فحص مجلد البيانات...")
    
    data_dir = Path('data')
    db_file = data_dir / 'customers.json'
    
    if not data_dir.exists():
        print("❌ مجلد data غير موجود")
        try:
            data_dir.mkdir(exist_ok=True)
            print("✅ تم إنشاء مجلد data")
        except Exception as e:
            print(f"❌ فشل في إنشاء مجلد data: {e}")
            return False
    else:
        print("✅ مجلد data موجود")
    
    if not db_file.exists():
        print("❌ ملف customers.json غير موجود")
        try:
            with open(db_file, 'w', encoding='utf-8') as f:
                json.dump({"debts": []}, f, ensure_ascii=False, indent=2)
            print("✅ تم إنشاء ملف customers.json")
        except Exception as e:
            print(f"❌ فشل في إنشاء ملف customers.json: {e}")
            return False
    else:
        print("✅ ملف customers.json موجود")
        
        # Check if file is valid JSON
        try:
            with open(db_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if 'debts' in data and isinstance(data['debts'], list):
                    print(f"✅ ملف customers.json صحيح ({len(data['debts'])} سجل)")
                else:
                    print("⚠️ ملف customers.json لا يحتوي على هيكل صحيح")
        except json.JSONDecodeError:
            print("❌ ملف customers.json تالف")
            return False
        except Exception as e:
            print(f"❌ خطأ في قراءة customers.json: {e}")
            return False
    
    return True

def check_config_file():
    """Check configuration file"""
    print("\n⚙️ فحص ملف الإعدادات...")
    
    config_file = Path('config.json')
    
    if not config_file.exists():
        print("❌ ملف config.json غير موجود")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
            
        required_sections = ['app', 'server', 'database', 'features', 'ui', 'validation']
        
        for section in required_sections:
            if section in config:
                print(f"✅ قسم {section} موجود")
            else:
                print(f"❌ قسم {section} مفقود")
                return False
        
        return True
        
    except json.JSONDecodeError:
        print("❌ ملف config.json تالف")
        return False
    except Exception as e:
        print(f"❌ خطأ في قراءة config.json: {e}")
        return False

def generate_report():
    """Generate system health report"""
    print("\n" + "="*50)
    print("📊 تقرير فحص سلامة النظام")
    print("="*50)
    
    # Check Python version
    python_ok = check_python_version()
    
    # Check packages
    missing_packages = check_required_packages()
    
    # Check file structure
    missing_files = check_file_structure()
    
    # Check data directory
    data_ok = check_data_directory()
    
    # Check config file
    config_ok = check_config_file()
    
    # Generate summary
    print("\n" + "="*50)
    print("📋 ملخص النتائج")
    print("="*50)
    
    issues = []
    
    if not python_ok:
        issues.append("إصدار Python غير مدعوم")
    
    if missing_packages:
        issues.append(f"حزم مفقودة: {', '.join(missing_packages)}")
    
    if missing_files:
        issues.append(f"ملفات مفقودة: {', '.join(missing_files)}")
    
    if not data_ok:
        issues.append("مشكلة في مجلد البيانات")
    
    if not config_ok:
        issues.append("مشكلة في ملف الإعدادات")
    
    if not issues:
        print("🎉 النظام سليم وجاهز للاستخدام!")
        print("\n💡 لتشغيل النظام:")
        print("   - Windows: start.bat")
        print("   - Linux/Mac: ./start.sh")
        return True
    else:
        print("⚠️ تم العثور على المشاكل التالية:")
        for i, issue in enumerate(issues, 1):
            print(f"   {i}. {issue}")
        
        print("\n🔧 الحلول المقترحة:")
        if missing_packages:
            print("   - قم بتشغيل: pip install -r python/requirements.txt")
        if missing_files:
            print("   - تأكد من تحميل جميع ملفات المشروع")
        
        return False

if __name__ == "__main__":
    try:
        success = generate_report()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف الفحص بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
