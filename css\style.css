/* Custom CSS for Customer Debt Management System */

/* منع التمرير الأفقي العام */
* {
    box-sizing: border-box;
}

html, body {
    overflow-x: hidden;
    max-width: 100%;
}

/* RTL Support and Arabic Font */
body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    direction: rtl;
    margin: 0;
    padding: 0;
    color: #1a1a1a; /* لون أغمق للوضوح */
    font-size: 19px; /* زيادة حجم الخط الأساسي */
    line-height: 1.8; /* زيادة المسافة بين الأسطر */
    font-weight: 500; /* زيادة سماكة الخط الأساسي */
    -webkit-font-smoothing: antialiased; /* تحسين عرض الخطوط */
    -moz-osx-font-smoothing: grayscale; /* تحسين عرض الخطوط في Firefox */
}

/* Container */
.container-fluid {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    margin: 2px auto; /* توسيط الحاوية */
    padding: 5px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(10px);
    max-width: 1200px; /* تحديد عرض أقصى */
    width: 95%; /* تقليل العرض */
}

/* Page Title */
.page-title {
    color: #1f2937;
    margin-bottom: 0;
    font-weight: 800;
    font-size: 1.8rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    background: linear-gradient(135deg, #7c3aed, #a855f7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: 0.5px;
    padding: 5px 0;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    -webkit-font-smoothing: antialiased;
}

.page-title i {
    background: linear-gradient(135deg, #7c3aed, #a855f7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 1px 2px rgba(124, 58, 237, 0.2));
    margin-left: 6px;
    font-size: 1.6rem;
}

/* Statistics Header */
.stats-header {
    background: white;
    border: 2px solid #ddd;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.stats-header h2 {
    color: #333;
    font-weight: bold;
    margin-bottom: 20px;
}

.stats-row {
    display: flex;
    justify-content: space-around;
    align-items: center;
    gap: 20px;
}

.stat-item {
    text-align: center;
    flex: 1;
}

.stat-value {
    display: block;
    font-size: 2rem; /* تكبير قيم الإحصائيات أكثر */
    font-weight: bold;
    color: #333;
    margin-bottom: 6px; /* زيادة المسافة */
}

.stat-label {
    display: block;
    font-size: 1.2rem; /* تكبير تسميات الإحصائيات أكثر */
    color: #666;
}

/* Input Form Container */
.input-form-container {
    background: white;
    border: none;
    border-radius: 6px; /* تقليل الاستدارة */
    padding: 6px; /* تقليل المساحة الداخلية */
    margin-bottom: 4px; /* تقليل المسافة السفلية */
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); /* ظل أخف */
    transition: all 0.2s ease; /* انتقال أسرع */
}

.input-form-container:hover {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08); /* ظل أخف */
}

.input-form .form-row {
    display: flex;
    gap: 4px; /* تقليل المسافة بين الحقول */
    margin-bottom: 4px; /* تقليل المسافة السفلية */
    flex-wrap: wrap;
}

.input-form .form-group {
    flex: 1;
    min-width: 120px; /* تقليل العرض الأدنى */
}

.input-form .form-control {
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 10px 14px; /* زيادة المساحة الداخلية */
    font-size: 16px; /* زيادة حجم الخط للوضوح */
    font-weight: 500; /* زيادة سماكة الخط */
    transition: all 0.2s ease;
    background: white;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    color: #1f2937; /* لون نص أغمق */
    -webkit-font-smoothing: antialiased;
}

.input-form .form-control:focus {
    border-color: #7c3aed;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.15); /* ظل أبسط */
    outline: none;
    background: white;
}

.input-form .form-control::placeholder {
    color: #9ca3af;
    font-weight: 400;
}

.form-buttons {
    display: flex;
    justify-content: center;
    margin-top: 8px; /* تقليل المسافة العلوية */
}

.btn-add {
    background: #7c3aed;
    color: white;
    border: none;
    padding: 12px 28px; /* زيادة المساحة الداخلية */
    border-radius: 8px;
    font-weight: 700; /* زيادة سماكة الخط للوضوح */
    font-size: 16px; /* زيادة حجم الخط */
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 6px rgba(124, 58, 237, 0.2);
    min-width: 130px; /* زيادة العرض الأدنى */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); /* ظل للنص */
    -webkit-font-smoothing: antialiased;
}

.btn-add:hover {
    background: #6d28d9; /* لون أغمق */
    box-shadow: 0 3px 8px rgba(124, 58, 237, 0.3); /* ظل أخف */
}



/* Search Container */
.search-container {
    position: relative;
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 20px;
    padding: 3px 8px;
    margin-bottom: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    transition: all 0.2s ease;
    max-width: 280px;
    margin-left: auto;
    margin-right: auto;
    height: 36px;
    display: flex;
    align-items: center;
}

.search-container:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
    border-color: #9ca3af;
}

.search-container:focus-within {
    box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.2), 0 2px 4px rgba(0, 0, 0, 0.06);
    border-color: #3b82f6;
}

.search-input {
    width: 100%;
    border: none;
    border-radius: 20px;
    padding: 0 8px;
    padding-right: 28px;
    font-size: 13px;
    font-weight: 400;
    transition: all 0.2s ease;
    background: transparent;
    color: #374151;
    outline: none;
    height: 100%;
    -webkit-font-smoothing: antialiased;
}

.search-input:focus {
    color: #1f2937;
}

.search-input::placeholder {
    color: #9ca3af;
    font-weight: 400;
    font-style: normal;
    font-size: 12px;
}

/* Search Icon */
.search-container::before {
    content: '\f002';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    font-size: 11px;
    z-index: 10;
    transition: all 0.2s ease;
    pointer-events: none;
}

.search-container:focus-within::before {
    color: #3b82f6;
}

/* Clear Search Button */
.clear-search-btn {
    position: absolute;
    right: 30px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #9ca3af;
    font-size: 10px;
    cursor: pointer;
    padding: 2px;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    z-index: 11;
}

.clear-search-btn:hover {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    transform: translateY(-50%) scale(1.1);
}

.clear-search-btn:active {
    transform: translateY(-50%) scale(0.95);
}

/* Search Info */
.search-info {
    margin-top: 3px;
    padding: 3px 6px;
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    border-radius: 8px;
    color: white;
    font-size: 10px;
    font-weight: 500;
    display: none;
    box-shadow: 0 1px 3px rgba(5, 150, 105, 0.3);
    animation: slideInDown 0.2s ease;
    text-align: center;
    max-width: 200px;
    margin-left: auto;
    margin-right: auto;
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.search-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.search-info i {
    margin-left: 2px;
    color: #fde047;
    font-size: 9px;
    filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
    position: relative;
    z-index: 1;
}

.search-info span {
    position: relative;
    z-index: 1;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Search Breakdown */
.search-breakdown {
    display: flex;
    gap: 4px;
    margin-top: 3px;
    padding-top: 3px;
    border-top: 1px solid rgba(255, 255, 255, 0.15);
    flex-wrap: wrap;
    justify-content: center;
}

.search-detail {
    background: rgba(255, 255, 255, 0.25);
    padding: 2px 4px;
    border-radius: 6px;
    font-size: 9px;
    font-weight: 600;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.2s ease;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.15);
    position: relative;
    overflow: hidden;
    min-width: 30px;
    text-align: center;
}

.search-detail::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.search-detail:hover {
    background: rgba(255, 255, 255, 0.35);
    transform: translateY(-1px) scale(1.05);
    box-shadow: 0 1px 4px rgba(255, 255, 255, 0.3);
}

.search-detail:hover::before {
    left: 100%;
}

/* Search Animations */
@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes searchPulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4);
    }
    50% {
        box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
    }
}

.search-container.searching {
    animation: searchPulse 2s infinite;
}

/* Search Results Highlight */
.search-highlight {
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    color: #92400e;
    padding: 3px 8px;
    border-radius: 8px;
    font-weight: 700;
    box-shadow: 0 2px 6px rgba(146, 64, 14, 0.3);
    text-shadow: 0 1px 2px rgba(146, 64, 14, 0.2);
    border: 1px solid rgba(146, 64, 14, 0.2);
    display: inline-block;
    animation: highlightPulse 2s ease-in-out infinite;
}

@keyframes highlightPulse {
    0%, 100% {
        box-shadow: 0 2px 6px rgba(146, 64, 14, 0.3);
    }
    50% {
        box-shadow: 0 4px 12px rgba(146, 64, 14, 0.5);
        transform: scale(1.05);
    }
}

/* Enhanced Search Input States */
.search-input:not(:placeholder-shown) {
    background: linear-gradient(135deg, #ffffff 0%, #eff6ff 100%);
    border-color: rgba(59, 130, 246, 0.3);
}

.search-input:not(:placeholder-shown):focus {
    background: linear-gradient(135deg, #ffffff 0%, #dbeafe 100%);
}

/* Analytics Styles */
.analytics-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    margin-bottom: 8px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.analytics-header-fixed {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.analytics-header-fixed h4 {
    margin: 0;
    font-weight: 600;
    font-size: 1.5rem; /* تكبير عنوان التحليلات */
}

.section-navigation {
    display: flex;
    gap: 8px;
}

.nav-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 12px 22px; /* زيادة المساحة الداخلية */
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 15px; /* تكبير خط أزرار التحليلات */
    font-weight: 500;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 10px; /* زيادة المسافة بين الأيقونة والنص */
}

.nav-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.nav-btn:hover::before {
    left: 100%;
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.35);
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
}

.nav-btn.active {
    background: white;
    color: #667eea;
    font-weight: 600;
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 6px 20px rgba(255, 255, 255, 0.3);
}

.nav-btn.active::before {
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
}

.nav-btn.active:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.4);
}

.nav-btn i {
    transition: transform 0.3s ease;
}

.nav-btn:hover i {
    transform: scale(1.1) rotate(5deg);
}

.nav-btn.active i {
    transform: scale(1.15);
}

.analytics-content-fixed {
    padding: 20px;
    min-height: 300px;
    transition: all 0.3s ease;
}

/* Ensure proper hiding/showing of sections */
#analyticsContent,
#comparisonContent,
#statsContent {
    width: 100%;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center top;
}

/* Smooth section transitions */
.section-hiding {
    opacity: 0 !important;
    transform: translateY(-20px) scale(0.95) !important;
    height: 0 !important;
    min-height: 0 !important;
    max-height: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    overflow: hidden !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.section-showing {
    opacity: 1 !important;
    transform: translateY(0) scale(1) !important;
    height: auto !important;
    min-height: 300px !important;
    padding: 20px !important;
    overflow: visible !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Force hide sections when display is none */
#analyticsContent[style*="display: none"],
#comparisonContent[style*="display: none"],
#statsContent[style*="display: none"] {
    display: none !important;
    opacity: 0 !important;
    height: 0 !important;
    min-height: 0 !important;
    max-height: 0 !important;
    overflow: hidden !important;
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
    visibility: hidden !important;
    transform: translateY(-20px) scale(0.95) !important;
}

/* Show sections when display is block */
#analyticsContent[style*="display: block"],
#comparisonContent[style*="display: block"],
#statsContent[style*="display: block"] {
    display: block !important;
    opacity: 1 !important;
    height: auto !important;
    min-height: 300px !important;
    overflow: visible !important;
    padding: 20px !important;
    visibility: visible !important;
    transform: translateY(0) scale(1) !important;
}

/* Additional CSS to ensure only one section shows at a time */
.analytics-content-fixed {
    position: relative;
}

.analytics-content-fixed:not([style*="display: block"]) {
    display: none !important;
    height: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    overflow: hidden !important;
}

/* Analytics Grid */
.analytics-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.analytics-section {
    background: #f8fafc;
    border-radius: 12px;
    padding: 15px;
    border: 1px solid #e2e8f0;
}

.analytics-section h5 {
    margin: 0 0 15px 0;
    color: #1e293b;
    font-weight: 600;
    font-size: 1.3rem; /* تكبير عناوين أقسام التحليلات */
    display: flex;
    align-items: center;
    gap: 8px;
}

.analytics-list {
    max-height: 200px;
    overflow-y: auto;
}

.analytics-item {
    background: white;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.analytics-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.customer-name {
    font-weight: 600;
    color: #1f2937;
    font-size: 0.8rem; /* تصغير قليلاً */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px; /* تقليل العرض */
}

.customer-city {
    font-size: 0.7rem; /* تصغير قليلاً */
    color: #6b7280;
    display: flex;
    align-items: center;
    gap: 3px;
    white-space: nowrap;
}

.customer-city::before {
    content: "📍";
    font-size: 0.7rem;
}

.customer-amount {
    font-weight: 700;
    color: #059669;
    font-size: 0.7rem; /* تصغير أكثر */
    background: #f0fdf4;
    padding: 1px 3px; /* تقليل المساحة أكثر */
    border-radius: 2px;
    border: 1px solid #bbf7d0;
    white-space: nowrap;
}

.empty-analytics {
    text-align: center;
    color: #64748b;
    font-style: italic;
    padding: 20px;
}

/* Comparison Styles */
.comparison-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.comparison-card {
    background: #f8fafc;
    border-radius: 12px;
    padding: 15px;
    border: 1px solid #e2e8f0;
}

.comparison-card h5 {
    margin: 0 0 15px 0;
    color: #1e293b;
    font-weight: 600;
    font-size: 1.3rem; /* تكبير عناوين المقارنات */
    display: flex;
    align-items: center;
    gap: 8px;
}

.comparison-stats {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e2e8f0;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    color: #64748b;
    font-size: 0.9rem;
}

.stat-value {
    font-weight: 600;
    color: #1e293b;
}

.comparison-summary {
    grid-column: 1 / -1;
    background: #f8fafc;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e2e8f0;
}

.comparison-summary h5 {
    text-align: center;
    margin-bottom: 15px;
    color: #1e293b;
    font-size: 1.2rem;
    font-weight: 600;
}

.simple-comparison {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.comparison-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.comparison-label {
    font-size: 0.9rem;
    color: #64748b;
    font-weight: 500;
}

.comparison-value {
    font-weight: 700;
    font-size: 1rem;
}

.comparison-value.positive {
    color: #059669;
}

.comparison-value.negative {
    color: #dc2626;
}

/* Legacy difference styles for backward compatibility */
.difference-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.difference-label {
    color: #64748b;
    font-size: 0.9rem;
}

.difference-value {
    font-weight: 700;
    font-size: 1.2rem;
}

.difference-value.positive {
    color: #059669;
}

.difference-value.negative {
    color: #dc2626;
}

/* Advanced Statistics Styles - Compact */
.stats-grid {
    display: flex;
    flex-direction: column;
    gap: 12px; /* تقليل المسافة */
}

.stats-section {
    background: #f8fafc;
    border-radius: 8px; /* تقليل الاستدارة */
    padding: 10px; /* تقليل المساحة الداخلية */
    border: 1px solid #e2e8f0;
}

.stats-section h5 {
    margin: 0 0 8px 0; /* تقليل المسافة */
    color: #1e293b;
    font-weight: 600;
    font-size: 1.1rem; /* تصغير العناوين */
    display: flex;
    align-items: center;
    gap: 6px; /* تقليل المسافة */
}

.top-customers-list {
    display: flex;
    flex-direction: column;
    gap: 4px; /* تقليل المسافة */
}

.top-customer-item {
    background: #ffffff;
    border-radius: 4px;
    padding: 4px 6px; /* تقليل أكثر */
    display: flex;
    align-items: center;
    gap: 6px; /* تقليل المسافة أكثر */
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    min-height: 28px; /* تقليل الارتفاع أكثر */
}

.top-customer-item:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.customer-rank {
    background: #3b82f6;
    color: white;
    width: 18px; /* تصغير أكثر */
    height: 18px; /* تصغير أكثر */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.65rem; /* تصغير الخط أكثر */
    flex-shrink: 0;
}

.customer-info {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-width: 0; /* للسماح بالتقليص */
}

.customer-details {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 4px; /* تقليل المسافة أكثر */
    flex: 1;
    min-width: 0;
}

.customer-stats {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 1px;
    flex-shrink: 0;
}

.customer-invoices {
    font-size: 0.6rem; /* تصغير أكثر */
    color: #6b7280;
    white-space: nowrap;
}

.stats-section-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px; /* تقليل المسافة */
}

.stats-subsection {
    background: #f8fafc;
    border-radius: 8px; /* تقليل الاستدارة */
    padding: 8px; /* تقليل المساحة الداخلية */
    border: 1px solid #e2e8f0;
}

.stats-subsection h6 {
    margin: 0 0 6px 0; /* تقليل المسافة */
    color: #1e293b;
    font-weight: 600;
    font-size: 0.85rem; /* تصغير الخط */
    display: flex;
    align-items: center;
    gap: 5px; /* تقليل المسافة */
}

.debt-list {
    display: flex;
    flex-direction: column;
    gap: 4px; /* تقليل المسافة */
}

.debt-stat-item {
    background: white;
    border-radius: 4px; /* تقليل الاستدارة */
    padding: 6px; /* تقليل المساحة الداخلية */
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.debt-stat-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}

.debt-customer {
    font-weight: 500;
    color: #1e293b;
    font-size: 0.8rem; /* تصغير الخط */
}

.debt-amount {
    font-weight: 600;
    color: #059669;
    font-size: 0.8rem; /* تصغير الخط */
}

/* Responsive Design for Analytics */
@media (max-width: 768px) {
    .analytics-grid,
    .comparison-grid {
        grid-template-columns: 1fr;
    }

    .stats-section-row {
        grid-template-columns: 1fr;
    }

    .section-navigation {
        flex-direction: column;
        gap: 5px;
    }

    .nav-btn {
        padding: 6px 12px;
        font-size: 12px;
    }

    .comparison-result {
        flex-direction: column;
        gap: 15px;
    }

    .top-customer-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .customer-stats {
        align-items: flex-start;
    }
}

/* Filter Indicator Styles */
.filter-indicator {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.9rem;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.filter-indicator i {
    margin-left: 8px;
}

.clear-filter-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 6px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

.clear-filter-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.clear-filter-btn i {
    margin: 0;
}

/* Empty State Styles */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #64748b;
}

.empty-state-icon {
    font-size: 4rem;
    color: #cbd5e1;
    margin-bottom: 20px;
}

.empty-state h5 {
    color: #475569;
    font-weight: 600;
    margin-bottom: 10px;
    font-size: 1.6rem; /* تكبير عنوان الحالة الفارغة */
}

.empty-state p {
    color: #64748b;
    margin-bottom: 25px;
    font-size: 1.2rem; /* تكبير نص الحالة الفارغة */
    line-height: 1.6;
}

.empty-state-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.empty-state-actions .btn {
    padding: 12px 24px; /* زيادة المساحة الداخلية */
    border-radius: 8px;
    font-weight: 500;
    font-size: 1.1rem; /* تكبير خط أزرار الحالة الفارغة */
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.empty-state-actions .btn-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

.empty-state-actions .btn-primary:hover {
    background: linear-gradient(135deg, #2563eb, #1e40af);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.empty-state-actions .btn i {
    margin-left: 8px;
}

/* Smooth Section Transition Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes fadeOutDown {
    from {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    to {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

@keyframes slideOutToLeft {
    from {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
    to {
        opacity: 0;
        transform: translateX(-50px) scale(0.9);
    }
}

/* Section animation classes */
.section-enter {
    animation: fadeInUp 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.section-exit {
    animation: fadeOutDown 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.section-slide-in {
    animation: slideInFromRight 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.section-slide-out {
    animation: slideOutToLeft 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Debt Cards */
.debt-card {
    background: white;
    border-radius: 6px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    margin-bottom: 3px;
    transition: all 0.2s ease;
    max-height: 500px; /* زيادة الارتفاع بشكل كبير */
    min-height: 450px; /* ارتفاع أدنى */
    display: flex;
    flex-direction: column;
    border: 1px solid #e5e7eb;
}

.debt-card:hover {
    transform: translateY(-1px); /* تقليل التأثير */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08); /* ظل أخف */
}

.previous-debt-card {
    border-color: rgba(156, 163, 175, 0.3);
}

.previous-debt-card:hover {
    border-color: rgba(156, 163, 175, 0.5);
}

.new-invoices-card {
    border-color: rgba(16, 185, 129, 0.2);
}

.new-invoices-card:hover {
    border-color: rgba(16, 185, 129, 0.4);
}

/* Debt Header */
.debt-header {
    background: white;
    padding: 8px 12px; /* تقليل المساحة الداخلية */
    border-bottom: 1px solid #f1f5f9;
}

.debt-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px; /* تقليل المسافة */
}

.debt-actions-menu {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Dropdown Styles */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #6b7280;
    padding: 6px 10px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.dropdown-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.dropdown-content {
    display: none;
    position: absolute;
    left: 0;
    background-color: white;
    min-width: 180px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-radius: 12px;
    z-index: 1000;
    border: 1px solid #e5e7eb;
    overflow: hidden;
    top: 100%;
    margin-top: 5px;
    direction: rtl;
}

.dropdown-content.show {
    display: block;
    animation: dropdownFadeIn 0.3s ease;
}

@keyframes dropdownFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-content a {
    color: #374151;
    padding: 12px 16px;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 14px;
    border-bottom: 1px solid #f1f5f9;
    direction: rtl;
    text-align: right;
}

.dropdown-content a:last-child {
    border-bottom: none;
}

.dropdown-content a:hover {
    background-color: #f8fafc;
    color: #7c3aed;
    transform: translateX(-5px);
}

.dropdown-content a i {
    width: 16px;
    text-align: center;
}

.dropdown-content a:first-child i {
    color: #ef4444;
}

.dropdown-content a:nth-child(2) i {
    color: #10b981;
}

.dropdown-content a:last-child i {
    color: #3b82f6;
}

.debt-badge {
    padding: 4px 10px; /* تقليل المساحة */
    border-radius: 12px; /* تقليل الاستدارة */
    font-weight: 500; /* تقليل سماكة الخط */
    font-size: 12px; /* تقليل حجم الخط */
    color: white;
}

.previous-debt-badge {
    background: #6b7280; /* لون بسيط */
}

.new-invoices-badge {
    background: #10b981; /* لون بسيط */
}

.debt-icon {
    font-size: 14px; /* تقليل حجم الأيقونة */
    opacity: 0.6; /* تقليل الشفافية */
}

.previous-debt-card .debt-icon {
    color: #6b7280;
}

.new-invoices-card .debt-icon {
    color: #10b981;
}

.debt-summary {
    display: flex;
    gap: 6px; /* تقليل المسافة */
}

.summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 4px 8px; /* تقليل المساحة */
    background: #f8fafc;
    border-radius: 6px; /* تقليل الاستدارة */
    flex: 1;
    min-width: 0;
}

.summary-label {
    font-size: 10px; /* تقليل حجم الخط */
    color: #64748b;
    font-weight: 400; /* تقليل سماكة الخط */
    margin-bottom: 2px; /* تقليل المسافة */
    text-align: center;
    white-space: nowrap;
}

.summary-value {
    font-size: 12px; /* تقليل حجم الخط */
    font-weight: 600; /* تقليل سماكة الخط */
    color: #1e293b;
    text-align: center;
    word-break: break-word;
}

.data-header {
    background: #7c3aed; /* لون بسيط */
    padding: 4px 8px; /* تقليل المساحة */
    border-bottom: none;
    color: white;
}

.data-header h4 {
    margin: 0;
    color: white;
    font-weight: 800; /* زيادة سماكة الخط للوضوح */
    font-size: 1.1rem; /* زيادة حجم الخط */
    letter-spacing: 0.5px; /* زيادة المسافة بين الحروف */
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); /* ظل للنص */
    -webkit-font-smoothing: antialiased;
}

.data-stats {
    display: flex;
    gap: 6px; /* تقليل المسافة */
    font-size: 12px; /* تقليل حجم الخط */
    color: rgba(255, 255, 255, 0.95);
    flex-wrap: wrap;
}

.data-stats span {
    font-weight: 500; /* تقليل سماكة الخط */
    background: rgba(255, 255, 255, 0.15); /* تقليل الشفافية */
    padding: 2px 6px; /* تقليل المساحة */
    border-radius: 6px; /* تقليل الاستدارة */
    font-size: 11px; /* تقليل حجم الخط */
}

/* Table Styling */
.data-table {
    overflow-x: auto;
    background: white;
    max-height: 200px; /* تقليل الارتفاع */
    overflow-y: auto;
}

.data-table table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
    font-size: 14px; /* زيادة حجم الخط للوضوح */
}

.data-table th {
    background: #374151;
    color: white;
    padding: 6px 8px;
    text-align: center;
    font-weight: 700; /* زيادة سماكة الخط */
    border: none;
    font-size: 13px; /* زيادة حجم الخط */
    position: sticky;
    top: 0;
    z-index: 10;
    border-bottom: 1px solid #1f2937;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3); /* ظل للنص */
    -webkit-font-smoothing: antialiased;
}

.data-table td {
    padding: 6px 8px;
    text-align: center;
    border-bottom: 1px solid #e5e7eb;
    vertical-align: middle;
    font-weight: 500; /* زيادة سماكة الخط */
    transition: all 0.2s ease;
    font-size: 13px; /* زيادة حجم الخط */
    color: #1f2937; /* لون أغمق للوضوح */
    -webkit-font-smoothing: antialiased;
}

.data-table tbody tr:nth-child(even) {
    background-color: rgba(124, 58, 237, 0.03);
}

.data-table tbody tr:hover {
    background: #f8fafc; /* خلفية بسيطة */
}

/* Action buttons in table */
.btn-sm {
    padding: 4px 8px; /* تقليل المساحة */
    font-size: 11px; /* تقليل حجم الخط */
    border-radius: 4px; /* تقليل الاستدارة */
    font-weight: 500; /* تقليل سماكة الخط */
    transition: all 0.2s ease; /* انتقال أسرع */
}

.btn-danger {
    background: #ef4444; /* لون بسيط */
    border: none;
    color: white;
}

.btn-danger:hover {
    background: #dc2626; /* لون أغمق */
}

/* Debt Content */
.debt-content {
    flex: 1;
    overflow-y: auto;
    max-height: 400px; /* زيادة الارتفاع بشكل كبير */
    min-height: 350px; /* ارتفاع أدنى */
    position: relative;
}

.debt-list {
    padding: 0;
}

.debt-item {
    padding: 8px 12px; /* تقليل المساحة */
    border-bottom: 1px solid #e2e8f0;
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative;
    background: white;
    margin-bottom: 1px; /* تقليل المسافة */
    border-radius: 4px;
}

.debt-item:last-child {
    border-bottom: none;
}

.debt-item:hover {
    background: #f8fafc;
}

.debt-item[style*="cursor: pointer"]:hover {
    background: #f1f5f9;
}

/* تحسين أسماء العملاء عند التمرير */
.debt-item:hover .debt-customer {
    color: #1e40af; /* لون أزرق عند التمرير */
    text-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
    transform: scale(1.02); /* تكبير خفيف */
    transition: all 0.3s ease;
}

/* تحسين المبالغ عند التمرير */
.debt-item:hover .debt-amount {
    color: #065f46; /* لون أغمق عند التمرير */
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.15); /* ظل أقوى */
    transform: scale(1.02); /* تكبير خفيف */
    transition: all 0.3s ease;
}

.debt-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px; /* تقليل المسافة */
    padding-bottom: 3px; /* تقليل المساحة */
    border-bottom: 1px solid #f1f5f9;
}

.debt-customer {
    font-weight: 900; /* أقصى سماكة للخط */
    font-size: 20px; /* زيادة حجم الخط */
    color: #000000; /* أسود كامل للوضوح الأقصى */
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.12); /* ظل مناسب */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    letter-spacing: 0.6px; /* زيادة المسافة بين الحروف */
    line-height: 1.4; /* زيادة المسافة بين الأسطر */
    font-family: 'Cairo', 'Arial Black', sans-serif;
    text-transform: none;
    margin-bottom: 2px; /* زيادة المسافة السفلية */
    display: block;
    position: relative;
}

.debt-amount {
    font-weight: 900; /* أقصى سماكة للخط */
    font-size: 19px; /* زيادة حجم الخط */
    color: #047857; /* أخضر غامق للوضوح */
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); /* ظل خفيف */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    letter-spacing: 0.5px; /* زيادة المسافة بين الحروف */
    line-height: 1.4; /* زيادة المسافة بين الأسطر */
    font-family: 'Cairo', 'Arial Black', sans-serif; /* خط واضح */
    /* إزالة جميع الخلفيات والحدود والمربعات */
    background: none;
    padding: 0;
    border: none;
    border-radius: 0;
    display: inline;
    box-shadow: none;
    margin: 0;
}

.debt-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13px; /* زيادة حجم الخط */
    color: #475569;
    font-weight: 500;
    margin-top: 4px; /* زيادة المسافة */
    -webkit-font-smoothing: antialiased;
}

.debt-invoice {
    background: #f3f4f6;
    padding: 4px 10px; /* زيادة المساحة */
    border-radius: 4px;
    font-size: 13px; /* زيادة حجم الخط */
    font-weight: 600; /* زيادة سماكة الخط */
    color: #4b5563; /* لون أغمق للوضوح */
    -webkit-font-smoothing: antialiased;
}

.debt-city {
    font-weight: 600; /* زيادة سماكة الخط */
    font-size: 13px; /* زيادة حجم الخط */
    color: #4b5563; /* لون أغمق للوضوح */
    background: #f9fafb;
    padding: 4px 10px; /* زيادة المساحة */
    border-radius: 4px;
    -webkit-font-smoothing: antialiased;
}

.debt-date {
    font-size: 12px; /* زيادة حجم الخط */
    color: #6b7280; /* لون أغمق للوضوح */
    font-weight: 500; /* زيادة سماكة الخط */
    -webkit-font-smoothing: antialiased;
}

.debt-actions {
    position: absolute;
    left: 8px; /* تقليل المسافة */
    top: 50%;
    transform: translateY(-50%);
    opacity: 0;
    transition: all 0.2s ease; /* انتقال أسرع */
    display: flex;
    gap: 4px; /* تقليل المسافة بين الأزرار */
    z-index: 10;
}

.debt-item:hover .debt-actions {
    opacity: 1;
}

.debt-action-btn {
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px; /* زيادة حجم الخط للوضوح */
    font-weight: 600; /* زيادة سماكة الخط */
    cursor: pointer;
    transition: all 0.2s ease;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); /* ظل للنص */
    -webkit-font-smoothing: antialiased;
}

.debt-edit-btn {
    background: #3b82f6; /* لون بسيط */
}

.debt-edit-btn:hover {
    background: #2563eb; /* لون أغمق */
}

.debt-delete-btn {
    background: #ef4444; /* لون بسيط */
}

.debt-delete-btn:hover {
    background: #dc2626; /* لون أغمق */
}

/* Empty State */
.empty-state {
    padding: 20px 10px; /* تقليل المساحة */
    text-align: center;
    color: #94a3b8;
}

.empty-icon {
    font-size: 24px; /* تقليل حجم الأيقونة */
    margin-bottom: 8px; /* تقليل المسافة */
    opacity: 0.4; /* تقليل الشفافية */
}

.empty-state p {
    margin: 0;
    font-size: 12px; /* تقليل حجم الخط */
    font-weight: 400; /* تقليل سماكة الخط */
}

/* Bootstrap Column Spacing Override */
.row {
    margin-left: -1px;
    margin-right: -1px;
    margin-bottom: 0;
}

.col-md-6 {
    padding-left: 1px;
    padding-right: 1px;
    flex: 0 0 50%;
    max-width: 50%;
}

/* Remove any extra spacing */
.container-fluid .row:last-child {
    margin-bottom: 0;
}

/* Compact layout */
body {
    line-height: 1.7; /* تحسين المسافة بين الأسطر */
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6 {
    margin-bottom: 0.3rem;
}

p {
    margin-bottom: 0.3rem;
}

/* Maximum width utilization */
.container-fluid {
    padding-left: 2px !important;
    padding-right: 2px !important;
}

/* Ultra-compact columns */
@media (min-width: 768px) {
    .col-md-6 {
        padding-left: 1px !important;
        padding-right: 1px !important;
    }

    .row {
        margin-left: -1px !important;
        margin-right: -1px !important;
    }
}

/* Responsive Design */
@media (max-width: 992px) {
    .input-form .form-row {
        flex-direction: column;
        gap: 10px;
    }

    .input-form .form-group {
        min-width: auto;
    }

    .form-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .btn-add {
        width: 90%;
        max-width: 350px;
        padding: 8px 40px;
    }

    .search-container {
        margin-bottom: 10px;
    }

    /* Dropdown responsive */
    .dropdown-content {
        min-width: 160px;
        left: -50px;
        right: auto;
    }

    .dropdown-content a {
        padding: 10px 12px;
        font-size: 13px;
    }

    .debt-actions-menu {
        gap: 5px;
    }

    .dropdown-btn {
        padding: 4px 8px;
        font-size: 12px;
    }

    .data-stats {
        flex-direction: column;
        gap: 5px;
    }

    .data-table {
        font-size: 12px;
    }

    .data-table th,
    .data-table td {
        padding: 8px 10px;
    }

    /* Header responsive */
    .page-title {
        font-size: 1.5rem;
        text-align: center;
        justify-content: center;
        margin-bottom: 10px;
        padding: 3px 0;
    }

    .page-title i {
        font-size: 1.3rem;
        margin-left: 4px;
    }

    .row.mb-4 {
        flex-direction: column-reverse;
    }

    .search-container {
        margin-bottom: 15px;
    }
}

@media (max-width: 768px) {
    .container-fluid {
        margin: 1px;
        padding: 3px;
    }

    .row {
        margin-left: -0.5px;
        margin-right: -0.5px;
    }

    .col-md-6 {
        padding-left: 0.5px;
        padding-right: 0.5px;
        margin-bottom: 3px;
        flex: 0 0 50%;
        max-width: 50%;
    }

    /* Header for tablets */
    .page-title {
        font-size: 1.4rem;
        padding: 4px 0;
        margin-bottom: 8px;
    }

    .page-title i {
        font-size: 1.2rem;
        margin-left: 4px;
    }

    .search-container {
        padding: 2px 6px;
        max-width: 250px;
        height: 32px;
    }

    .search-input {
        font-size: 12px;
        padding: 0 6px;
        padding-right: 24px;
    }

    .search-input::placeholder {
        font-size: 11px;
    }

    .search-container::before {
        right: 8px;
        font-size: 10px;
    }

    .clear-search-btn {
        right: 25px;
        width: 16px;
        height: 16px;
        font-size: 9px;
    }

    .debt-card {
        margin-bottom: 3px;
        max-height: 380px;
    }

    .debt-header {
        padding: 12px 15px;
    }

    .debt-badge {
        font-size: 12px;
        padding: 6px 12px;
    }

    .debt-icon {
        font-size: 14px;
    }

    .debt-summary {
        gap: 8px;
    }

    .summary-item {
        padding: 6px 8px;
    }

    .summary-label {
        font-size: 9px;
    }

    .summary-value {
        font-size: 12px;
    }

    /* تحسين رؤوس الأعمدة للشاشات الصغيرة */
    .debt-list-header {
        padding: 0;
        position: sticky;
        top: 0;
        z-index: 20;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    }

    .header-item {
        padding: 8px 4px;
        font-size: 10px;
    }

    .sortable-header {
        gap: 3px;
    }

    .sort-icon {
        font-size: 9px;
    }

    .debt-content {
        max-height: 280px;
    }

    .debt-item {
        padding: 12px 15px;
    }

    .debt-customer {
        font-size: 13px;
    }

    .debt-amount {
        font-size: 12px;
    }

    .debt-details {
        font-size: 10px;
    }

    .debt-invoice {
        font-size: 9px;
        padding: 1px 6px;
    }

    .empty-state {
        padding: 20px 15px;
    }

    .empty-icon {
        font-size: 32px;
    }

    .empty-state p {
        font-size: 14px;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: 8px;
    }

    .debt-card {
        margin-bottom: 10px;
    }

    .debt-header {
        padding: 15px;
    }

    .debt-summary {
        gap: 6px;
        flex-wrap: wrap;
    }

    .summary-item {
        padding: 6px 8px;
        min-width: calc(33.333% - 4px);
    }

    .summary-label {
        font-size: 8px;
    }

    .summary-value {
        font-size: 11px;
    }

    .debt-badge {
        font-size: 11px;
        padding: 4px 10px;
    }

    .debt-content {
        max-height: 250px;
    }

    .debt-item {
        padding: 10px 15px;
    }

    .debt-customer {
        font-size: 12px;
    }

    .debt-amount {
        font-size: 11px;
    }

    .debt-details {
        font-size: 9px;
    }

    /* تحسينات إضافية لمربع البحث في الشاشات الصغيرة جداً */
    .search-container {
        max-width: 220px;
        height: 30px;
        margin-bottom: 6px;
    }

    .search-input {
        font-size: 11px;
        padding-right: 22px;
    }

    .search-input::placeholder {
        font-size: 10px;
    }

    .search-container::before {
        right: 7px;
        font-size: 9px;
    }

    .search-info {
        font-size: 9px;
        padding: 2px 4px;
        max-width: 180px;
        margin-top: 2px;
    }

    .search-breakdown {
        gap: 2px;
        margin-top: 2px;
        padding-top: 2px;
    }

    .search-detail {
        font-size: 8px;
        padding: 1px 3px;
        min-width: 25px;
    }

    .search-info i {
        font-size: 8px;
        margin-left: 1px;
    }

    .clear-search-btn {
        right: 22px;
        width: 14px;
        height: 14px;
        font-size: 8px;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOutRight {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}

/* Toast Notifications */
.toast-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 15px;
    padding: 18px 25px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25);
    z-index: 9999;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    max-width: 450px;
    min-width: 300px;
    border-left: 6px solid;
    backdrop-filter: blur(10px);
}

.toast-notification.show {
    transform: translateX(0);
    opacity: 1;
}

.toast-success {
    border-left-color: #10b981;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(255, 255, 255, 0.95));
}

.toast-error {
    border-left-color: #ef4444;
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(255, 255, 255, 0.95));
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 15px;
    position: relative;
}

.toast-content i {
    font-size: 20px;
    flex-shrink: 0;
}

.toast-success .toast-content i {
    color: #10b981;
}

.toast-error .toast-content i {
    color: #ef4444;
}

.toast-content span {
    font-weight: 600;
    color: #1f2937;
    font-size: 15px;
    flex: 1;
    line-height: 1.4;
}

.toast-close {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    transition: all 0.3s ease;
    flex-shrink: 0;
    margin-right: -5px;
}

.toast-close:hover {
    background: rgba(107, 114, 128, 0.1);
    color: #374151;
    transform: scale(1.1);
}

.toast-close i {
    font-size: 12px;
}

/* Highlight Search Results */
.highlight {
    background-color: #ffeb3b;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 20px;
    z-index: 10;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modal Styling */
.modal-content {
    border-radius: 10px;
    border: none;
}

.modal-header {
    border-radius: 10px 10px 0 0;
    border-bottom: none;
}

/* Print Styles */
@media print {
    body {
        background: white !important;
    }

    .stats-header,
    .input-form-container,
    .search-container,
    .data-container {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }

    .btn, .modal {
        display: none !important;
    }
}

/* Analytics Cards */
.analytics-card, .comparison-card, .stats-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.analytics-card:hover, .comparison-card:hover, .stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    border-color: rgba(124, 58, 237, 0.2);
}

.analytics-header, .comparison-header, .stats-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 15px 20px;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.analytics-title, .comparison-header h4, .stats-header h4 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.analytics-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.analytics-toggle-btn, .comparison-toggle-btn, .stats-toggle-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 6px 10px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.analytics-toggle-btn:hover, .comparison-toggle-btn:hover, .stats-toggle-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.analytics-content, .comparison-content, .stats-content {
    padding: 20px;
    max-height: 600px;
    overflow-y: auto;
}

/* Analysis Tables */
.analysis-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.analysis-table th {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 12px 15px;
    text-align: center;
    font-weight: 600;
    color: #374151;
    border-bottom: 2px solid #e5e7eb;
    font-size: 14px;
}

.analysis-table td {
    padding: 10px 15px;
    text-align: center;
    border-bottom: 1px solid #f1f5f9;
    font-size: 13px;
    color: #4b5563;
}

.analysis-table tr:hover {
    background: #f8fafc;
}

.analysis-table tr:last-child td {
    border-bottom: none;
}

/* Status Badges */
.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-new {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.status-inactive {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
}

.status-active {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
}

/* Comparison Cards */
.comparison-item {
    background: #f8fafc;
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 15px;
    border-left: 4px solid #667eea;
}

.comparison-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.comparison-label {
    font-weight: 600;
    color: #374151;
    font-size: 14px;
}

.comparison-value {
    font-weight: 700;
    font-size: 16px;
}

.comparison-positive {
    color: #059669;
}

.comparison-negative {
    color: #dc2626;
}

.comparison-neutral {
    color: #6b7280;
}

/* Progress Bars */
.progress-bar-container {
    background: #e5e7eb;
    border-radius: 10px;
    height: 8px;
    overflow: hidden;
    margin: 5px 0;
}

.progress-bar {
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
}

.progress-new {
    background: linear-gradient(90deg, #10b981, #059669);
}

.progress-previous {
    background: linear-gradient(90deg, #6b7280, #4b5563);
}

/* Top Debts List */
.top-debts-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.top-debt-item {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.top-debt-item:hover {
    transform: translateX(-5px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #667eea;
}

.debt-rank {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 14px;
}

.debt-info {
    flex: 1;
    margin: 0 15px;
}

.debt-customer {
    font-weight: 600;
    color: #374151;
    margin-bottom: 4px;
}

.debt-details {
    font-size: 12px;
    color: #6b7280;
}

.debt-amount {
    font-weight: 700;
    font-size: 16px;
    color: #059669;
}

/* Fix comparison-active color */
.comparison-value.comparison-active {
    color: #3b82f6;
}

/* Analytics Sections */
.analytics-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    height: 100%;
}

.new-customers-section {
    border-left: 4px solid #10b981;
}

.inactive-customers-section {
    border-left: 4px solid #ef4444;
}

.active-customers-section {
    border-left: 4px solid #3b82f6;
}

.customers-count {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    padding: 10px;
    background: #f8fafc;
    border-radius: 8px;
}

.count-number {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
}

.count-label {
    font-size: 14px;
    color: #6b7280;
    font-weight: 500;
}

.customers-list {
    max-height: 300px;
    overflow-y: auto;
}

.customer-item {
    background: #f8fafc;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
}

.customer-item:hover {
    transform: translateX(-3px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.new-customer {
    border-left: 3px solid #10b981;
}

.inactive-customer {
    border-left: 3px solid #ef4444;
}

.customer-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.customer-name {
    font-weight: 600;
    color: #1f2937;
    font-size: 14px;
}

.customer-city {
    font-size: 12px;
    color: #6b7280;
    background: #e5e7eb;
    padding: 2px 8px;
    border-radius: 12px;
}

.customer-amount {
    font-weight: 700;
    color: #059669;
    font-size: 13px;
    margin-bottom: 3px;
}

.customer-invoice {
    font-size: 11px;
    color: #9ca3af;
}

.no-data {
    text-align: center;
    color: #6b7280;
    font-style: italic;
    padding: 20px;
    background: #f9fafb;
    border-radius: 8px;
    border: 2px dashed #d1d5db;
}

/* Analytics Main Sections */
.analytics-main-section {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    border: 2px solid transparent;
    height: 100%;
}

.explanations-container {
    border-left: 4px solid #3b82f6;
}

.analysis-container {
    border-left: 4px solid #10b981;
}

.analytics-main-section h4 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin: 0;
    padding: 15px 20px;
    font-size: 1.1rem;
    font-weight: 600;
}

/* Analytics Subsections */
.analytics-subsection {
    padding: 20px;
    border-bottom: 1px solid #e5e7eb;
}

.analytics-subsection:last-child {
    border-bottom: none;
}

.analytics-subsection h6 {
    color: #374151;
    font-weight: 600;
    margin-bottom: 15px;
    font-size: 14px;
}

/* Compact Table */
.compact-table {
    font-size: 12px;
}

.compact-table th,
.compact-table td {
    padding: 8px 10px;
}

.analysis-table-container {
    max-height: 300px;
    overflow-y: auto;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

/* Summary Statistics */
.summary-stats {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.stat-label {
    font-weight: 600;
    color: #374151;
    font-size: 13px;
}

.stat-value {
    font-weight: 700;
    font-size: 14px;
}

.stat-value.positive {
    color: #059669;
}

.stat-value.negative {
    color: #dc2626;
}

/* Customer Items */
.customers-count {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    padding: 10px 15px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 8px;
    border: 1px solid #cbd5e1;
}

.count-number {
    font-size: 24px;
    font-weight: 700;
    color: #1e40af;
}

.count-label {
    font-size: 14px;
    color: #64748b;
    font-weight: 500;
}

.customers-list {
    max-height: 300px;
    overflow-y: auto;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.customer-item {
    padding: 12px 15px;
    border-bottom: 1px solid #f1f5f9;
    transition: all 0.2s ease;
}

.customer-item:last-child {
    border-bottom: none;
}

.customer-item:hover {
    background: #f8fafc;
}

.customer-item.new-customer {
    border-left: 4px solid #10b981;
    background: linear-gradient(90deg, #ecfdf5 0%, #ffffff 100%);
}

.customer-item.inactive-customer {
    border-left: 4px solid #ef4444;
    background: linear-gradient(90deg, #fef2f2 0%, #ffffff 100%);
}

.customer-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.customer-name {
    font-weight: 600;
    color: #1f2937;
    font-size: 14px;
}

.customer-city {
    font-size: 12px;
    color: #6b7280;
    background: #f3f4f6;
    padding: 2px 8px;
    border-radius: 12px;
}

.customer-amount {
    font-weight: 700;
    color: #059669;
    font-size: 13px;
    margin-bottom: 3px;
}

.customer-invoice {
    font-size: 11px;
    color: #9ca3af;
    font-family: 'Courier New', monospace;
}

/* Stats Section Styling */
.stats-section {
    background: #f8fafc;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #e2e8f0;
    height: 100%;
}

.stats-section h5 {
    margin-bottom: 15px;
    color: #1e293b;
    font-weight: 600;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 10px;
}

/* Responsive adjustments for side-by-side layout */
@media (max-width: 768px) {
    .stats-section {
        margin-bottom: 20px;
    }

    .top-debts-list {
        max-height: 300px;
        overflow-y: auto;
    }
}

/* Analytics Section Styling */
.analytics-section {
    background: #f8fafc;
    border-radius: 8px; /* تصغير الحواف */
    padding: 10px; /* تقليل المسافة الداخلية */
    border: 1px solid #e2e8f0;
    height: 100%;
    margin-bottom: 10px; /* تقليل المسافة بين الحاويات */
    overflow: hidden; /* منع التمرير الأفقي */
    word-wrap: break-word; /* كسر الكلمات الطويلة */
}

.analytics-header {
    margin-bottom: 10px;
}

.analytics-header h5 {
    margin-bottom: 8px;
    color: #1e293b;
    font-weight: 600;
    font-size: 14px; /* تصغير حجم الخط */
    border-bottom: 1px solid #e2e8f0; /* تصغير سمك الحد */
    padding-bottom: 4px; /* تقليل المسافة */
}

.analytics-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 10px;
    padding: 8px;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.analytics-stats .stat-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    font-weight: 500;
    color: #475569;
    background: #f8fafc;
    padding: 3px 6px;
    border-radius: 4px;
    border: 1px solid #e2e8f0;
    white-space: nowrap;
}

.analytics-stats .stat-item i {
    font-size: 10px;
    color: #64748b;
}

.analytics-list {
    max-height: 300px;
    overflow-y: auto;
}

.analytics-item {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1.5fr;
    gap: 8px;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f1f5f9;
    font-size: 12px;
}

.analytics-item:last-child {
    border-bottom: none;
}

.analytics-item .customer-name {
    font-weight: 600;
    color: #1e293b;
    font-size: 13px;
}

.analytics-item .customer-city {
    color: #64748b;
    font-size: 11px;
}

.analytics-item .customer-invoices {
    color: #3b82f6;
    font-size: 11px;
    font-weight: 500;
}

.analytics-item .customer-amount {
    font-weight: 600;
    color: #059669;
    font-size: 12px;
    text-align: left;
}

.empty-analytics {
    text-align: center;
    color: #64748b;
    font-style: italic;
    padding: 15px;
    font-size: 12px;
}

/* Differences Section Styling */
.differences-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Summary Section */
.total-differences-summary {
    display: flex;
    justify-content: space-around;
    background: #ffffff;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 10px;
}

.summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.summary-item.main {
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    padding: 10px;
    border-radius: 6px;
    border: 1px solid #0ea5e9;
}

.summary-label {
    font-size: 12px;
    color: #64748b;
    font-weight: 500;
    margin-bottom: 5px;
}

.summary-value {
    font-size: 16px;
    font-weight: 700;
}

.summary-value.previous {
    color: #dc2626;
}

.summary-value.new {
    color: #059669;
}

.summary-value.positive {
    color: #059669;
}

.summary-value.negative {
    color: #dc2626;
}

/* Table Styling */
.differences-table-container {
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    margin-top: 15px;
    width: 100%;
}

.differences-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    table-layout: fixed;
    background: #ffffff;
    border: none;
}

/* تحديد عرض الأعمدة */
.differences-table th:nth-child(1), /* رقم الفاتورة السابق */
.differences-table td:nth-child(1) {
    width: 10%;
}

.differences-table th:nth-child(2), /* رقم الفاتورة الجديد */
.differences-table td:nth-child(2) {
    width: 10%;
}

.differences-table th:nth-child(3), /* اسم العميل */
.differences-table td:nth-child(3) {
    width: 25%;
}

.differences-table th:nth-child(4), /* المدينة */
.differences-table td:nth-child(4) {
    width: 15%;
}

.differences-table th:nth-child(5), /* المبلغ السابق */
.differences-table td:nth-child(5) {
    width: 15%;
}

.differences-table th:nth-child(6), /* المبلغ الجديد */
.differences-table td:nth-child(6) {
    width: 15%;
}

.differences-table th:nth-child(7), /* الفرق */
.differences-table td:nth-child(7) {
    width: 10%;
}

.differences-table thead {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
}

.differences-table th {
    padding: 12px 8px;
    text-align: center;
    font-weight: 600;
    color: #1e293b;
    border-bottom: 2px solid #e2e8f0;
    font-size: 13px;
}

.differences-table tbody tr {
    border-bottom: 1px solid #f1f5f9;
    transition: background-color 0.2s ease;
}

.differences-table tbody tr:hover {
    background-color: #f8fafc;
}

.differences-table tbody tr:last-child {
    border-bottom: none;
}

.differences-table td {
    padding: 10px 8px;
    text-align: center;
    vertical-align: middle;
    font-size: 13px;
}

.differences-table .invoice-numbers {
    font-weight: 500 !important;
    color: #3b82f6 !important;
    font-size: 12px !important;
    word-wrap: break-word;
    overflow-wrap: break-word;
    line-height: 1.3;
    padding: 6px 8px;
    background: #eff6ff;
    border-radius: 4px;
    text-align: center;
}

.differences-table .customer-name {
    font-weight: 600 !important;
    color: #1e293b !important;
    font-size: 15px !important;
    text-align: right !important;
    padding: 8px 12px;
    background: #f8fafc;
    border-radius: 4px;
}

.differences-table .customer-city {
    color: #64748b !important;
    font-size: 12px !important;
    background: #f1f5f9 !important;
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-block;
    font-weight: 500;
}

.differences-table .amount {
    font-weight: 600;
    font-size: 13px;
}

.differences-table .amount.previous {
    color: #dc2626 !important;
    background: #fef2f2;
    padding: 2px 6px;
    border-radius: 3px;
}

.differences-table .amount.new {
    color: #059669 !important;
    background: #f0fdf4;
    padding: 2px 6px;
    border-radius: 3px;
}

.differences-table .difference {
    font-weight: 700;
    font-size: 14px;
}

.differences-table .difference.positive {
    color: #059669 !important;
    background: #f0fdf4 !important;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 700 !important;
}

.differences-table .difference.negative {
    color: #dc2626 !important;
    background: #fef2f2 !important;
    padding: 4px 8px;
    border-radius: 4px;
}

/* إضافة تنسيقات قوية للجدول */
.differences-container .differences-table-container {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.differences-container .differences-table {
    display: table !important;
    visibility: visible !important;
}

.differences-container .differences-table thead,
.differences-container .differences-table tbody,
.differences-container .differences-table tr,
.differences-container .differences-table th,
.differences-container .differences-table td {
    display: table-row-group !important;
    visibility: visible !important;
}

.differences-container .differences-table tr {
    display: table-row !important;
}

.differences-container .differences-table th,
.differences-container .differences-table td {
    display: table-cell !important;
}



.empty-differences {
    text-align: center;
    color: #64748b;
    font-style: italic;
    padding: 20px;
    background: #f8fafc;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.empty-differences i {
    font-size: 24px;
    margin-bottom: 10px;
    display: block;
}

.empty-differences p {
    margin: 0;
    font-size: 14px;
}

/* Sort Headers Styling */
.debt-list-header {
    display: flex;
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 6px 6px 0 0;
    border: 1px solid #e2e8f0;
    border-bottom: 2px solid #cbd5e1;
    margin-bottom: 0;
    padding: 0;
    position: sticky;
    top: 0;
    z-index: 15;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-item {
    flex: 1;
    padding: 10px 8px;
    font-weight: 600;
    color: #1e293b;
    font-size: 12px;
    text-align: center;
    border-left: 1px solid #e2e8f0;
}

.header-item:last-child {
    border-left: none;
}

.sortable-header {
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.sortable-header:hover {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.sort-icon {
    font-size: 11px;
    color: #64748b;
    transition: all 0.2s ease;
}

.sortable-header:hover .sort-icon {
    color: #3b82f6;
}

.sort-icon.fas.fa-sort-up {
    color: #059669;
}

.sort-icon.fas.fa-sort-down {
    color: #dc2626;
}

/* Active sort column styling */
.sortable-header.active-sort {
    background: rgba(59, 130, 246, 0.15);
    color: #1e40af;
    font-weight: 700;
}

.sortable-header.active-sort .sort-icon {
    transform: scale(1.1);
}

/* Enhanced sticky header visibility */
.debt-list-header.sticky-active {
    background: linear-gradient(135deg, #ffffff, #f1f5f9);
    border-bottom: 3px solid #3b82f6;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Adjust debt list to connect with header */
.debt-list {
    border-radius: 0 0 6px 6px;
    border-top: none;
    padding-top: 0;
    margin-top: 0;
}

/* Ensure proper spacing when header is sticky */
.debt-content .debt-list {
    padding-top: 5px;
}

/* Table sort headers */
.differences-table .sortable-header {
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
}

.differences-table .sortable-header:hover {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.differences-table .sort-icon {
    font-size: 11px;
    color: #64748b;
    margin-left: 5px;
    transition: all 0.2s ease;
}

.differences-table .sortable-header:hover .sort-icon {
    color: #3b82f6;
}

/* Compact List Styling */
.compact-list {
    max-height: 400px; /* زيادة الارتفاع لعرض المزيد */
    overflow-y: hidden; /* إزالة التمرير العمودي */
    overflow-x: hidden; /* منع التمرير الأفقي */
    padding-right: 5px;
    width: 100%; /* ضمان العرض الكامل */
}

.compact-item {
    padding: 6px 10px; /* تقليل المسافة الداخلية */
    margin-bottom: 4px; /* تقليل المسافة بين العناصر */
    font-size: 12px; /* تصغير حجم الخط */
    width: 100%; /* ضمان العرض الكامل */
    box-sizing: border-box; /* تضمين الحشو في العرض */
    overflow: hidden; /* منع تجاوز المحتوى */
}

.compact-item .customer-info {
    margin-bottom: 3px;
    display: flex;
    flex-wrap: wrap; /* السماح بالتفاف النص */
    gap: 5px;
    width: 100%;
    overflow: hidden;
}

.compact-item .customer-name {
    font-size: 13px;
    font-weight: 600;
    flex: 1;
    min-width: 0; /* السماح بالانكماش */
    overflow: hidden;
    text-overflow: ellipsis; /* إضافة نقاط عند القطع */
    white-space: nowrap;
}

.compact-item .customer-city {
    font-size: 11px;
    color: #64748b;
    flex-shrink: 0; /* منع الانكماش */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 80px; /* حد أقصى للعرض */
}

.compact-item .customer-amount {
    font-size: 12px;
    font-weight: 600;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.compact-item .customer-invoice {
    font-size: 10px;
    color: #9ca3af;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Customers Count Styling */
.customers-count {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    padding: 8px 12px;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.count-number {
    font-size: 20px;
    font-weight: 700;
    color: #3b82f6;
}

.count-label {
    font-size: 12px;
    color: #64748b;
    font-weight: 500;
}

/* No Data Styling */
.no-data {
    text-align: center;
    padding: 20px;
    color: #9ca3af;
    font-style: italic;
    background: #ffffff;
    border-radius: 8px;
    border: 1px dashed #e2e8f0;
}

/* Compact Table Styling */
.compact-table {
    font-size: 13px;
}

.compact-table th {
    padding: 8px 10px;
    font-size: 12px;
    font-weight: 600;
}

.compact-table td {
    padding: 6px 10px;
    font-size: 12px;
}

/* Container Specific Colors */
.new-customers-container {
    border-left: 4px solid #10b981;
}

.inactive-customers-container {
    border-left: 4px solid #ef4444;
}

.active-customers-container {
    border-left: 4px solid #3b82f6;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .analytics-section {
        margin-bottom: 15px;
        padding: 12px;
        overflow: hidden; /* منع التمرير الأفقي */
    }

    .compact-list {
        max-height: 200px;
        overflow-x: hidden; /* منع التمرير الأفقي */
    }

    .customers-count {
        padding: 6px 10px;
    }

    .count-number {
        font-size: 18px;
    }

    .count-label {
        font-size: 11px;
    }

    .compact-item .customer-name {
        font-size: 12px;
        max-width: 120px; /* حد أقصى للأسماء الطويلة */
    }

    .compact-item .customer-city {
        font-size: 10px;
        max-width: 60px; /* حد أقصى للمدن */
    }
}

/* إضافة قواعد عامة لمنع التمرير الأفقي */
.row {
    margin-left: 0;
    margin-right: 0;
    overflow: hidden;
}

.col-md-6, .col-12 {
    padding-left: 7.5px;
    padding-right: 7.5px;
    overflow: hidden;
}

/* تحسين عرض النصوص الطويلة */
.customer-item {
    word-break: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
}

/* Pagination Styling */
.pagination-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin-top: 10px;
    padding: 8px 0;
}

.pagination-btn {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 4px 8px;
    font-size: 11px;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination-btn:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
    color: #475569;
}

.pagination-btn.active {
    background: #3b82f6;
    border-color: #3b82f6;
    color: #ffffff;
}

.pagination-btn:disabled {
    background: #f8fafc;
    border-color: #e2e8f0;
    color: #cbd5e1;
    cursor: not-allowed;
}

.pagination-info {
    font-size: 10px;
    color: #64748b;
    margin: 0 8px;
}

/* عداد العناصر المصغر */
.items-count {
    font-size: 10px;
    color: #64748b;
    margin-top: 5px;
    text-align: center;
}

/* Analytics Export Buttons */
.analytics-actions,
.comparison-actions,
.stats-actions,
.differences-actions {
    display: flex;
    gap: 5px;
    margin-left: 10px;
}

.export-btn {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 6px 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 12px;
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.export-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.excel-btn {
    color: #059669;
    border-color: #059669;
}

.excel-btn:hover {
    background: #f0fdf4;
    color: #047857;
    border-color: #047857;
}

.pdf-btn {
    color: #dc2626;
    border-color: #dc2626;
}

.pdf-btn:hover {
    background: #fef2f2;
    color: #b91c1c;
    border-color: #b91c1c;
}

/* Analytics Header Styling */
.analytics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e2e8f0;
}

.analytics-header h5 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
}

.comparison-header,
.stats-header,
.differences-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px 20px;
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.comparison-header h4,
.stats-header h4,
.differences-header h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 8px;
}

.comparison-header i,
.stats-header i,
.differences-header i {
    font-size: 20px;
    color: #3b82f6;
}

/* Responsive adjustments for export buttons */
@media (max-width: 768px) {
    .analytics-actions,
    .comparison-actions,
    .stats-actions,
    .differences-actions {
        gap: 3px;
        margin-left: 5px;
    }

    .export-btn {
        padding: 4px 6px;
        min-width: 28px;
        height: 28px;
        font-size: 11px;
    }

    .analytics-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .comparison-header,
    .stats-header,
    .differences-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
        padding: 12px 15px;
    }

    .comparison-header h4,
    .stats-header h4,
    .differences-header h4 {
        font-size: 16px;
    }
}

/* Fixed Headers and Content Styling */
.analytics-header-fixed,
.comparison-header-fixed,
.stats-header-fixed {
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    padding: 8px 15px; /* تقليل المسافة الداخلية */
    border-radius: 8px 8px 0 0;
    margin-bottom: 0;
}

.analytics-header-fixed h4,
.comparison-header-fixed h4,
.stats-header-fixed h4 {
    margin: 0;
    font-size: 14px; /* تصغير حجم الخط */
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
}

.analytics-content-fixed,
.comparison-content-fixed,
.stats-content-fixed {
    display: block !important; /* إزالة القابلية للطي */
    padding: 10px 15px; /* تقليل المسافة الداخلية */
    background: #ffffff;
    border-radius: 0 0 8px 8px;
    border: 1px solid #e2e8f0;
    border-top: none;
}

/* تقليل المسافات بين الأقسام */
.row.mt-2 {
    margin-top: 0.5rem !important; /* تقليل المسافة بين الصفوف */
}

.analytics-card,
.comparison-card,
.stats-card {
    margin-bottom: 10px; /* تقليل المسافة السفلية */
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* تحسين عرض المحتوى */
.analytics-content-fixed .row,
.comparison-content-fixed .row,
.stats-content-fixed .row {
    margin: 0;
}

.analytics-content-fixed .col-md-6,
.analytics-content-fixed .col-12,
.comparison-content-fixed .col-md-6,
.comparison-content-fixed .col-12,
.stats-content-fixed .col-md-6,
.stats-content-fixed .col-12 {
    padding: 5px; /* تقليل المسافة الداخلية للأعمدة */
}

/* Section Navigation Styling */
.section-navigation {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    flex-wrap: wrap;
}

.nav-btn {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 11px;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
    min-height: 28px;
}

.nav-btn:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
    color: #475569;
    transform: translateY(-1px);
}

.nav-btn.active {
    background: #3b82f6;
    border-color: #3b82f6;
    color: #ffffff;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.nav-btn i {
    font-size: 10px;
}

/* Analytics Header with Navigation */
.analytics-header-fixed {
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    padding: 8px 15px;
    border-radius: 8px 8px 0 0;
    margin-bottom: 0;
}

.analytics-header-fixed h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
}

/* Section Content Animation */
.analytics-content-fixed {
    display: block;
    padding: 10px 15px;
    background: #ffffff;
    border-radius: 0 0 8px 8px;
    border: 1px solid #e2e8f0;
    border-top: none;
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.analytics-content-fixed.hidden {
    display: none;
}

.analytics-content-fixed.fade-in {
    opacity: 1;
    transform: translateY(0);
}

.analytics-content-fixed.fade-out {
    opacity: 0;
    transform: translateY(-10px);
}

/* Container Navigation Styling */
.container-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 15px;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
    border-radius: 0 0 8px 8px;
    margin-top: 10px;
}

.pagination-info {
    font-size: 11px;
    color: #64748b;
    font-weight: 500;
}

.navigation-buttons {
    display: flex;
    gap: 8px;
}

.nav-page-btn {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 4px 8px;
    font-size: 10px;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
    min-height: 24px;
}

.nav-page-btn:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
    color: #475569;
    transform: translateY(-1px);
}

.nav-page-btn:disabled {
    background: #f8fafc;
    border-color: #e2e8f0;
    color: #cbd5e1;
    cursor: not-allowed;
    transform: none;
}

.nav-page-btn i {
    font-size: 9px;
}

/* Responsive Navigation */
@media (max-width: 768px) {
    .section-navigation {
        gap: 4px;
    }

    .nav-btn {
        padding: 4px 8px;
        font-size: 10px;
        min-height: 24px;
    }

    .nav-btn i {
        font-size: 9px;
    }

    .container-navigation {
        padding: 6px 10px;
        flex-direction: column;
        gap: 6px;
    }

    .pagination-info {
        font-size: 10px;
    }

    .nav-page-btn {
        padding: 3px 6px;
        font-size: 9px;
        min-height: 20px;
    }
}

/* Analytics Sections */
.analytics-section {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    height: 100%;
}

.analytics-section h5 {
    margin: 0 0 10px 0;
    font-size: 13px;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
}

.customers-count {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 15px;
    padding: 8px 12px;
    background: #f1f5f9;
    border-radius: 6px;
}

.count-number {
    font-size: 18px;
    font-weight: 700;
    color: #3b82f6;
}

.count-label {
    font-size: 11px;
    color: #64748b;
}

.customers-list {
    max-height: 200px;
    overflow-y: auto;
}

.customer-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 10px;
    margin-bottom: 6px;
    border-radius: 6px;
    font-size: 11px;
    border: 1px solid #e2e8f0;
}

.new-customer {
    background: #f0f9ff;
    border-color: #3b82f6;
}

.inactive-customer {
    background: #fef2f2;
    border-color: #ef4444;
}

.customer-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.customer-name {
    font-weight: 600;
    color: #1e293b;
}

.customer-city {
    color: #64748b;
    font-size: 10px;
}

.customer-amount {
    font-weight: 600;
    color: #059669;
}

.customer-invoice {
    font-size: 10px;
    color: #64748b;
}

.analysis-table-container {
    overflow-x: auto;
}

.analysis-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 11px;
}

.analysis-table th,
.analysis-table td {
    padding: 6px 8px;
    text-align: center;
    border: 1px solid #e2e8f0;
}

.analysis-table th {
    background: #f8fafc;
    font-weight: 600;
    color: #374151;
}

.comparison-positive {
    color: #059669;
    font-weight: 600;
}

.comparison-negative {
    color: #dc2626;
    font-weight: 600;
}

.comparison-active {
    color: #3b82f6;
    font-weight: 600;
}

.comparison-neutral {
    color: #6b7280;
    font-weight: 600;
}

.no-data {
    text-align: center;
    color: #9ca3af;
    font-style: italic;
    padding: 20px;
}

/* Stats Sections */
.stats-section {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 15px;
    height: 100%;
}

.stats-section h5 {
    margin: 0 0 15px 0;
    font-size: 13px;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
}

.top-debts-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.top-debt-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 10px;
    margin-bottom: 8px;
    background: #f8fafc;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    font-size: 11px;
}

.debt-rank {
    background: #3b82f6;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 10px;
    flex-shrink: 0;
}

.debt-info {
    flex: 1;
}

.debt-customer {
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 2px;
}

.debt-details {
    color: #64748b;
    font-size: 10px;
}

.debt-amount {
    font-weight: 600;
    color: #059669;
    text-align: right;
}

/* Comparison Styling */
.comparison-item {
    background: #f8fafc;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e2e8f0;
    height: 100%;
}

.comparison-item h5 {
    margin: 0 0 15px 0;
    font-size: 13px;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
}

.comparison-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e2e8f0;
}

.comparison-row:last-child {
    border-bottom: none;
}

.comparison-label {
    font-size: 12px;
    color: #64748b;
    font-weight: 500;
}

.comparison-value {
    font-size: 13px;
    font-weight: 600;
}

.comparison-item h5 {
    font-size: 13px;
    margin-bottom: 8px;
    color: #1e293b;
    font-weight: 600;
}

.comparison-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 0;
    font-size: 12px;
}

.comparison-label {
    color: #64748b;
    font-weight: 500;
}

.comparison-value {
    font-weight: 600;
}

.comparison-positive {
    color: #059669;
}

.comparison-negative {
    color: #dc2626;
}

.comparison-neutral {
    color: #6b7280;
}

.comparison-active {
    color: #3b82f6;
}

/* Stats Section Styling */
.stats-section {
    background: #f8fafc;
    border-radius: 6px;
    padding: 10px;
    border: 1px solid #e2e8f0;
}

.stats-section h5 {
    font-size: 13px;
    margin-bottom: 8px;
    color: #1e293b;
    font-weight: 600;
}

.top-debts-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.top-debt-item {
    display: flex;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid #e2e8f0;
    font-size: 11px;
}

.top-debt-item:last-child {
    border-bottom: none;
}

.debt-rank {
    background: #3b82f6;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 600;
    margin-left: 8px;
    flex-shrink: 0;
}

.debt-info {
    flex: 1;
    margin-left: 8px;
    min-width: 0;
}

.debt-customer {
    font-weight: 600;
    color: #1e293b;
    font-size: 11px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.debt-details {
    color: #64748b;
    font-size: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.debt-amount {
    font-weight: 600;
    color: #059669;
    font-size: 11px;
    flex-shrink: 0;
}

/* Analysis Table Styling */
.analysis-table-container {
    overflow-x: auto;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.analysis-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 11px;
    margin: 0;
}

.analysis-table th,
.analysis-table td {
    padding: 6px 8px;
    text-align: center;
    border-bottom: 1px solid #e2e8f0;
}

.analysis-table th {
    background: #f8fafc;
    font-weight: 600;
    color: #1e293b;
    font-size: 10px;
}

.analysis-table td {
    color: #374151;
}

.analysis-table tr:last-child td {
    border-bottom: none;
}

/* Horizontal Summary Stats */
.summary-stats-horizontal {
    display: flex;
    border-radius: 6px;
    padding: 10px;
    border: 1px solid #e2e8f0;
}

.comparison-item h5 {
    font-size: 13px;
    margin-bottom: 8px;
    color: #1e293b;
    font-weight: 600;
}

.comparison-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 0;
    font-size: 12px;
}

.comparison-label {
    color: #64748b;
    font-weight: 500;
}

.comparison-value {
    font-weight: 600;
}

.comparison-positive {
    color: #059669;
}

.comparison-negative {
    color: #dc2626;
}

.comparison-neutral {
    color: #6b7280;
}

.comparison-active {
    color: #3b82f6;
}

/* Stats Section Styling */
.stats-section {
    background: #f8fafc;
    border-radius: 6px;
    padding: 10px;
    border: 1px solid #e2e8f0;
}

.stats-section h5 {
    font-size: 13px;
    margin-bottom: 8px;
    color: #1e293b;
    font-weight: 600;
}

.top-debts-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.top-debt-item {
    display: flex;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid #e2e8f0;
    font-size: 11px;
}

.top-debt-item:last-child {
    border-bottom: none;
}

.debt-rank {
    background: #3b82f6;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 600;
    margin-left: 8px;
    flex-shrink: 0;
}

.debt-info {
    flex: 1;
    margin-left: 8px;
    min-width: 0;
}

.debt-customer {
    font-weight: 600;
    color: #1e293b;
    font-size: 11px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.debt-details {
    color: #64748b;
    font-size: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.debt-amount {
    font-weight: 600;
    color: #059669;
    font-size: 11px;
    flex-shrink: 0;
}

/* Analysis Table Styling */
.analysis-table-container {
    overflow-x: auto;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.analysis-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 11px;
    margin: 0;
}

.analysis-table th,
.analysis-table td {
    padding: 6px 8px;
    text-align: center;
    border-bottom: 1px solid #e2e8f0;
}

.analysis-table th {
    background: #f8fafc;
    font-weight: 600;
    color: #1e293b;
    font-size: 10px;
}

.analysis-table td {
    color: #374151;
}

.analysis-table tr:last-child td {
    border-bottom: none;
}

/* Horizontal Summary Stats */
.summary-stats-horizontal {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: space-around;
}

.summary-stats-horizontal .stat-item {
    flex: 1;
    min-width: 200px;
    text-align: center;
    padding: 15px 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 12px;
    border: 2px solid #cbd5e1;
    transition: all 0.3s ease;
}

.summary-stats-horizontal .stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.summary-stats-horizontal .stat-label {
    display: block;
    font-size: 12px;
    color: #64748b;
    margin-bottom: 8px;
    font-weight: 600;
}

.summary-stats-horizontal .stat-value {
    display: block;
    font-size: 18px;
    font-weight: 700;
}

/* Responsive adjustments for horizontal layout */
@media (max-width: 768px) {
    .summary-stats-horizontal {
        flex-direction: column;
        gap: 10px;
    }

    .summary-stats-horizontal .stat-item {
        min-width: auto;
    }

    .customers-list {
        max-height: 200px;
    }

    .analysis-table-container {
        max-height: 250px;
    }
}

/* Analytics Responsive */
@media (max-width: 768px) {
    .analytics-header, .comparison-header, .stats-header {
        padding: 12px 15px;
        flex-direction: column;
        gap: 10px;
    }

    .analytics-title, .comparison-header h4, .stats-header h4 {
        font-size: 1rem;
    }

    .analytics-controls {
        width: 100%;
        justify-content: center;
    }

    .analysis-table {
        font-size: 11px;
    }

    .analysis-table th, .analysis-table td {
        padding: 6px 8px;
    }

    .top-debt-item {
        padding: 10px;
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .debt-info {
        margin: 0;
    }

    .comparison-item {
        padding: 12px;
    }

    .comparison-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}

/* Info indicator for statistics */
.info-indicator {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    border: 2px solid #17a2b8;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    color: #0c5460;
    font-weight: 500;
    text-align: center;
}

.info-indicator i {
    margin-left: 8px;
    color: #17a2b8;
}

/* Customer invoice numbers styling - Simple */
.customer-invoices-list {
    font-size: 0.7rem; /* تكبير للوضوح */
    color: #6b7280;
    display: inline-block;
    margin: 0;
    font-style: italic;
    background: #f9fafb; /* خلفية بسيطة */
    padding: 2px 4px;
    border-radius: 3px;
    border: 1px solid #e5e7eb;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100px; /* زيادة العرض */
}

/* Debt info styling - Compact */
.debt-info {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.debt-invoice {
    font-size: 9px; /* تصغير الخط */
    color: #6c757d;
    margin-top: 1px; /* تقليل المسافة */
    font-style: italic;
}
