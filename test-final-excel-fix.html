<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نهائي - إصلاح فصل الأعمدة في Excel</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 28px;
        }
        .test-btn {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            width: calc(50% - 20px);
            display: inline-block;
        }
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
        }
        .test-btn.primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            width: calc(100% - 20px);
        }
        .test-btn.primary:hover {
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            direction: ltr;
            text-align: left;
        }
        .success { background: #d4edda; color: #155724; border-color: #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border-color: #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border-color: #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border-color: #ffeaa7; }
        .expected-result {
            background: #e8f5e8;
            border: 2px solid #4caf50;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .expected-result h3 {
            color: #2e7d32;
            margin-top: 0;
        }
        .column-layout {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 10px;
            margin: 15px 0;
            padding: 15px;
            background: #f0f0f0;
            border-radius: 8px;
        }
        .column-header {
            background: #2c3e50;
            color: white;
            padding: 10px;
            text-align: center;
            border-radius: 4px;
            font-weight: bold;
        }
        .column-data {
            background: white;
            padding: 8px;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 4px;
            min-height: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 اختبار نهائي - إصلاح فصل الأعمدة في Excel</h1>
        
        <div class="expected-result">
            <h3>🎯 النتيجة المتوقعة بعد الإصلاح:</h3>
            <p><strong>يجب أن تظهر البيانات في Excel كما يلي:</strong></p>
            
            <div class="column-layout">
                <div class="column-header">A<br>رقم الفاتورة</div>
                <div class="column-header">B<br>اسم العميل</div>
                <div class="column-header">C<br>المدينة</div>
                <div class="column-header">D<br>المبلغ</div>
                <div class="column-header">E<br>الملاحظات</div>
                
                <div class="column-data">001</div>
                <div class="column-data">أحمد محمد</div>
                <div class="column-data">الرياض</div>
                <div class="column-data">1500</div>
                <div class="column-data">ملاحظة تجريبية</div>
                
                <div class="column-data">002</div>
                <div class="column-data">فاطمة علي</div>
                <div class="column-data">جدة</div>
                <div class="column-data">2000</div>
                <div class="column-data">ملاحظة أخرى</div>
                
                <div class="column-data">003</div>
                <div class="column-data">محمد سالم</div>
                <div class="column-data">الدمام</div>
                <div class="column-data">1750</div>
                <div class="column-data">ملاحظة ثالثة</div>
            </div>
            
            <p><strong>🚫 يجب ألا تظهر البيانات في عمود واحد كما كان يحدث سابقاً!</strong></p>
        </div>

        <button class="test-btn primary" onclick="testUltimateExcelFix()">
            🚀 اختبار الحل النهائي لفصل الأعمدة
        </button>

        <button class="test-btn" onclick="openMainApp()">
            🌐 فتح التطبيق الرئيسي
        </button>

        <button class="test-btn" onclick="clearLog()">
            🗑️ مسح السجل
        </button>

        <div id="log" class="log" style="display: none;"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            logDiv.style.display = 'block';
            logDiv.className = `log ${type}`;
            logDiv.innerHTML += `<div>[${new Date().toLocaleTimeString()}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            const logDiv = document.getElementById('log');
            logDiv.innerHTML = '';
            logDiv.style.display = 'none';
        }

        // Test data with Arabic content
        const ultimateTestData = [
            {
                invoiceNumber: '001',
                customerName: 'أحمد محمد علي الأحمدي',
                city: 'الرياض',
                amount: 1500.75,
                notes: 'ملاحظة تجريبية طويلة مع نص عربي'
            },
            {
                invoiceNumber: '002',
                customerName: 'فاطمة سالم عبدالله',
                city: 'جدة',
                amount: 2000.50,
                notes: 'ملاحظة أخرى مع تفاصيل إضافية'
            },
            {
                invoiceNumber: '003',
                customerName: 'محمد عبدالله الزهراني',
                city: 'الدمام',
                amount: 1750.25,
                notes: 'ملاحظة ثالثة مع معلومات مهمة'
            },
            {
                invoiceNumber: '004',
                customerName: 'نورا أحمد السعيد',
                city: 'مكة المكرمة',
                amount: 3000.00,
                notes: 'ملاحظة رابعة مع تفاصيل شاملة'
            },
            {
                invoiceNumber: '005',
                customerName: 'خالد محمد القحطاني',
                city: 'المدينة المنورة',
                amount: 2500.80,
                notes: 'ملاحظة خامسة مع بيانات إضافية'
            }
        ];

        function testUltimateExcelFix() {
            clearLog();
            log('🚀 بدء الاختبار النهائي لإصلاح فصل الأعمدة...', 'info');
            
            try {
                // Check XLSX library
                if (typeof XLSX === 'undefined') {
                    log('❌ مكتبة XLSX غير متوفرة', 'error');
                    return;
                }
                log('✅ مكتبة XLSX متوفرة', 'success');

                log('🔧 إنشاء Excel مع ضمان مطلق لفصل الأعمدة...', 'info');
                
                // Create workbook
                const workbook = XLSX.utils.book_new();
                
                // Create empty worksheet
                const worksheet = {};
                
                // Create headers manually in row 1
                log('📋 إنشاء headers يدوياً...', 'info');
                worksheet['A1'] = { v: 'رقم الفاتورة', t: 's' };
                worksheet['B1'] = { v: 'اسم العميل', t: 's' };
                worksheet['C1'] = { v: 'المدينة', t: 's' };
                worksheet['D1'] = { v: 'المبلغ', t: 's' };
                worksheet['E1'] = { v: 'الملاحظات', t: 's' };
                
                log('✅ Headers تم إنشاؤها:', 'success');
                log(`  A1: "${worksheet['A1'].v}"`, 'info');
                log(`  B1: "${worksheet['B1'].v}"`, 'info');
                log(`  C1: "${worksheet['C1'].v}"`, 'info');
                log(`  D1: "${worksheet['D1'].v}"`, 'info');
                log(`  E1: "${worksheet['E1'].v}"`, 'info');

                // Add data rows with precise cell targeting
                log('📊 إضافة البيانات صف بصف...', 'info');
                ultimateTestData.forEach((item, index) => {
                    const rowNum = index + 2; // +2 because row 1 is headers and indexing starts from 1
                    
                    // Column A: Invoice Number - precise cell targeting
                    worksheet[`A${rowNum}`] = {
                        v: String(item.invoiceNumber || '').trim(),
                        t: 's'
                    };
                    
                    // Column B: Customer Name - precise cell targeting
                    worksheet[`B${rowNum}`] = {
                        v: String(item.customerName || '').trim(),
                        t: 's'
                    };
                    
                    // Column C: City - precise cell targeting
                    worksheet[`C${rowNum}`] = {
                        v: String(item.city || '').trim(),
                        t: 's'
                    };
                    
                    // Column D: Amount - precise cell targeting as number
                    worksheet[`D${rowNum}`] = {
                        v: parseFloat(item.amount) || 0,
                        t: 'n',
                        z: '#,##0.00'
                    };
                    
                    // Column E: Notes - precise cell targeting
                    worksheet[`E${rowNum}`] = {
                        v: String(item.notes || '').trim(),
                        t: 's'
                    };
                    
                    log(`✅ صف ${rowNum} تم إنشاؤه:`, 'success');
                    log(`  A${rowNum}: "${worksheet[`A${rowNum}`].v}"`, 'info');
                    log(`  B${rowNum}: "${worksheet[`B${rowNum}`].v}"`, 'info');
                    log(`  C${rowNum}: "${worksheet[`C${rowNum}`].v}"`, 'info');
                    log(`  D${rowNum}: ${worksheet[`D${rowNum}`].v}`, 'info');
                    log(`  E${rowNum}: "${worksheet[`E${rowNum}`].v}"`, 'info');
                });

                // Set worksheet range precisely
                const range = `A1:E${ultimateTestData.length + 1}`;
                worksheet['!ref'] = range;
                log(`📐 نطاق الورقة: ${range}`, 'info');

                // Set column widths
                worksheet['!cols'] = [
                    { wch: 15 }, // A: رقم الفاتورة
                    { wch: 30 }, // B: اسم العميل
                    { wch: 20 }, // C: المدينة
                    { wch: 15 }, // D: المبلغ
                    { wch: 35 }  // E: الملاحظات
                ];

                // Add formatting to headers
                ['A1', 'B1', 'C1', 'D1', 'E1'].forEach(cell => {
                    if (worksheet[cell]) {
                        worksheet[cell].s = {
                            font: { bold: true, color: { rgb: "000000" } },
                            fill: { fgColor: { rgb: "CCCCCC" } },
                            alignment: { horizontal: "center", vertical: "center" },
                            border: {
                                top: { style: "thin", color: { rgb: "000000" } },
                                bottom: { style: "thin", color: { rgb: "000000" } },
                                left: { style: "thin", color: { rgb: "000000" } },
                                right: { style: "thin", color: { rgb: "000000" } }
                            }
                        };
                    }
                });

                // Add worksheet to workbook
                XLSX.utils.book_append_sheet(workbook, worksheet, 'بيانات الاختبار النهائي');
                
                log('✅ تم إنشاء الورقة وإضافتها للمصنف', 'success');

                // Generate filename
                const fileName = `اختبار_نهائي_فصل_الأعمدة_${new Date().toISOString().split('T')[0]}.xlsx`;

                // Save file
                XLSX.writeFile(workbook, fileName, {
                    bookType: 'xlsx',
                    type: 'binary',
                    cellStyles: true
                });

                log(`✅ تم حفظ الملف: ${fileName}`, 'success');
                log('🎉 الاختبار النهائي مكتمل!', 'success');
                log('', 'info');
                log('🔍 تحقق من الملف المحفوظ:', 'warning');
                log('• يجب أن تكون البيانات في أعمدة منفصلة تماماً', 'warning');
                log('• العمود A: أرقام الفواتير فقط', 'warning');
                log('• العمود B: أسماء العملاء فقط', 'warning');
                log('• العمود C: المدن فقط', 'warning');
                log('• العمود D: المبالغ فقط (كأرقام)', 'warning');
                log('• العمود E: الملاحظات فقط', 'warning');
                log('', 'info');
                log('🚫 إذا ظهرت البيانات في عمود واحد، فهناك مشكلة!', 'error');

            } catch (error) {
                log(`❌ خطأ في الاختبار النهائي: ${error.message}`, 'error');
                console.error('Ultimate test error:', error);
            }
        }

        function openMainApp() {
            window.open('http://localhost:5000', '_blank');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('🎯 صفحة الاختبار النهائي جاهزة', 'info');
            log('📋 هذا هو الاختبار الأخير لضمان فصل الأعمدة', 'info');
            log('🔧 تم استخدام طريقة تحديد الخلايا المباشرة (A1, B1, C1...)', 'info');
            log('✅ اضغط الزر أعلاه لبدء الاختبار', 'success');
        });
    </script>
</body>
</html>
