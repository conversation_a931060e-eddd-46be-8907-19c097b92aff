const { app, BrowserWindow, Menu, dialog, shell, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');

// تعطيل تحذيرات الأمان في بيئة التطوير
process.env['ELECTRON_DISABLE_SECURITY_WARNINGS'] = 'true';

class DebtManagerApp {
    constructor() {
        this.mainWindow = null;
        this.isDev = process.argv.includes('--dev');
        this.isReady = false;
    }

    // إنشاء النافذة الرئيسية
    createMainWindow() {
        this.mainWindow = new BrowserWindow({
            width: 1400,
            height: 900,
            minWidth: 1000,
            minHeight: 700,
            icon: this.getIconPath(),
            title: 'نظام إدارة ديون العملاء',
            show: false, // لا تظهر حتى تكون جاهزة
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                enableRemoteModule: false,
                webSecurity: true,
                allowRunningInsecureContent: false,
                preload: path.join(__dirname, 'preload.js')
            },
            titleBarStyle: 'default',
            backgroundColor: '#667eea',
            center: true,
            resizable: true,
            maximizable: true,
            minimizable: true,
            closable: true
        });

        // تحميل الصفحة الرئيسية
        const indexPath = path.join(__dirname, '..', 'html', 'index.html');
        this.mainWindow.loadFile(indexPath);

        // إظهار النافذة عند الجاهزية
        this.mainWindow.once('ready-to-show', () => {
            this.mainWindow.show();
            
            // فتح أدوات المطور في بيئة التطوير
            if (this.isDev) {
                this.mainWindow.webContents.openDevTools();
            }
        });

        // التعامل مع إغلاق النافذة
        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });

        // منع التنقل خارج التطبيق
        this.mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
            const parsedUrl = new URL(navigationUrl);
            
            if (parsedUrl.origin !== 'file://') {
                event.preventDefault();
                shell.openExternal(navigationUrl);
            }
        });

        // التعامل مع النوافذ الجديدة
        this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
            shell.openExternal(url);
            return { action: 'deny' };
        });

        console.log('✅ تم إنشاء النافذة الرئيسية بنجاح');
    }

    // الحصول على مسار الأيقونة
    getIconPath() {
        const iconName = process.platform === 'win32' ? 'icon.ico' : 
                        process.platform === 'darwin' ? 'icon.icns' : 'icon.png';
        return path.join(__dirname, '..', 'build', iconName);
    }

    // إنشاء القائمة
    createMenu() {
        const template = [
            {
                label: 'ملف',
                submenu: [
                    {
                        label: 'جديد',
                        accelerator: 'CmdOrCtrl+N',
                        click: () => {
                            this.mainWindow.webContents.send('menu-action', 'new');
                        }
                    },
                    {
                        label: 'فتح',
                        accelerator: 'CmdOrCtrl+O',
                        click: () => {
                            this.openFile();
                        }
                    },
                    {
                        label: 'حفظ',
                        accelerator: 'CmdOrCtrl+S',
                        click: () => {
                            this.mainWindow.webContents.send('menu-action', 'save');
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'تصدير CSV',
                        click: () => {
                            this.mainWindow.webContents.send('menu-action', 'export-csv');
                        }
                    },
                    {
                        label: 'تصدير Excel',
                        click: () => {
                            this.mainWindow.webContents.send('menu-action', 'export-excel');
                        }
                    },
                    {
                        label: 'طباعة PDF',
                        accelerator: 'CmdOrCtrl+P',
                        click: () => {
                            this.mainWindow.webContents.send('menu-action', 'print-pdf');
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'خروج',
                        accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                        click: () => {
                            app.quit();
                        }
                    }
                ]
            },
            {
                label: 'تحرير',
                submenu: [
                    {
                        label: 'تراجع',
                        accelerator: 'CmdOrCtrl+Z',
                        role: 'undo'
                    },
                    {
                        label: 'إعادة',
                        accelerator: 'Shift+CmdOrCtrl+Z',
                        role: 'redo'
                    },
                    { type: 'separator' },
                    {
                        label: 'قص',
                        accelerator: 'CmdOrCtrl+X',
                        role: 'cut'
                    },
                    {
                        label: 'نسخ',
                        accelerator: 'CmdOrCtrl+C',
                        role: 'copy'
                    },
                    {
                        label: 'لصق',
                        accelerator: 'CmdOrCtrl+V',
                        role: 'paste'
                    },
                    {
                        label: 'تحديد الكل',
                        accelerator: 'CmdOrCtrl+A',
                        role: 'selectall'
                    }
                ]
            },
            {
                label: 'عرض',
                submenu: [
                    {
                        label: 'إعادة تحميل',
                        accelerator: 'CmdOrCtrl+R',
                        click: () => {
                            this.mainWindow.reload();
                        }
                    },
                    {
                        label: 'تكبير',
                        accelerator: 'CmdOrCtrl+Plus',
                        click: () => {
                            this.mainWindow.webContents.setZoomLevel(
                                this.mainWindow.webContents.getZoomLevel() + 0.5
                            );
                        }
                    },
                    {
                        label: 'تصغير',
                        accelerator: 'CmdOrCtrl+-',
                        click: () => {
                            this.mainWindow.webContents.setZoomLevel(
                                this.mainWindow.webContents.getZoomLevel() - 0.5
                            );
                        }
                    },
                    {
                        label: 'حجم طبيعي',
                        accelerator: 'CmdOrCtrl+0',
                        click: () => {
                            this.mainWindow.webContents.setZoomLevel(0);
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'ملء الشاشة',
                        accelerator: process.platform === 'darwin' ? 'Ctrl+Cmd+F' : 'F11',
                        click: () => {
                            this.mainWindow.setFullScreen(!this.mainWindow.isFullScreen());
                        }
                    }
                ]
            },
            {
                label: 'مساعدة',
                submenu: [
                    {
                        label: 'حول البرنامج',
                        click: () => {
                            this.showAbout();
                        }
                    },
                    {
                        label: 'تعليمات الاستخدام',
                        click: () => {
                            this.mainWindow.webContents.send('menu-action', 'help');
                        }
                    }
                ]
            }
        ];

        // إضافة قائمة خاصة بـ macOS
        if (process.platform === 'darwin') {
            template.unshift({
                label: app.getName(),
                submenu: [
                    {
                        label: 'حول ' + app.getName(),
                        role: 'about'
                    },
                    { type: 'separator' },
                    {
                        label: 'إخفاء ' + app.getName(),
                        accelerator: 'Command+H',
                        role: 'hide'
                    },
                    {
                        label: 'إخفاء الآخرين',
                        accelerator: 'Command+Shift+H',
                        role: 'hideothers'
                    },
                    {
                        label: 'إظهار الكل',
                        role: 'unhide'
                    },
                    { type: 'separator' },
                    {
                        label: 'خروج',
                        accelerator: 'Command+Q',
                        click: () => {
                            app.quit();
                        }
                    }
                ]
            });
        }

        const menu = Menu.buildFromTemplate(template);
        Menu.setApplicationMenu(menu);
    }

    // فتح ملف
    async openFile() {
        const result = await dialog.showOpenDialog(this.mainWindow, {
            properties: ['openFile'],
            filters: [
                { name: 'JSON Files', extensions: ['json'] },
                { name: 'CSV Files', extensions: ['csv'] },
                { name: 'All Files', extensions: ['*'] }
            ]
        });

        if (!result.canceled && result.filePaths.length > 0) {
            const filePath = result.filePaths[0];
            this.mainWindow.webContents.send('file-opened', filePath);
        }
    }

    // إظهار معلومات البرنامج
    showAbout() {
        dialog.showMessageBox(this.mainWindow, {
            type: 'info',
            title: 'حول البرنامج',
            message: 'نظام إدارة ديون العملاء',
            detail: `الإصدار: ${app.getVersion()}\n\nنظام شامل لإدارة ديون العملاء مع إحصائيات متقدمة\nيعمل بدون إنترنت مع حفظ البيانات محلياً\n\nتم التطوير باستخدام Electron و JavaScript`,
            buttons: ['موافق']
        });
    }

    // تهيئة التطبيق
    init() {
        // التعامل مع جاهزية التطبيق
        app.whenReady().then(() => {
            this.createMainWindow();
            this.createMenu();
            this.isReady = true;
            console.log('✅ تم تهيئة التطبيق بنجاح');
        });

        // التعامل مع إغلاق جميع النوافذ
        app.on('window-all-closed', () => {
            if (process.platform !== 'darwin') {
                app.quit();
            }
        });

        // التعامل مع تفعيل التطبيق (macOS)
        app.on('activate', () => {
            if (BrowserWindow.getAllWindows().length === 0) {
                this.createMainWindow();
            }
        });

        // التعامل مع الأخطاء
        process.on('uncaughtException', (error) => {
            console.error('خطأ غير متوقع:', error);
        });

        console.log('🚀 بدء تشغيل نظام إدارة ديون العملاء...');
    }
}

// إنشاء وتشغيل التطبيق
const debtManagerApp = new DebtManagerApp();
debtManagerApp.init();
