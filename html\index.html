<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة ديون العملاء</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/css/style.css" rel="stylesheet">

    <!-- Libraries for PDF and Excel -->
    <script src="https://unpkg.com/jspdf@latest/dist/jspdf.umd.min.js"></script>
    <script src="https://unpkg.com/jspdf-autotable@latest/dist/jspdf.plugin.autotable.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <!-- HTML to Canvas for Arabic text -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
</head>
<body>
    <div class="container-fluid p-2">
        <!-- Header Section -->
        <div class="row mb-2">
            <div class="col-md-6">
                <h1 class="page-title text-start">
                    <i class="fas fa-calculator me-3"></i>
                    نظام إدارة ديون العملاء
                </h1>
            </div>
            <div class="col-md-6">
                <div class="search-container">
                    <input type="text" class="form-control search-input" id="searchInput"
                           placeholder="🔍 البحث في جميع البيانات...">
                    <button type="button" id="clearSearchBtn" class="clear-search-btn" onclick="debtManager.clearSearch()" title="إلغاء البحث" style="display: none;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Input Form -->
        <div class="row mb-2">
            <div class="col-12">
                <div class="input-form-container">
                    <form id="debtForm" class="input-form">
                        <div class="form-row">
                            <div class="form-group">
                                <input type="text" class="form-control" id="invoiceNumber" placeholder="رقم الفاتورة" required>
                            </div>
                            <div class="form-group">
                                <input type="text" class="form-control" id="customerName" placeholder="اسم العميل" required>
                            </div>
                            <div class="form-group">
                                <input type="text" class="form-control" id="city" placeholder="المدينة" required>
                            </div>
                            <div class="form-group">
                                <input type="number" class="form-control" id="amount" placeholder="المبلغ" step="0.01" required>
                            </div>
                            <div class="form-group">
                                <input type="text" class="form-control" id="notes" placeholder="الملاحظات">
                            </div>
                        </div>
                        <div class="form-buttons">
                            <button type="submit" class="btn btn-add">إضافة</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Data Tables Section -->
        <div class="row">
            <!-- Previous Debt (Reference Only) -->
            <div class="col-md-6">
                <div class="debt-card previous-debt-card">
                    <div class="debt-header">
                        <div class="debt-title">
                            <span class="debt-badge previous-debt-badge">الدين السابق</span>
                            <div class="debt-actions-menu">
                                <div class="dropdown">
                                    <button class="dropdown-btn" onclick="toggleDropdown('previousDebtDropdown')">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <div class="dropdown-content" id="previousDebtDropdown">
                                        <a href="#" onclick="safePrintPDF('previous')">
                                            <i class="fas fa-file-pdf"></i> طباعة PDF
                                        </a>
                                        <a href="#" onclick="exportCSV('previous')">
                                            <i class="fas fa-file-csv"></i> تصدير إلى CSV
                                        </a>
                                        <a href="#" onclick="exportExcel('previous')">
                                            <i class="fas fa-file-excel"></i> تصدير إلى Excel
                                        </a>
                                        <a href="#" onclick="document.getElementById('importPreviousFile').click()">
                                            <i class="fas fa-file-import"></i> استيراد من CSV/Excel
                                        </a>
                                    </div>
                                </div>
                                <i class="fas fa-history debt-icon"></i>
                            </div>
                        </div>
                        <div class="debt-summary">
                            <div class="summary-item">
                                <span class="summary-label">العملاء</span>
                                <span class="summary-value" id="previousDebtCustomers">0</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">الفواتير</span>
                                <span class="summary-value" id="previousDebtInvoices">0</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">المجموع</span>
                                <span class="summary-value" id="previousDebtTotal">0 ر.س</span>
                            </div>
                        </div>
                    </div>
                    <div class="debt-content">
                        <!-- رؤوس الأعمدة للفرز -->
                        <div class="debt-list-header">
                            <div class="header-item sortable-header" data-sort-field="invoiceNumber" data-sort-section="previous" onclick="debtManager.sortData('invoiceNumber', 'previous')">
                                رقم الفاتورة <i class="sort-icon fas fa-sort"></i>
                            </div>
                            <div class="header-item sortable-header" data-sort-field="customerName" data-sort-section="previous" onclick="debtManager.sortData('customerName', 'previous')">
                                اسم العميل <i class="sort-icon fas fa-sort"></i>
                            </div>
                            <div class="header-item sortable-header" data-sort-field="amount" data-sort-section="previous" onclick="debtManager.sortData('amount', 'previous')">
                                المبلغ <i class="sort-icon fas fa-sort"></i>
                            </div>
                        </div>
                        <div id="previousDebtList" class="debt-list">
                            <!-- Previous debt items will be populated here -->
                        </div>
                        <div id="noPreviousDebt" class="empty-state">
                            <i class="fas fa-archive empty-icon"></i>
                            <p>لا توجد ديون سابقة</p>
                        </div>
                        <div class="container-navigation" id="previousDebtsNavigation" style="display: none;">
                            <div class="pagination-info">
                                <span id="previousDebtsPageInfo">صفحة 1 من 1</span>
                            </div>
                            <div class="navigation-buttons">
                                <button class="nav-page-btn" onclick="debtManager.changeMainPage('previousDebts', 'prev')" id="previousDebtsPrevBtn">
                                    <i class="fas fa-chevron-right"></i> السابق
                                </button>
                                <button class="nav-page-btn" onclick="debtManager.changeMainPage('previousDebts', 'next')" id="previousDebtsNextBtn">
                                    التالي <i class="fas fa-chevron-left"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- New Invoices -->
            <div class="col-md-6">
                <div class="debt-card new-invoices-card">
                    <div class="debt-header">
                        <div class="debt-title">
                            <span class="debt-badge new-invoices-badge">الفواتير الجديدة</span>
                            <div class="debt-actions-menu">
                                <div class="dropdown">
                                    <button class="dropdown-btn" onclick="toggleDropdown('newInvoicesDropdown')">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <div class="dropdown-content" id="newInvoicesDropdown">
                                        <a href="#" onclick="safePrintPDF('new')">
                                            <i class="fas fa-file-pdf"></i> طباعة PDF
                                        </a>
                                        <a href="#" onclick="exportCSV('new')">
                                            <i class="fas fa-file-csv"></i> تصدير إلى CSV
                                        </a>
                                        <a href="#" onclick="exportExcel('new')">
                                            <i class="fas fa-file-excel"></i> تصدير إلى Excel
                                        </a>
                                        <a href="#" onclick="document.getElementById('importNewFile').click()">
                                            <i class="fas fa-file-import"></i> استيراد من CSV/Excel
                                        </a>
                                    </div>
                                </div>
                                <i class="fas fa-file-invoice debt-icon"></i>
                            </div>
                        </div>
                        <div class="debt-summary">
                            <div class="summary-item">
                                <span class="summary-label">العملاء</span>
                                <span class="summary-value" id="newInvoicesCustomers">0</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">الفواتير</span>
                                <span class="summary-value" id="newInvoicesCount">0</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">المجموع</span>
                                <span class="summary-value" id="newInvoicesTotal">0 ر.س</span>
                            </div>
                        </div>
                    </div>
                    <div class="debt-content">
                        <!-- رؤوس الأعمدة للفرز -->
                        <div class="debt-list-header">
                            <div class="header-item sortable-header" data-sort-field="invoiceNumber" data-sort-section="new" onclick="debtManager.sortData('invoiceNumber', 'new')">
                                رقم الفاتورة <i class="sort-icon fas fa-sort"></i>
                            </div>
                            <div class="header-item sortable-header" data-sort-field="customerName" data-sort-section="new" onclick="debtManager.sortData('customerName', 'new')">
                                اسم العميل <i class="sort-icon fas fa-sort"></i>
                            </div>
                            <div class="header-item sortable-header" data-sort-field="amount" data-sort-section="new" onclick="debtManager.sortData('amount', 'new')">
                                المبلغ <i class="sort-icon fas fa-sort"></i>
                            </div>
                        </div>
                        <div id="newInvoicesList" class="debt-list">
                            <!-- New invoices will be populated here -->
                        </div>
                        <div id="noNewInvoices" class="empty-state" style="display: none;">
                            <i class="fas fa-inbox empty-icon"></i>
                            <p>لا توجد فواتير جديدة</p>
                        </div>
                        <div class="container-navigation" id="newInvoicesNavigation" style="display: none;">
                            <div class="pagination-info">
                                <span id="newInvoicesPageInfo">صفحة 1 من 1</span>
                            </div>
                            <div class="navigation-buttons">
                                <button class="nav-page-btn" onclick="debtManager.changeMainPage('newInvoices', 'prev')" id="newInvoicesPrevBtn">
                                    <i class="fas fa-chevron-right"></i> السابق
                                </button>
                                <button class="nav-page-btn" onclick="debtManager.changeMainPage('newInvoices', 'next')" id="newInvoicesNextBtn">
                                    التالي <i class="fas fa-chevron-left"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analytics Section -->
        <div class="row mt-2">
            <!-- Detailed Analytics -->
            <div class="col-md-12">
                <div class="analytics-card">
                    <div class="analytics-header-fixed">
                        <h4><i class="fas fa-chart-line me-2"></i>التحليلات</h4>
                        <div class="section-navigation">
                            <button class="nav-btn" onclick="debtManager.showSection('analytics')" id="analyticsNavBtn">
                                <i class="fas fa-chart-line"></i> التحليلات
                            </button>
                            <button class="nav-btn" onclick="debtManager.showSection('comparisons')" id="comparisonsNavBtn">
                                <i class="fas fa-balance-scale"></i> المقارنات
                            </button>
                            <button class="nav-btn" onclick="debtManager.showSection('statistics')" id="statisticsNavBtn">
                                <i class="fas fa-chart-bar"></i> الإحصائيات
                            </button>
                            <button class="nav-btn" onclick="debtManager.showSection('differences')" id="differencesNavBtn">
                                <i class="fas fa-exchange-alt"></i> الفروقات
                            </button>
                        </div>
                    </div>
                    <div class="analytics-content-fixed" id="analyticsContent">
                        <!-- Analytics content will be loaded here -->
                    </div>
                    <div class="analytics-content-fixed" id="comparisonContent" style="display: none;">
                        <!-- Comparison content will be loaded here -->
                    </div>
                    <div class="analytics-content-fixed" id="statsContent" style="display: none;">
                        <!-- Advanced stats content will be loaded here -->
                    </div>
                    <div class="analytics-content-fixed" id="differencesContent" style="display: none;">
                        <!-- Differences content will be loaded here -->
                    </div>
                </div>
            </div>
        </div>


    </div>

    <!-- Hidden file inputs for import -->
    <input type="file" id="importPreviousFile" accept=".xlsx,.xls,.csv" style="display: none;" onchange="handleFileImport(event, 'previous')">
    <input type="file" id="importNewFile" accept=".xlsx,.xls,.csv" style="display: none;" onchange="handleFileImport(event, 'new')">

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/javascript/arabic-font.js"></script>
    <script src="/javascript/app.js"></script>
    <script src="/javascript/export-import.js"></script>
    <script src="/javascript/fix-functions.js"></script>

    <script>
        // Direct import function to ensure it works
        function handleFileImport(event, type) {
            console.log('🔧 handleFileImport called with type:', type);

            const file = event.target.files[0];
            if (!file) {
                console.log('No file selected');
                return;
            }

            console.log('📁 File selected:', file.name);

            // Check if debtManager is available
            if (typeof window.debtManager === 'undefined') {
                alert('نظام إدارة الديون غير جاهز. يرجى إعادة تحميل الصفحة.');
                console.error('debtManager not available');
                return;
            }

            const fileName = file.name.toLowerCase();

            if (fileName.endsWith('.csv')) {
                handleCSVImport(file, type, event);
            } else {
                alert('يرجى اختيار ملف CSV (.csv)');
            }
        }

        function handleCSVImport(file, type, originalEvent) {
            console.log('📂 Starting CSV import for type:', type);

            const reader = new FileReader();

            reader.onload = function(e) {
                try {
                    console.log('📖 File read successfully');
                    let csvData = e.target.result;

                    // Remove BOM if present
                    if (csvData.charCodeAt(0) === 0xFEFF) {
                        csvData = csvData.slice(1);
                    }

                    // Split into lines
                    const lines = csvData.split('\n').filter(line => line.trim());

                    if (lines.length < 2) {
                        alert('الملف فارغ أو لا يحتوي على بيانات');
                        return;
                    }

                    // Detect separator
                    const headerLine = lines[0].trim();
                    const separator = headerLine.includes(';') ? ';' : ',';

                    console.log('📋 Header found:', headerLine);
                    console.log('📋 Separator detected:', separator);

                    // Process data
                    const processedData = [];

                    for (let i = 1; i < lines.length; i++) {
                        const line = lines[i].trim();
                        if (!line) continue;

                        const parts = line.split(separator).map(field => field.trim());

                        if (parts.length >= 4) {
                            const newItem = {
                                id: Date.now() + i + Math.random(),
                                invoiceNumber: parts[0] || '',
                                customerName: parts[1] || '',
                                city: parts[2] || '',
                                amount: parseFloat(parts[3]) || 0,
                                notes: parts[4] || '',
                                date: new Date().toLocaleDateString('en-GB'),
                                timestamp: new Date().toISOString()
                            };

                            // Add all items during import (allow duplicates for import)
                            processedData.push(newItem);
                            console.log('✅ Item processed:', newItem.invoiceNumber);
                        }
                    }

                    console.log('📊 Processed data count:', processedData.length);

                    if (processedData.length > 0) {
                        // Ask user if they want to replace existing data
                        const shouldReplace = confirm(`تم العثور على ${processedData.length} سجل للاستيراد.\n\nهل تريد:\n- موافق: استبدال البيانات الموجودة\n- إلغاء: إضافة للبيانات الموجودة`);

                        // Add to appropriate section
                        if (type === 'previous') {
                            console.log('📥 Adding to previous debts');
                            if (shouldReplace) {
                                debtManager.previousDebts = [...processedData];
                                console.log('🔄 Replaced previous debts');
                            } else {
                                if (!debtManager.previousDebts) {
                                    debtManager.previousDebts = [];
                                }
                                debtManager.previousDebts.unshift(...processedData);
                                console.log('➕ Added to previous debts');
                            }
                            debtManager.savePreviousDebts();
                            debtManager.displayPreviousDebt(debtManager.previousDebts);
                        } else {
                            console.log('📥 Adding to new invoices');
                            if (shouldReplace) {
                                debtManager.debts = [...processedData];
                                console.log('🔄 Replaced new invoices');
                            } else {
                                debtManager.debts.unshift(...processedData);
                                console.log('➕ Added to new invoices');
                            }
                            debtManager.saveData();
                            debtManager.updateStatistics();
                            debtManager.displayRecords();
                        }

                        console.log('✅ Import completed successfully');
                        if (debtManager.showSuccess) {
                            debtManager.showSuccess(`تم استيراد ${processedData.length} سجل بنجاح`);
                        } else {
                            alert(`تم استيراد ${processedData.length} سجل بنجاح`);
                        }
                    } else {
                        console.log('❌ No valid data found');
                        alert('لم يتم العثور على بيانات صالحة');
                    }

                    // Reset file input
                    if (originalEvent && originalEvent.target) {
                        originalEvent.target.value = '';
                    }

                } catch (error) {
                    console.error('CSV Import Error:', error);
                    alert('حدث خطأ أثناء استيراد الملف: ' + error.message);
                }
            };

            reader.onerror = function() {
                alert('خطأ في قراءة الملف');
            };

            reader.readAsText(file, 'UTF-8');
        }

        // Make sure the function is available globally
        window.handleFileImport = handleFileImport;
        window.handleCSVImport = handleCSVImport;

        // Create Arabic Report HTML function
        function createArabicReportHTML(data, title, type) {
            console.log('📄 Creating Arabic report HTML for:', title);

            const currentDate = new Date().toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            });

            let html = `
                <div style="direction: rtl; text-align: right; font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif; padding: 20px; background: white;">
                    <div style="text-align: center; margin-bottom: 30px; border-bottom: 2px solid #2c3e50; padding-bottom: 20px;">
                        <h1 style="color: #2c3e50; font-size: 28px; margin: 0; font-weight: bold;">${title}</h1>
                        <p style="color: #7f8c8d; font-size: 16px; margin: 10px 0 0 0;">${currentDate}</p>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <table style="width: 100%; border-collapse: collapse; font-size: 14px; background: white;">
                            <thead>
                                <tr style="background: linear-gradient(135deg, #3498db, #2980b9); color: white;">
                                    <th style="border: 1px solid #ddd; padding: 12px; text-align: center; font-weight: bold;">رقم الفاتورة</th>
                                    <th style="border: 1px solid #ddd; padding: 12px; text-align: center; font-weight: bold;">اسم العميل</th>
                                    <th style="border: 1px solid #ddd; padding: 12px; text-align: center; font-weight: bold;">المدينة</th>
                                    <th style="border: 1px solid #ddd; padding: 12px; text-align: center; font-weight: bold;">المبلغ (ر.س)</th>
                                    <th style="border: 1px solid #ddd; padding: 12px; text-align: center; font-weight: bold;">الملاحظات</th>
                                </tr>
                            </thead>
                            <tbody>
            `;

            let totalAmount = 0;
            data.forEach((item, index) => {
                const amount = parseFloat(item.amount) || 0;
                totalAmount += amount;

                const rowColor = index % 2 === 0 ? '#f8f9fa' : 'white';

                html += `
                    <tr style="background: ${rowColor};">
                        <td style="border: 1px solid #ddd; padding: 10px; text-align: center; direction: ltr;">${item.invoiceNumber || ''}</td>
                        <td style="border: 1px solid #ddd; padding: 10px; text-align: center; direction: rtl;">${item.customerName || ''}</td>
                        <td style="border: 1px solid #ddd; padding: 10px; text-align: center; direction: rtl;">${item.city || ''}</td>
                        <td style="border: 1px solid #ddd; padding: 10px; text-align: center; direction: ltr; font-weight: bold; color: #27ae60;">${amount.toLocaleString('ar-SA')}</td>
                        <td style="border: 1px solid #ddd; padding: 10px; text-align: center; direction: rtl;">${item.notes || ''}</td>
                    </tr>
                `;
            });

            html += `
                            </tbody>
                            <tfoot>
                                <tr style="background: linear-gradient(135deg, #e74c3c, #c0392b); color: white; font-weight: bold;">
                                    <td colspan="3" style="border: 1px solid #ddd; padding: 12px; text-align: center; font-size: 16px;">المجموع الكلي</td>
                                    <td style="border: 1px solid #ddd; padding: 12px; text-align: center; font-size: 16px; direction: ltr;">${totalAmount.toLocaleString('ar-SA')}</td>
                                    <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">ر.س</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <div style="margin-top: 30px; text-align: center; color: #7f8c8d; font-size: 12px; border-top: 1px solid #ecf0f1; padding-top: 15px;">
                        <p style="margin: 5px 0;">تم إنشاء هذا التقرير بواسطة نظام إدارة ديون العملاء</p>
                        <p style="margin: 5px 0;">عدد السجلات: ${data.length} | إجمالي المبلغ: ${totalAmount.toLocaleString('ar-SA')} ر.س</p>
                    </div>
                </div>
            `;

            return html;
        }

        console.log('✅ Direct import functions loaded');

        // Add missing functions immediately - FIXED to prevent recursion
        window.printPDFDirect = function(type) {
            console.log('🔧 printPDFDirect called with type:', type);

            try {
                // Check for jsPDF
                let jsPDFConstructor = null;
                if (typeof window.jspdf !== 'undefined' && window.jspdf.jsPDF) {
                    jsPDFConstructor = window.jspdf.jsPDF;
                } else if (typeof window.jsPDF !== 'undefined') {
                    jsPDFConstructor = window.jsPDF;
                } else {
                    alert('مكتبة PDF غير متوفرة. يرجى إعادة تحميل الصفحة.');
                    return;
                }

                // Check for debtManager
                if (typeof window.debtManager === 'undefined') {
                    alert('نظام إدارة الديون غير جاهز. يرجى إعادة تحميل الصفحة.');
                    return;
                }

                // Get data
                let data = [];
                let title = '';

                if (type === 'previous') {
                    data = debtManager.previousDebts || [];
                    title = 'Previous Debts Report';
                } else {
                    data = debtManager.debts || [];
                    title = 'New Invoices Report';
                }

                if (data.length === 0) {
                    alert('لا توجد بيانات للطباعة');
                    return;
                }

                // Create simple PDF directly without recursion
                const doc = new jsPDFConstructor();
                doc.setFontSize(16);
                doc.text(title, 20, 20);

                const now = new Date();
                doc.setFontSize(10);
                doc.text(`Date: ${now.toLocaleDateString()}`, 20, 30);

                let y = 50;
                doc.setFontSize(12);
                data.forEach((item, index) => {
                    if (y > 270) {
                        doc.addPage();
                        y = 20;
                    }
                    const line = `${index + 1}. ${item.invoiceNumber || ''} - ${item.customerName || ''} - ${item.city || ''} - ${(item.amount || 0).toLocaleString()} SAR`;
                    doc.text(line, 20, y);
                    y += 8;
                });

                const fileName = `${title.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;
                doc.save(fileName);
                alert('تم إنشاء ملف PDF بنجاح!');

            } catch (error) {
                console.error('PDF Error:', error);
                alert('حدث خطأ أثناء إنشاء ملف PDF: ' + error.message);
            }
        };

        window.exportCSV = function(type) {
            console.log('🔧 exportCSV called with type:', type);

            try {
                if (typeof window.debtManager === 'undefined') {
                    alert('نظام إدارة الديون غير جاهز. يرجى إعادة تحميل الصفحة.');
                    return;
                }

                // Get data
                let data = [];
                let fileName = '';

                if (type === 'previous') {
                    data = debtManager.previousDebts || [];
                    fileName = 'الديون_السابقة';
                } else {
                    data = debtManager.debts || [];
                    fileName = 'الفواتير_الجديدة';
                }

                if (data.length === 0) {
                    alert('لا توجد بيانات للتصدير');
                    return;
                }

                // Create CSV content
                let csvContent = '\uFEFF'; // UTF-8 BOM
                csvContent += 'رقم الفاتورة,اسم العميل,المدينة,المبلغ,الملاحظات\n';

                // Add data rows
                data.forEach(item => {
                    const row = [
                        `"${(item.invoiceNumber || '').replace(/"/g, '""')}"`,
                        `"${(item.customerName || '').replace(/"/g, '""')}"`,
                        `"${(item.city || '').replace(/"/g, '""')}"`,
                        `"${item.amount || 0}"`,
                        `"${(item.notes || '').replace(/"/g, '""')}"`
                    ];
                    csvContent += row.join(',') + '\n';
                });

                // Create and download file
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);

                link.setAttribute('href', url);
                link.setAttribute('download', `${fileName}_${new Date().toISOString().split('T')[0]}.csv`);
                link.style.visibility = 'hidden';

                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                alert(`تم تصدير ${data.length} سجل بنجاح إلى ملف CSV`);

            } catch (error) {
                console.error('CSV Export Error:', error);
                alert('حدث خطأ أثناء تصدير البيانات إلى CSV');
            }
        };

        window.exportExcel = function(type) {
            console.log('🔧 exportExcel called with type:', type);

            // استخدام وظيفة Excel المحسنة من export-import.js
            if (typeof window.exportToExcel === 'function') {
                window.exportToExcel(type);
            } else {
                console.error('Enhanced Excel function not available');
                alert('وظيفة Excel المحسنة غير متوفرة. يرجى إعادة تحميل الصفحة.');
            }
        };

        window.toggleDropdown = function(dropdownId) {
            console.log('🔧 toggleDropdown called with id:', dropdownId);

            // Close all other dropdowns first
            const allDropdowns = document.querySelectorAll('.dropdown-content');
            allDropdowns.forEach(dropdown => {
                if (dropdown.id !== dropdownId) {
                    dropdown.style.display = 'none';
                }
            });

            // Toggle the requested dropdown
            const dropdown = document.getElementById(dropdownId);
            if (dropdown) {
                dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
            }
        };

        // Enhanced safePrintPDF function with proper Arabic support
        window.safePrintPDF = function(type) {
            console.log('🔧 safePrintPDF called with type:', type);

            // Prevent multiple calls
            if (window.pdfInProgress) {
                console.log('⚠️ PDF generation already in progress');
                alert('جاري إنشاء PDF، يرجى الانتظار...');
                return;
            }

            window.pdfInProgress = true;

            try {
                // Show loading indicator
                if (window.debtManager && debtManager.showSuccess) {
                    debtManager.showSuccess('جاري إنشاء ملف PDF بجودة عالية...');
                }

                // Check if required objects are available
                if (typeof window.jspdf === 'undefined') {
                    console.error('❌ jsPDF library not loaded');
                    alert('مكتبة PDF غير متوفرة. يرجى إعادة تحميل الصفحة.');
                    return;
                }

                if (typeof window.html2canvas === 'undefined') {
                    console.error('❌ html2canvas library not loaded');
                    alert('مكتبة html2canvas غير متوفرة. يرجى إعادة تحميل الصفحة.');
                    return;
                }

                if (typeof window.debtManager === 'undefined') {
                    console.error('❌ debtManager not available');
                    alert('نظام إدارة الديون غير جاهز. يرجى إعادة تحميل الصفحة.');
                    return;
                }

                // Get ALL data based on type (not filtered data)
                let data = [];
                let title = '';

                if (type === 'previous') {
                    data = debtManager.previousDebts || [];
                    title = 'تقرير الديون السابقة';
                    console.log(`📊 طباعة جميع الديون السابقة: ${data.length} دين`);
                } else {
                    data = debtManager.debts || [];
                    title = 'تقرير الفواتير الجديدة';
                    console.log(`📊 طباعة جميع الفواتير الجديدة: ${data.length} فاتورة`);
                }

                if (data.length === 0) {
                    const sectionName = type === 'previous' ? 'الديون السابقة' : 'الفواتير الجديدة';
                    debtManager.showError(`لا توجد بيانات في قسم ${sectionName} للطباعة`);
                    return;
                }

                // Create HTML content for the report with proper Arabic support
                const reportHTML = createArabicReportHTML(data, title, type);

                // Create a temporary div to render the HTML
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = reportHTML;
                tempDiv.style.position = 'absolute';
                tempDiv.style.left = '-9999px';
                tempDiv.style.top = '-9999px';
                tempDiv.style.width = '800px';
                tempDiv.style.backgroundColor = 'white';
                tempDiv.style.padding = '20px';
                tempDiv.style.fontFamily = 'Arial, sans-serif';
                tempDiv.style.direction = 'rtl';

                document.body.appendChild(tempDiv);

                // Use html2canvas to convert HTML to image, then add to PDF
                if (window.html2canvas) {
                    html2canvas(tempDiv, {
                        scale: 2.0,
                        useCORS: true,
                        allowTaint: true,
                        backgroundColor: '#ffffff',
                        width: tempDiv.scrollWidth,
                        height: tempDiv.scrollHeight,
                        scrollX: 0,
                        scrollY: 0,
                        windowWidth: 1000,
                        windowHeight: tempDiv.scrollHeight,
                        removeContainer: true,
                        imageTimeout: 10000,
                        logging: false,
                        foreignObjectRendering: true,
                        onclone: function(clonedDoc) {
                            const style = clonedDoc.createElement('style');
                            style.textContent = `
                                * {
                                    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif !important;
                                    -webkit-font-smoothing: antialiased;
                                    -moz-osx-font-smoothing: grayscale;
                                    text-rendering: optimizeLegibility;
                                    font-feature-settings: "liga", "kern";
                                }
                                table {
                                    border-collapse: collapse !important;
                                    font-variant-numeric: tabular-nums;
                                }
                                th, td {
                                    border: 1px solid #ddd !important;
                                    text-align: center !important;
                                    white-space: nowrap;
                                    overflow: visible;
                                }
                                .arabic-text {
                                    direction: rtl;
                                    unicode-bidi: bidi-override;
                                }
                            `;
                            clonedDoc.head.appendChild(style);
                        }
                    }).then(canvas => {
                        document.body.removeChild(tempDiv);

                        const { jsPDF } = window.jspdf;
                        const pdf = new jsPDF('p', 'mm', 'a4');

                        const imgData = canvas.toDataURL('image/jpeg', 0.98);
                        const imgWidth = 200;
                        const pageHeight = 290;
                        const imgHeight = (canvas.height * imgWidth) / canvas.width;

                        pdf.addImage(imgData, 'JPEG', 5, 5, imgWidth, Math.min(imgHeight, pageHeight));

                        const fileName = `${title.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;
                        pdf.save(fileName);

                        console.log('✅ PDF created successfully:', fileName);

                        if (typeof window.debtManager !== 'undefined' && window.debtManager.showSuccess) {
                            window.debtManager.showSuccess('تم إنشاء ملف PDF بنجاح!');
                        } else {
                            alert('تم إنشاء ملف PDF بنجاح!');
                        }

                    }).catch(error => {
                        document.body.removeChild(tempDiv);
                        console.error('PDF Creation Error:', error);

                        if (typeof window.debtManager !== 'undefined' && window.debtManager.showError) {
                            window.debtManager.showError('حدث خطأ أثناء إنشاء ملف PDF');
                        } else {
                            alert('حدث خطأ أثناء إنشاء ملف PDF: ' + error.message);
                        }
                    });
                }

            } catch (error) {
                console.error('PDF Function Error:', error);

                if (typeof window.debtManager !== 'undefined' && window.debtManager.showError) {
                    window.debtManager.showError('حدث خطأ في وظيفة PDF');
                } else {
                    alert('حدث خطأ في وظيفة PDF: ' + error.message);
                }
            } finally {
                // Reset flag after 3 seconds
                setTimeout(() => {
                    window.pdfInProgress = false;
                }, 3000);
            }
        };

        console.log('✅ All export/import functions loaded and available globally');

        // Add click outside handler for dropdowns
        document.addEventListener('click', function(event) {
            const isDropdownButton = event.target.closest('.dropdown-btn');
            const isDropdownContent = event.target.closest('.dropdown-content');

            if (!isDropdownButton && !isDropdownContent) {
                const allDropdowns = document.querySelectorAll('.dropdown-content');
                allDropdowns.forEach(dropdown => {
                    dropdown.style.display = 'none';
                });
            }
        });
    </script>
</body>
</html>
