#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Database Manager for Customer Debt Management System
مدير قاعدة البيانات لنظام إدارة ديون العملاء
"""

import json
import os
from datetime import datetime
from typing import List, Dict, Optional

class DatabaseManager:
    """Database manager using JSON file storage"""

    def __init__(self, data_file=None):
        if data_file is None:
            # Get the absolute path to the data directory
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_dir = os.path.dirname(current_dir)
            data_dir = os.path.join(project_dir, 'data')
            self.data_file = os.path.join(data_dir, 'customers.json')
        else:
            self.data_file = data_file
        self.data = {'debts': []}
        self.next_id = 1

    def init_database(self):
        """Initialize the database file"""
        try:
            # Create data directory if it doesn't exist
            os.makedirs(os.path.dirname(self.data_file), exist_ok=True)

            # Load existing data or create new file
            if os.path.exists(self.data_file):
                self.load_data()
            else:
                self.save_data()

            print(f"✅ Database initialized: {self.data_file}")
            print(f"📊 Total records: {len(self.data['debts'])}")

        except Exception as e:
            print(f"❌ Error initializing database: {e}")
            raise

    def load_data(self):
        """Load data from JSON file"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                self.data = json.load(f)

            # Ensure data structure
            if 'debts' not in self.data:
                self.data['debts'] = []

            # Set next ID
            if self.data['debts']:
                self.next_id = max(debt.get('id', 0) for debt in self.data['debts']) + 1
            else:
                self.next_id = 1

        except FileNotFoundError:
            self.data = {'debts': []}
            self.next_id = 1
        except json.JSONDecodeError as e:
            print(f"❌ Error reading JSON file: {e}")
            # Backup corrupted file
            backup_file = f"{self.data_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            os.rename(self.data_file, backup_file)
            print(f"📁 Corrupted file backed up to: {backup_file}")
            self.data = {'debts': []}
            self.next_id = 1

    def save_data(self):
        """Save data to JSON file"""
        try:
            # Create backup before saving
            if os.path.exists(self.data_file):
                backup_file = f"{self.data_file}.bak"
                with open(self.data_file, 'r', encoding='utf-8') as src:
                    with open(backup_file, 'w', encoding='utf-8') as dst:
                        dst.write(src.read())

            # Save current data
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"❌ Error saving data: {e}")
            raise

    def add_debt(self, debt_record: Dict) -> int:
        """Add a new debt record"""
        try:
            # Add ID and timestamp
            debt_record['id'] = self.next_id
            debt_record['created_at'] = datetime.now().isoformat()

            # Add to data
            self.data['debts'].append(debt_record)
            self.next_id += 1

            # Save to file
            self.save_data()

            print(f"✅ Added debt record: ID {debt_record['id']}, Invoice {debt_record['invoice_number']}")
            return debt_record['id']

        except Exception as e:
            print(f"❌ Error adding debt record: {e}")
            raise

    def get_all_debts(self) -> List[Dict]:
        """Get all debt records"""
        try:
            # Sort by timestamp (newest first)
            sorted_debts = sorted(
                self.data['debts'],
                key=lambda x: x.get('timestamp', ''),
                reverse=True
            )
            return sorted_debts
        except Exception as e:
            print(f"❌ Error getting all debts: {e}")
            return []

    def get_debt_by_id(self, debt_id: int) -> Optional[Dict]:
        """Get a specific debt record by ID"""
        try:
            for debt in self.data['debts']:
                if debt.get('id') == debt_id:
                    return debt
            return None
        except Exception as e:
            print(f"❌ Error getting debt by ID {debt_id}: {e}")
            return None

    def delete_debt(self, debt_id: int) -> bool:
        """Delete a debt record"""
        try:
            original_count = len(self.data['debts'])
            self.data['debts'] = [debt for debt in self.data['debts'] if debt.get('id') != debt_id]

            if len(self.data['debts']) < original_count:
                self.save_data()
                print(f"✅ Deleted debt record: ID {debt_id}")
                return True
            else:
                print(f"⚠️ Debt record not found: ID {debt_id}")
                return False

        except Exception as e:
            print(f"❌ Error deleting debt record {debt_id}: {e}")
            return False

    def get_customer_debts(self, customer_name: str, city: str) -> List[Dict]:
        """Get all debts for a specific customer"""
        try:
            customer_debts = []
            for debt in self.data['debts']:
                if (debt.get('customer_name', '').lower() == customer_name.lower() and
                    debt.get('city', '').lower() == city.lower()):
                    customer_debts.append(debt)

            # Sort by timestamp (newest first)
            return sorted(customer_debts, key=lambda x: x.get('timestamp', ''), reverse=True)

        except Exception as e:
            print(f"❌ Error getting customer debts: {e}")
            return []

    def search_debts(self, search_term: str) -> List[Dict]:
        """Search debt records"""
        try:
            search_lower = search_term.lower()
            matching_debts = []

            for debt in self.data['debts']:
                # Search in all relevant fields
                if (search_lower in debt.get('invoice_number', '').lower() or
                    search_lower in debt.get('customer_name', '').lower() or
                    search_lower in debt.get('city', '').lower() or
                    search_lower in str(debt.get('amount', '')).lower() or
                    search_lower in debt.get('notes', '').lower() or
                    search_lower in debt.get('date', '').lower()):
                    matching_debts.append(debt)

            # Sort by timestamp (newest first)
            return sorted(matching_debts, key=lambda x: x.get('timestamp', ''), reverse=True)

        except Exception as e:
            print(f"❌ Error searching debts: {e}")
            return []

    def invoice_exists(self, invoice_number: str) -> bool:
        """Check if invoice number already exists"""
        try:
            for debt in self.data['debts']:
                if debt.get('invoice_number', '') == invoice_number:
                    return True
            return False
        except Exception as e:
            print(f"❌ Error checking invoice existence: {e}")
            return False

    def get_statistics(self) -> Dict:
        """Get system statistics"""
        try:
            debts = self.data['debts']

            # Calculate total amount
            total_amount = sum(debt.get('amount', 0) for debt in debts)

            # Count unique customers
            unique_customers = set()
            for debt in debts:
                customer_key = f"{debt.get('customer_name', '').lower()}_{debt.get('city', '').lower()}"
                unique_customers.add(customer_key)

            # Count total invoices
            total_invoices = len(debts)

            # Get top customers by debt amount
            customer_totals = {}
            for debt in debts:
                customer_key = f"{debt.get('customer_name', '')} - {debt.get('city', '')}"
                if customer_key not in customer_totals:
                    customer_totals[customer_key] = 0
                customer_totals[customer_key] += debt.get('amount', 0)

            top_customers = sorted(
                customer_totals.items(),
                key=lambda x: x[1],
                reverse=True
            )[:5]

            return {
                'total_amount': total_amount,
                'total_customers': len(unique_customers),
                'total_invoices': total_invoices,
                'top_customers': top_customers,
                'last_updated': datetime.now().isoformat()
            }

        except Exception as e:
            print(f"❌ Error getting statistics: {e}")
            return {
                'total_amount': 0,
                'total_customers': 0,
                'total_invoices': 0,
                'top_customers': [],
                'last_updated': datetime.now().isoformat()
            }

    def backup_data(self, backup_path: str = None) -> str:
        """Create a backup of the data"""
        try:
            if backup_path is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_path = f"{self.data_file}.backup_{timestamp}"

            with open(self.data_file, 'r', encoding='utf-8') as src:
                with open(backup_path, 'w', encoding='utf-8') as dst:
                    dst.write(src.read())

            print(f"✅ Data backed up to: {backup_path}")
            return backup_path

        except Exception as e:
            print(f"❌ Error creating backup: {e}")
            raise

    def restore_data(self, backup_path: str):
        """Restore data from backup"""
        try:
            if not os.path.exists(backup_path):
                raise FileNotFoundError(f"Backup file not found: {backup_path}")

            # Create current backup before restore
            current_backup = self.backup_data()

            # Restore from backup
            with open(backup_path, 'r', encoding='utf-8') as src:
                with open(self.data_file, 'w', encoding='utf-8') as dst:
                    dst.write(src.read())

            # Reload data
            self.load_data()

            print(f"✅ Data restored from: {backup_path}")
            print(f"📁 Previous data backed up to: {current_backup}")

        except Exception as e:
            print(f"❌ Error restoring data: {e}")
            raise
