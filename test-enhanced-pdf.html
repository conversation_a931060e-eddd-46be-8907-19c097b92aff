<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار PDF المحسن متعدد الصفحات</title>
    <script src="https://unpkg.com/jspdf@latest/dist/jspdf.umd.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 32px;
        }
        .feature-section {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 25px;
            margin: 20px 0;
        }
        .feature-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .test-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 20px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 18px;
            margin: 15px;
            transition: all 0.3s ease;
            width: calc(50% - 30px);
            display: inline-block;
            font-weight: bold;
        }
        .test-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.5);
        }
        .test-btn.primary {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            width: calc(100% - 30px);
        }
        .test-btn.primary:hover {
            box-shadow: 0 8px 25px rgba(39, 174, 96, 0.5);
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: center;
        }
        .comparison-table th {
            background: #2c3e50;
            color: white;
            font-weight: bold;
        }
        .old {
            background: #f8d7da;
            color: #721c24;
        }
        .new {
            background: #d4edda;
            color: #155724;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            direction: ltr;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 اختبار PDF المحسن متعدد الصفحات</h1>
        
        <div class="feature-section">
            <h3>✨ المميزات الجديدة:</h3>
            
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>الميزة</th>
                        <th>النسخة القديمة</th>
                        <th>النسخة المحسنة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>تقسيم الصفحات</strong></td>
                        <td class="old">صفحة واحدة طويلة</td>
                        <td class="new">صفحات متعددة منظمة</td>
                    </tr>
                    <tr>
                        <td><strong>حجم الخط</strong></td>
                        <td class="old">صغير وغير واضح</td>
                        <td class="new">كبير وواضح للقراءة</td>
                    </tr>
                    <tr>
                        <td><strong>التنسيق</strong></td>
                        <td class="old">بسيط وعادي</td>
                        <td class="new">احترافي مع ألوان</td>
                    </tr>
                    <tr>
                        <td><strong>الهيدر والتذييل</strong></td>
                        <td class="old">غير موجود</td>
                        <td class="new">هيدر وتذييل في كل صفحة</td>
                    </tr>
                    <tr>
                        <td><strong>ترقيم الصفحات</strong></td>
                        <td class="old">غير موجود</td>
                        <td class="new">ترقيم تلقائي</td>
                    </tr>
                    <tr>
                        <td><strong>المجموع الكلي</strong></td>
                        <td class="old">في نهاية الجدول</td>
                        <td class="new">في الصفحة الأخيرة</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="feature-section">
            <h3>📊 إعدادات الطباعة المحسنة:</h3>
            <ul>
                <li><strong>عدد الصفوف لكل صفحة:</strong> حساب تلقائي حسب حجم الصفحة</li>
                <li><strong>الهوامش:</strong> 15mm من جميع الجهات</li>
                <li><strong>ارتفاع الصف:</strong> 12mm للوضوح</li>
                <li><strong>ارتفاع الهيدر:</strong> 15mm مع خلفية ملونة</li>
                <li><strong>الألوان:</strong> تناوب ألوان الصفوف للوضوح</li>
                <li><strong>الخطوط:</strong> أحجام مختلفة حسب الأهمية</li>
            </ul>
        </div>

        <button class="test-btn primary" onclick="testEnhancedPDF()">
            🚀 اختبار PDF المحسن مع بيانات تجريبية
        </button>

        <button class="test-btn" onclick="testSmallDataset()">
            📄 اختبار مع بيانات قليلة (صفحة واحدة)
        </button>

        <button class="test-btn" onclick="testLargeDataset()">
            📚 اختبار مع بيانات كثيرة (صفحات متعددة)
        </button>

        <div id="log" class="log" style="display: none;"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            logDiv.style.display = 'block';
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type === 'error' ? 'color: red;' : type === 'success' ? 'color: green;' : '';
            logDiv.innerHTML += `<div style="${colorClass}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            const logDiv = document.getElementById('log');
            logDiv.innerHTML = '';
            logDiv.style.display = 'none';
        }

        // نسخ وظيفة PDF المحسنة من الملف الرئيسي
        function createProfessionalPDFReport(data, title, type) {
            console.log('📄 Creating Professional Multi-Page PDF Report for:', title);

            try {
                if (typeof window.jspdf === 'undefined') {
                    throw new Error('مكتبة jsPDF غير متوفرة');
                }

                const { jsPDF } = window.jspdf;
                const pdf = new jsPDF({
                    orientation: 'portrait',
                    unit: 'mm',
                    format: 'a4',
                    compress: true,
                    precision: 16
                });

                // إعدادات الصفحة
                const pageWidth = pdf.internal.pageSize.getWidth();
                const pageHeight = pdf.internal.pageSize.getHeight();
                const margin = 15;
                const contentWidth = pageWidth - (margin * 2);
                const contentHeight = pageHeight - (margin * 2);

                // إعدادات الجدول
                const rowHeight = 12;
                const headerHeight = 15;
                const rowsPerPage = Math.floor((contentHeight - 80) / rowHeight);

                log(`📊 إعدادات الطباعة: ${rowsPerPage} صف لكل صفحة`);

                let currentPage = 1;
                let currentY = margin;
                let totalAmount = 0;

                // حساب المجموع الكلي
                data.forEach(item => {
                    totalAmount += parseFloat(item.amount) || 0;
                });

                // وظيفة إضافة هيدر الصفحة
                function addPageHeader(pageNum) {
                    pdf.setFillColor(44, 62, 80);
                    pdf.rect(margin, margin, contentWidth, 35, 'F');

                    pdf.setTextColor(255, 255, 255);
                    pdf.setFont('helvetica', 'bold');
                    pdf.setFontSize(24);
                    
                    const titleWidth = pdf.getTextWidth(title);
                    const titleX = (pageWidth - titleWidth) / 2;
                    pdf.text(title, titleX, margin + 20);

                    const currentDate = new Date().toLocaleDateString('ar-SA');
                    pdf.setFontSize(12);
                    pdf.text(`التاريخ: ${currentDate}`, margin + 5, margin + 30);
                    pdf.text(`صفحة ${pageNum}`, pageWidth - margin - 30, margin + 30);

                    return margin + 45;
                }

                // وظيفة إضافة هيدر الجدول
                function addTableHeader(startY) {
                    const colWidths = [25, 45, 30, 30, 40];
                    let currentX = margin;

                    pdf.setFillColor(52, 73, 94);
                    pdf.rect(margin, startY, contentWidth, headerHeight, 'F');

                    pdf.setTextColor(255, 255, 255);
                    pdf.setFont('helvetica', 'bold');
                    pdf.setFontSize(14);

                    const headers = ['رقم الفاتورة', 'اسم العميل', 'المدينة', 'المبلغ (ر.س)', 'الملاحظات'];
                    
                    headers.forEach((header, index) => {
                        const headerX = currentX + (colWidths[index] / 2);
                        pdf.text(header, headerX, startY + 10, { align: 'center' });
                        currentX += colWidths[index];
                    });

                    // حدود الجدول
                    pdf.setDrawColor(44, 62, 80);
                    pdf.setLineWidth(0.5);
                    
                    currentX = margin;
                    headers.forEach((_, index) => {
                        pdf.line(currentX, startY, currentX, startY + headerHeight);
                        currentX += colWidths[index];
                    });
                    pdf.line(currentX, startY, currentX, startY + headerHeight);
                    pdf.line(margin, startY, margin + contentWidth, startY);
                    pdf.line(margin, startY + headerHeight, margin + contentWidth, startY + headerHeight);

                    return startY + headerHeight;
                }

                // وظيفة إضافة صف بيانات
                function addDataRow(item, startY, rowIndex) {
                    const colWidths = [25, 45, 30, 30, 40];
                    let currentX = margin;

                    const rowColor = rowIndex % 2 === 0 ? [255, 255, 255] : [248, 249, 250];
                    pdf.setFillColor(...rowColor);
                    pdf.rect(margin, startY, contentWidth, rowHeight, 'F');

                    pdf.setTextColor(44, 62, 80);
                    pdf.setFont('helvetica', 'normal');
                    pdf.setFontSize(11);

                    const rowData = [
                        item.invoiceNumber || '',
                        item.customerName || '',
                        item.city || '',
                        (parseFloat(item.amount) || 0).toLocaleString('ar-SA'),
                        item.notes || ''
                    ];

                    rowData.forEach((data, index) => {
                        const cellX = currentX + (colWidths[index] / 2);
                        
                        let displayText = String(data);
                        const maxWidth = colWidths[index] - 4;
                        
                        while (pdf.getTextWidth(displayText) > maxWidth && displayText.length > 3) {
                            displayText = displayText.substring(0, displayText.length - 4) + '...';
                        }
                        
                        pdf.text(displayText, cellX, startY + 8, { align: 'center' });
                        currentX += colWidths[index];
                    });

                    // حدود الصف
                    pdf.setDrawColor(222, 226, 230);
                    pdf.setLineWidth(0.3);
                    
                    currentX = margin;
                    colWidths.forEach(width => {
                        pdf.line(currentX, startY, currentX, startY + rowHeight);
                        currentX += width;
                    });
                    pdf.line(currentX, startY, currentX, startY + rowHeight);
                    pdf.line(margin, startY + rowHeight, margin + contentWidth, startY + rowHeight);

                    return startY + rowHeight;
                }

                // وظيفة إضافة تذييل الصفحة
                function addPageFooter(pageNum, isLastPage = false) {
                    const footerY = pageHeight - 20;
                    
                    pdf.setTextColor(108, 117, 125);
                    pdf.setFont('helvetica', 'normal');
                    pdf.setFontSize(10);
                    
                    pdf.text('نظام إدارة ديون العملاء', margin, footerY);
                    
                    if (isLastPage) {
                        pdf.setFont('helvetica', 'bold');
                        pdf.setFontSize(12);
                        pdf.setTextColor(44, 62, 80);
                        const totalText = `إجمالي المبلغ: ${totalAmount.toLocaleString('ar-SA')} ر.س`;
                        const totalWidth = pdf.getTextWidth(totalText);
                        pdf.text(totalText, (pageWidth - totalWidth) / 2, footerY - 10);
                    }
                }

                // بدء إنشاء الصفحات
                log(`📄 بدء إنشاء ${Math.ceil(data.length / rowsPerPage)} صفحة...`);

                for (let i = 0; i < data.length; i += rowsPerPage) {
                    if (currentPage > 1) {
                        pdf.addPage();
                    }

                    log(`📄 إنشاء الصفحة ${currentPage}...`);

                    currentY = addPageHeader(currentPage);
                    currentY = addTableHeader(currentY);

                    const pageData = data.slice(i, i + rowsPerPage);
                    pageData.forEach((item, index) => {
                        currentY = addDataRow(item, currentY, i + index);
                    });

                    const isLastPage = (i + rowsPerPage) >= data.length;
                    addPageFooter(currentPage, isLastPage);

                    currentPage++;
                }

                const fileName = `${title.replace(/\s+/g, '_')}_محسن_${new Date().toISOString().split('T')[0]}.pdf`;
                pdf.save(fileName);

                log(`✅ تم إنشاء PDF محسن: ${fileName}`, 'success');
                
                return {
                    success: true,
                    fileName: fileName,
                    pages: currentPage - 1,
                    records: data.length
                };

            } catch (error) {
                log(`❌ خطأ في إنشاء PDF المحسن: ${error.message}`, 'error');
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        // بيانات تجريبية
        function generateTestData(count) {
            const names = ['أحمد محمد', 'فاطمة علي', 'محمد سالم', 'نورا أحمد', 'خالد عبدالله', 'سارة محمود', 'عمر حسن', 'ليلى يوسف'];
            const cities = ['الرياض', 'جدة', 'الدمام', 'مكة المكرمة', 'المدينة المنورة', 'الطائف', 'أبها', 'تبوك'];
            const notes = ['ملاحظة تجريبية', 'معلومات إضافية', 'تفاصيل مهمة', 'بيانات شاملة', 'ملاحظة مفصلة'];
            
            const data = [];
            for (let i = 1; i <= count; i++) {
                data.push({
                    invoiceNumber: String(i).padStart(3, '0'),
                    customerName: names[Math.floor(Math.random() * names.length)],
                    city: cities[Math.floor(Math.random() * cities.length)],
                    amount: Math.floor(Math.random() * 5000) + 500,
                    notes: notes[Math.floor(Math.random() * notes.length)]
                });
            }
            return data;
        }

        function testEnhancedPDF() {
            clearLog();
            log('🚀 بدء اختبار PDF المحسن مع بيانات متوسطة...', 'info');
            
            const data = generateTestData(25);
            const result = createProfessionalPDFReport(data, 'تقرير اختبار PDF المحسن', 'test');
            
            if (result.success) {
                log(`🎉 نجح الاختبار! تم إنشاء ${result.pages} صفحة مع ${result.records} سجل`, 'success');
                alert(`✅ نجح الاختبار!\n\n📁 الملف: ${result.fileName}\n📄 عدد الصفحات: ${result.pages}\n📊 عدد السجلات: ${result.records}\n\n🔍 افتح الملف وتحقق من:\n• وضوح الخطوط\n• تنسيق الألوان\n• ترقيم الصفحات\n• المجموع الكلي`);
            } else {
                log(`❌ فشل الاختبار: ${result.error}`, 'error');
                alert(`❌ فشل الاختبار: ${result.error}`);
            }
        }

        function testSmallDataset() {
            clearLog();
            log('📄 اختبار مع بيانات قليلة (صفحة واحدة)...', 'info');
            
            const data = generateTestData(5);
            const result = createProfessionalPDFReport(data, 'تقرير صفحة واحدة', 'small');
            
            if (result.success) {
                log(`✅ تم إنشاء PDF بصفحة واحدة بنجاح`, 'success');
                alert(`✅ تم إنشاء PDF بصفحة واحدة!\n\nالملف: ${result.fileName}`);
            }
        }

        function testLargeDataset() {
            clearLog();
            log('📚 اختبار مع بيانات كثيرة (صفحات متعددة)...', 'info');
            
            const data = generateTestData(100);
            const result = createProfessionalPDFReport(data, 'تقرير صفحات متعددة', 'large');
            
            if (result.success) {
                log(`✅ تم إنشاء PDF بـ ${result.pages} صفحة بنجاح`, 'success');
                alert(`✅ تم إنشاء PDF متعدد الصفحات!\n\n📁 الملف: ${result.fileName}\n📄 عدد الصفحات: ${result.pages}\n📊 عدد السجلات: ${result.records}`);
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('🔥 صفحة اختبار PDF المحسن جاهزة', 'success');
            log('📋 اختبر المميزات الجديدة وقارن مع النسخة القديمة', 'info');
        });
    </script>
</body>
</html>
