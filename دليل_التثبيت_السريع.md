# 🚀 دليل التثبيت السريع - Windows

## 🎯 طرق التثبيت (اختر واحدة)

---

### **⚡ الطريقة الأسرع - تشغيل مباشر**

#### **الخطوات:**
```
1. تحميل Node.js من: https://nodejs.org/
2. تثبيت Node.js وإعادة تشغيل الكمبيوتر
3. انقر مرتين على: install-windows.bat
4. اختر "1" للتشغيل المباشر
5. انتظر حتى يفتح التطبيق
```

#### **المميزات:**
- ✅ سريع وبسيط
- ✅ لا يحتاج مثبت
- ✅ يعمل فوراً

---

### **📦 الطريقة الاحترافية - مثبت Windows**

#### **الخطوات:**
```
1. تحميل Node.js من: https://nodejs.org/
2. تثبيت Node.js وإعادة تشغيل الكمبيوتر
3. انقر مرتين على: install-windows.bat
4. اختر "2" لبناء مثبت Windows
5. انتظر حتى ينتهي البناء (5-10 دقائق)
6. اذهب لمجلد dist/ وشغل ملف Setup.exe
7. اتبع خطوات التثبيت
```

#### **المميزات:**
- ✅ مثبت احترافي
- ✅ أيقونة على سطح المكتب
- ✅ إمكانية الإزالة من لوحة التحكم
- ✅ تكامل مع النظام

---

### **💾 الطريقة المحمولة - بدون تثبيت**

#### **الخطوات:**
```
1. تحميل Node.js من: https://nodejs.org/
2. تثبيت Node.js وإعادة تشغيل الكمبيوتر
3. انقر مرتين على: install-windows.bat
4. اختر "3" لبناء نسخة محمولة
5. انتظر حتى ينتهي البناء
6. انسخ مجلد win-unpacked إلى أي مكان
7. شغل customer-debt-manager.exe من المجلد
```

#### **المميزات:**
- ✅ لا يحتاج تثبيت
- ✅ يمكن نقله لأي جهاز
- ✅ يعمل من فلاشة USB

---

## 🛠️ حل المشاكل الشائعة

### **❌ مشكلة: "Node.js غير مثبت"**
```
الحل:
1. اذهب إلى: https://nodejs.org/
2. حمل النسخة LTS (الموصى بها)
3. شغل الملف وثبت Node.js
4. أعد تشغيل الكمبيوتر
5. جرب مرة أخرى
```

### **❌ مشكلة: "فشل في تثبيت المتطلبات"**
```
الحل:
1. تأكد من اتصال الإنترنت
2. شغل install-windows.bat كمدير:
   - انقر بالزر الأيمن على الملف
   - اختر "تشغيل كمدير"
3. اختر "4" لتثبيت المتطلبات فقط
4. انتظر حتى ينتهي
5. جرب التشغيل مرة أخرى
```

### **❌ مشكلة: "الأيقونة لا تظهر"**
```
الحل:
1. افتح create-default-icon.html في المتصفح
2. حمل الأيقونات بأحجام مختلفة
3. ضعها في مجلد build/
4. أعد بناء التطبيق
```

### **❌ مشكلة: "التطبيق لا يفتح"**
```
الحل:
1. تأكد من إغلاق برامج الحماية مؤقتاً
2. شغل كمدير
3. تأكد من وجود مساحة كافية (500 MB على الأقل)
4. أعد تشغيل الكمبيوتر وحاول مرة أخرى
```

---

## 📋 متطلبات النظام

### **الحد الأدنى:**
- Windows 7 أو أحدث
- 2 GB RAM
- 500 MB مساحة فارغة
- اتصال إنترنت (للتثبيت الأولي فقط)

### **الموصى به:**
- Windows 10 أو أحدث
- 4 GB RAM
- 1 GB مساحة فارغة
- اتصال إنترنت سريع

---

## 🎯 اختبار التثبيت

### **بعد التثبيت، تأكد من:**
- [ ] التطبيق يفتح بدون أخطاء
- [ ] يمكن إضافة دين جديد
- [ ] البيانات تُحفظ وتُحمل بشكل صحيح
- [ ] الإحصائيات تظهر بشكل صحيح
- [ ] يمكن تصدير البيانات (CSV/Excel)
- [ ] التطبيق يعمل بدون إنترنت

---

## 📞 الدعم

### **إذا واجهت مشاكل:**
1. **راجع قسم حل المشاكل** أعلاه
2. **تحقق من ملف السجل** في مجلد التطبيق
3. **جرب التشغيل كمدير**
4. **تأكد من تحديث Windows**

### **معلومات مفيدة للدعم:**
- إصدار Windows
- رسالة الخطأ (إن وجدت)
- خطوات إعادة إنتاج المشكلة
- لقطة شاشة للخطأ

---

## 🎉 بعد التثبيت الناجح

### **ستجد:**
- ✅ أيقونة على سطح المكتب
- ✅ التطبيق في قائمة ابدأ
- ✅ إمكانية الإزالة من لوحة التحكم
- ✅ التطبيق يعمل بدون إنترنت

### **يمكنك:**
- 📊 إدارة ديون العملاء
- 📈 عرض الإحصائيات والتحليلات
- 🔍 البحث والفرز المتقدم
- 📄 تصدير التقارير
- 💾 حفظ البيانات محلياً

---

## 🚀 نصائح للاستخدام الأمثل

### **للأداء الأفضل:**
- أغلق البرامج غير الضرورية
- تأكد من وجود مساحة كافية
- اعمل نسخة احتياطية من البيانات بانتظام

### **للأمان:**
- صدر البيانات بانتظام
- احفظ نسخة من مجلد التطبيق
- لا تحذف مجلد البيانات

**🎯 الهدف: تطبيق يعمل بسلاسة على Windows!**
